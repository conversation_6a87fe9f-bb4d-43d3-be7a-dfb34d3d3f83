# Dependencies
node_modules/
server/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
dist/
build/
.next/
out/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
docs/

# Testing
coverage/
.nyc_output/
test/
tests/
__tests__/
*.test.js
*.spec.js

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Temporary folders
tmp/
temp/

# Docker files (don't include in image)
Dockerfile*
docker-compose*.yml
.dockerignore

# Scripts (may contain sensitive info)
scripts/

# Backups
backups/

# Development files
.env.example
.env.frontend.example
