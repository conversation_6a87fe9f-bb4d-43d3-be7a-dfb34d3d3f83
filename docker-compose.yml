version: '3.8'

services:
  # Main application (unified full-stack)
  app:
    build: .
    container_name: karmod-hub
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET:-your-super-secure-jwt-secret-change-this}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET:-your-super-secure-refresh-secret-change-this}
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:3001}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:3001}
    # No database dependency - using external PostgreSQL
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - karmod-network

  # Note: Using external PostgreSQL database via DATABASE_URL
  # No local database services needed

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: karmod-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - karmod-network

volumes:
  redis_data:
    driver: local

networks:
  karmod-network:
    driver: bridge
