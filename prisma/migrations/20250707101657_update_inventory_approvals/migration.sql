-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "InventoryRequestStatus" ADD VALUE 'approved_by_store_pending_release';
ALTER TYPE "InventoryRequestStatus" ADD VALUE 'partially_approved_by_store';
ALTER TYPE "InventoryRequestStatus" ADD VALUE 'rejected_by_store';
ALTER TYPE "InventoryRequestStatus" ADD VALUE 'pending_admin_approval';
ALTER TYPE "InventoryRequestStatus" ADD VALUE 'approved_by_admin_pending_store_release';
ALTER TYPE "InventoryRequestStatus" ADD VALUE 'revision_requested_by_admin';
ALTER TYPE "InventoryRequestStatus" ADD VALUE 'rejected_by_admin';

-- AlterTable
ALTER TABLE "inventory_requests" ADD COLUMN     "adminNotes" TEXT,
ADD COLUMN     "projectLeadNotes" TEXT;
