/*
  Warnings:

  - The values [pending_approval,approved_by_lead,approved_by_admin] on the enum `BudgetStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "BudgetStatus_new" AS ENUM ('draft', 'pending_accountant', 'pending_admin', 'approved', 'revision_needed', 'rejected');
ALTER TABLE "budgets" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "budgets" ALTER COLUMN "status" TYPE "BudgetStatus_new" USING ("status"::text::"BudgetStatus_new");
ALTER TYPE "BudgetStatus" RENAME TO "BudgetStatus_old";
ALTER TYPE "BudgetStatus_new" RENAME TO "BudgetStatus";
DROP TYPE "BudgetStatus_old";
ALTER TABLE "budgets" ALTER COLUMN "status" SET DEFAULT 'draft';
COMMIT;
