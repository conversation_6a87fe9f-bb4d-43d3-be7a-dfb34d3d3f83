const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Hash passwords with lower cost for development
  console.log('🔐 Hashing passwords...');
  const saltRounds = 8; // Lower for faster seeding

  // Create initial users
  const users = [
    {
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123', saltRounds),
      role: 'admin',
      name: 'Admin User',
      phone: '08012345678',
      location: 'Lagos',
      company: 'Karmod Nigeria HQ'
    }
    // {
    //   email: '<EMAIL>',
    //   password: await bcrypt.hash('lead123', saltRounds),
    //   role: 'project_lead',
    //   name: 'Project Lead Alpha',
    //   phone: '08023456789',
    //   location: 'Abuja',
    //   company: 'Karmod Nigeria'
    // },
    // {
    //   email: '<EMAIL>',
    //   password: await bcrypt.hash('eng123', saltRounds),
    //   role: 'team_member',
    //   name: 'Site Engineer Bravo',
    //   phone: '***********',
    //   location: 'Port Harcourt',
    //   company: 'Karmod Nigeria'
    // },
    // {
    //   email: '<EMAIL>',
    //   password: await bcrypt.hash('client123', saltRounds),
    //   role: 'client',
    //   name: 'Client User Charlie',
    //   phone: '***********',
    //   location: 'Kano',
    //   company: 'TechCorp Nigeria'
    // },
    // {
    //   email: '<EMAIL>',
    //   password: await bcrypt.hash('acc123', saltRounds),
    //   role: 'accountant',
    //   name: 'Accountant Delta',
    //   phone: '***********',
    //   location: 'Lagos',
    //   company: 'Karmod Nigeria HQ'
    // },
    // {
    //   email: '<EMAIL>',
    //   password: await bcrypt.hash('store123', saltRounds),
    //   role: 'store_keeper',
    //   name: 'Store Keeper Zulu',
    //   phone: '***********',
    //   location: 'Lagos Warehouse',
    //   company: 'Karmod Nigeria HQ'
    // },
    // {
    //   email: '<EMAIL>',
    //   password: await bcrypt.hash('qs123', saltRounds),
    //   role: 'quantity_surveyor',
    //   name: 'Quantity Surveyor Lima',
    //   phone: '***********',
    //   location: 'Lagos',
    //   company: 'Karmod Nigeria HQ'
    // }
  ];

  console.log('👥 Creating users...');
  const createdUsers = [];
  for (const userData of users) {
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: userData
    });
    createdUsers.push(user);
    console.log(`✅ Created user: ${user.name} (${user.email})`);
  }

  // Create sample projects
  console.log('🏗️ Creating sample projects...');
  
  const projectLead = createdUsers.find(u => u.role === 'project_lead');
  const client = createdUsers.find(u => u.role === 'client');
  const teamMember = createdUsers.find(u => u.role === 'team_member');

  // const project1 = await prisma.project.upsert({
  //   where: { id: 'KM-2024-001' },
  //   update: {},
  //   create: {
  //     id: 'KM-2024-001',
  //     title: 'Lagos Office Complex',
  //     description: 'Modern prefabricated office building with 20 units',
  //     location: 'Victoria Island, Lagos',
  //     category: 'Commercial',
  //     status: 'in_progress',
  //     startDate: new Date('2024-01-15'),
  //     endDate: new Date('2024-06-30'),
  //     budget: 15000000,
  //     spent: 8500000,
  //     progress: 65,
  //     projectLeadId: projectLead.id,
  //     clientId: client.id,
  //     phases: {
  //       create: [
  //         {
  //           name: 'Foundation',
  //           customLabel: 'Foundation Works',
  //           status: 'completed',
  //           progress: 100,
  //           tasks: {
  //             create: [
  //               {
  //                 name: 'Clear Site',
  //                 assignedTo: teamMember.name,
  //                 progress: 100,
  //                 notes: 'Site cleared and leveled.'
  //               }
  //             ]
  //           }
  //         },
  //         {
  //           name: 'Structure',
  //           customLabel: 'Steel Framework',
  //           status: 'in_progress',
  //           progress: 70,
  //           tasks: {
  //             create: [
  //               {
  //                 name: 'Install Steel Frame',
  //                 assignedTo: teamMember.name,
  //                 progress: 80,
  //                 notes: 'Main framework 80% complete.'
  //               }
  //             ]
  //           }
  //         },
  //         {
  //           name: 'Finishing',
  //           customLabel: 'Interior & Exterior',
  //           status: 'not_started',
  //           progress: 0
  //         }
  //       ]
  //     },
  //     teamMembers: {
  //       create: [
  //         { userId: teamMember.id }
  //       ]
  //     }
  //   }
  // });

  // console.log(`✅ Created project: ${project1.title}`);

  // Create sample inventory items
  console.log('📦 Creating inventory items...');
  
  const inventoryItems = [
    {
      name: 'Cement Bags (50kg)',
      category: 'Building Materials',
      quantity: 200,
      unit: 'bags',
      lowStockThreshold: 50
    },
    {
      name: 'Steel Rods (12mm)',
      category: 'Metals',
      quantity: 500,
      unit: 'lengths',
      lowStockThreshold: 100
    },
    {
      name: 'Paint (White Emulsion)',
      category: 'Finishing',
      quantity: 50,
      unit: 'gallons',
      lowStockThreshold: 10
    },
    {
      name: 'Safety Helmets',
      category: 'Safety Gear',
      quantity: 30,
      unit: 'pieces',
      lowStockThreshold: 5
    }
  ];

  // for (const item of inventoryItems) {
  //   const inventoryItem = await prisma.inventoryItem.create({
  //     data: item
  //   });
  //   console.log(`✅ Created inventory item: ${inventoryItem.name}`);
  // }

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
