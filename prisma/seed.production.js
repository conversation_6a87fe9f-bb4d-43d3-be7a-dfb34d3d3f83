/**
 * Production Database Seeding Script
 * Seeds the database with minimal essential data for production
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting production database seeding...');

  try {
    // Create admin user if it doesn't exist
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';

    const existingAdmin = await prisma.user.findUnique({
      where: { email: adminEmail }
    });

    if (!existingAdmin) {
      const hashedPassword = await bcrypt.hash(adminPassword, 12);
      
      const adminUser = await prisma.user.create({
        data: {
          email: adminEmail,
          password: hashedPassword,
          name: 'System Administrator',
          role: 'admin',
          phone: process.env.ADMIN_PHONE || '+234-xxx-xxx-xxxx',
          location: 'Lagos, Nigeria',
          company: 'Karmod Nigeria HQ'
        }
      });

      console.log('✅ Admin user created:', adminUser.email);
    } else {
      console.log('ℹ️  Admin user already exists:', existingAdmin.email);
    }

    // Create default inventory categories if they don't exist
    const defaultInventoryItems = [
      {
        name: 'Cement',
        category: 'Building Materials',
        quantity: 0,
        unit: 'bags',
        lowStockThreshold: 50
      },
      {
        name: 'Steel Rods',
        category: 'Building Materials', 
        quantity: 0,
        unit: 'tons',
        lowStockThreshold: 5
      },
      {
        name: 'Sand',
        category: 'Building Materials',
        quantity: 0,
        unit: 'cubic meters',
        lowStockThreshold: 10
      },
      {
        name: 'Gravel',
        category: 'Building Materials',
        quantity: 0,
        unit: 'cubic meters',
        lowStockThreshold: 10
      },
      {
        name: 'Safety Helmets',
        category: 'Safety Equipment',
        quantity: 0,
        unit: 'pieces',
        lowStockThreshold: 20
      },
      {
        name: 'Safety Vests',
        category: 'Safety Equipment',
        quantity: 0,
        unit: 'pieces',
        lowStockThreshold: 20
      }
    ];

    for (const item of defaultInventoryItems) {
      const existing = await prisma.inventoryItem.findFirst({
        where: { name: item.name }
      });

      if (!existing) {
        await prisma.inventoryItem.create({ data: item });
        console.log(`✅ Created inventory item: ${item.name}`);
      }
    }

    console.log('✅ Production database seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error during production seeding:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
