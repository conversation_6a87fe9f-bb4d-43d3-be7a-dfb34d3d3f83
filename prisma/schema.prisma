// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums (PostgreSQL supports these)
enum UserRole {
  admin
  project_manager
  project_lead
  team_member
  client
  accountant
  store_keeper
  quantity_surveyor
}

enum ProjectStatus {
  not_started
  in_progress
  completed
  on_hold
}

enum PhaseStatus {
  not_started
  in_progress
  completed
  on_hold
}

enum BudgetStatus {
  draft
  pending_accountant
  pending_admin
  approved
  revision_needed
  rejected
}

enum MaterialRequestStatus {
  pending_lead_approval
  approved_by_lead
  rejected_by_lead
  pending_store_approval
  approved_by_store
  rejected_by_store
  delivered
}

enum InventoryRequestStatus {
  pending_store_keeper_approval
  approved_by_store_pending_release
  partially_approved_by_store
  rejected_by_store
  pending_admin_approval
  approved_by_admin_pending_store_release
  revision_requested_by_admin
  rejected_by_admin
  approved
  rejected
}

// Models
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  password  String
  role      UserRole @default(team_member)
  name      String
  phone     String?
  location  String?
  company   String?
  bio       String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  projectsLed          Project[]           @relation("ProjectLead")
  projectTeamMembers   ProjectTeamMember[]
  clientProjects       Project[]           @relation("ProjectClient")
  dailyLogs            DailyLog[]
  dailyLogReplies      DailyLogReply[]
  budgets              Budget[]
  materialRequests     MaterialRequest[]
  inventoryRequests    InventoryRequest[]
  mediaFiles           MediaFile[]
  notifications        Notification[]
  sentNotifications    Notification[]      @relation("NotificationSender")
  notificationSettings NotificationSettings?
  createdSystemSettings SystemSettings[]   @relation("SystemSettingsCreator")
  updatedSystemSettings SystemSettings[]   @relation("SystemSettingsUpdater")

  @@map("users")
}

model Project {
  id          String        @id
  title       String
  description String?
  location    String?
  category    String?
  status      ProjectStatus @default(not_started)
  startDate   DateTime?
  endDate     DateTime?
  budget      Float?
  spent       Float?        @default(0)
  progress    Int?          @default(0)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  projectLead   User   @relation("ProjectLead", fields: [projectLeadId], references: [id])
  projectLeadId Int
  client        User?  @relation("ProjectClient", fields: [clientId], references: [id])
  clientId      Int?

  phases               ProjectPhase[]
  teamMembers          ProjectTeamMember[]
  dailyLogs            DailyLog[]
  budgets              Budget[]
  materialRequests     MaterialRequest[]
  inventoryRequests    InventoryRequest[]
  mediaFiles           MediaFile[]
  projectFinancials    ProjectFinancial?

  @@map("projects")
}

model ProjectTeamMember {
  id        Int      @id @default(autoincrement())
  projectId String
  userId    Int
  createdAt DateTime @default(now())

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, userId])
  @@map("project_team_members")
}

model ProjectPhase {
  id           Int         @id @default(autoincrement())
  projectId    String
  name         String
  customLabel  String?
  status       PhaseStatus @default(not_started)
  progress     Int         @default(0)
  startDate    DateTime?
  endDate      DateTime?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Relations
  project Project     @relation(fields: [projectId], references: [id], onDelete: Cascade)
  tasks   PhaseTask[]

  @@map("project_phases")
}

model PhaseTask {
  id          Int       @id @default(autoincrement())
  phaseId     Int
  name        String
  assignedTo  String?
  progress    Int       @default(0)
  notes       String?
  startDate   DateTime?
  endDate     DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  phase ProjectPhase @relation(fields: [phaseId], references: [id], onDelete: Cascade)

  @@map("phase_tasks")
}

model ProjectFinancial {
  id                  Int      @id @default(autoincrement())
  projectId           String   @unique
  totalProjectValue   Float    @default(0)
  initialPayment      Float    @default(0)
  outstandingBalance  Float    @default(0)
  installments        Json?    // Array of installment objects
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("project_financials")
}

model DailyLog {
  id        Int      @id @default(autoincrement())
  projectId String
  userId    Int
  title     String?  // Optional title field
  message   String
  type      String   @default("update") // update, issue, milestone, note
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  project     Project         @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user        User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  replies     DailyLogReply[]
  attachments Attachment[]

  @@map("daily_logs")
}

model DailyLogReply {
  id          Int      @id @default(autoincrement())
  dailyLogId  Int
  userId      Int
  message     String
  createdAt   DateTime @default(now())

  // Relations
  dailyLog    DailyLog     @relation(fields: [dailyLogId], references: [id], onDelete: Cascade)
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  attachments Attachment[]

  @@map("daily_log_replies")
}

model Attachment {
  id          Int      @id @default(autoincrement())
  filename    String
  originalName String
  mimeType    String
  size        Int
  path        String
  createdAt   DateTime @default(now())

  // Relations - can be attached to daily logs or replies
  dailyLogId      Int?
  dailyLogReplyId Int?

  dailyLog      DailyLog?      @relation(fields: [dailyLogId], references: [id], onDelete: Cascade)
  dailyLogReply DailyLogReply? @relation(fields: [dailyLogReplyId], references: [id], onDelete: Cascade)

  @@map("attachments")
}

model Budget {
  id                    Int          @id @default(autoincrement())
  projectId             String
  submittedBy           Int
  status                BudgetStatus @default(draft)
  totalAmount           Float        @default(0)
  remarks               String?
  pushedByAccountant    Boolean      @default(false)
  createdAt             DateTime     @default(now())
  updatedAt             DateTime     @updatedAt

  // Relations
  project Project      @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User         @relation(fields: [submittedBy], references: [id], onDelete: Cascade)
  items   BudgetItem[]

  @@map("budgets")
}

model BudgetItem {
  id          Int     @id @default(autoincrement())
  budgetId    Int
  description String
  quantity    Int
  unitPrice   Float
  total       Float
  notes       String?

  // Relations
  budget Budget @relation(fields: [budgetId], references: [id], onDelete: Cascade)

  @@map("budget_items")
}

model MaterialRequest {
  id                      Int                   @id @default(autoincrement())
  projectId               String
  requestedBy             Int
  items                   Json                  // Array of requested items
  notes                   String?
  status                  MaterialRequestStatus @default(pending_lead_approval)
  leadApprovalTimestamp   DateTime?
  storeKeeperNotes        String?
  storeStatus             String?
  storeTimestamp          DateTime?
  createdAt               DateTime              @default(now())
  updatedAt               DateTime              @updatedAt

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [requestedBy], references: [id], onDelete: Cascade)

  @@map("material_requests")
}

model InventoryItem {
  id                 Int      @id @default(autoincrement())
  name               String
  category           String
  quantity           Int
  unit               String
  lowStockThreshold  Int      @default(10)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // Relations
  inventoryRequests InventoryRequest[]

  @@map("inventory_items")
}

model InventoryRequest {
  id                    Int                    @id @default(autoincrement())
  inventoryItemId       Int
  projectId             String
  requestedBy           Int
  quantityRequested     Int
  quantityApproved      Int?
  status                InventoryRequestStatus @default(pending_store_keeper_approval)
  notes                 String?
  storeKeeperNotes      String?
  adminNotes            String?
  projectLeadNotes      String?
  lastActionTimestamp   DateTime?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt

  // Relations
  inventoryItem InventoryItem @relation(fields: [inventoryItemId], references: [id], onDelete: Cascade)
  project       Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user          User          @relation(fields: [requestedBy], references: [id], onDelete: Cascade)

  @@map("inventory_requests")
}

model MediaFile {
  id          Int      @id @default(autoincrement())
  projectId   String?
  uploadedBy  Int
  filename    String
  originalName String
  mimeType    String
  size        Int
  path        String
  description String?
  createdAt   DateTime @default(now())

  // Relations
  project Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User     @relation(fields: [uploadedBy], references: [id], onDelete: Cascade)

  @@map("media_files")
}

model Notification {
  id         Int      @id @default(autoincrement())
  userId     Int
  senderId   Int?
  title      String
  message    String
  type       String   @default("info") // info, success, warning, error
  isRead     Boolean  @default(false)
  data       Json?    // Additional notification data
  createdAt  DateTime @default(now())

  // Relations
  user   User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  sender User? @relation("NotificationSender", fields: [senderId], references: [id], onDelete: SetNull)

  @@map("notifications")
}

model NotificationSettings {
  id                  Int      @id @default(autoincrement())
  userId              Int      @unique
  emailNotifications  Boolean  @default(true)
  pushNotifications   Boolean  @default(false)
  projectUpdates      Boolean  @default(true)
  dailyDigest         Boolean  @default(false)
  weeklyReport        Boolean  @default(true)
  systemAlerts        Boolean  @default(true)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notification_settings")
}

model SystemSettings {
  id                Int      @id @default(autoincrement())
  key               String   @unique
  value             String?
  encrypted         Boolean  @default(false)
  description       String?
  category          String   @default("general")
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  createdBy         Int?
  updatedBy         Int?

  creator User? @relation("SystemSettingsCreator", fields: [createdBy], references: [id])
  updater User? @relation("SystemSettingsUpdater", fields: [updatedBy], references: [id])

  @@map("system_settings")
}

model Testimonial {
  id        Int      @id @default(autoincrement())
  projectId String
  clientName String
  content   String
  rating    Int?     @default(5)
  isPublic  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("testimonials")
}
