/**
 * Simplified ZeptoMail Service
 * A clean, simple implementation for sending emails via ZeptoMail API
 * This can be used as an alternative to the main EmailService class
 */

// Environment configuration helper
const getEnvConfig = () => {
  // Support both naming conventions for environment variables
  return {
    apiKey: process.env.ZEPTOMAIL_API_KEY || process.env.ZEPTO_API_KEY,
    fromEmail: process.env.ZEPTOMAIL_FROM_EMAIL || process.env.ZEPTO_FROM_EMAIL || '<EMAIL>',
    fromName: process.env.ZEPTOMAIL_FROM_NAME || 'Karmod Project Hub',
    bounceAddress: process.env.ZEPTOMAIL_BOUNCE_ADDRESS || process.env.ZEPTO_BOUNCE_ADDRESS
  };
};

/**
 * @typedef {Object} SendEmailParams
 * @property {string|string[]} to - Recipient email address(es)
 * @property {string} subject - Email subject
 * @property {string} htmlBody - HTML content of the email
 * @property {Object} [from] - Sender information
 * @property {string} [from.email] - Sender email address
 * @property {string} [from.name] - Sender name
 * @property {boolean} [trackClicks=true] - Enable click tracking
 * @property {boolean} [trackOpens=true] - Enable open tracking
 */

const zeptoMailService = {
  /**
   * Send email using ZeptoMail API
   * @param {SendEmailParams} params - Email parameters
   * @returns {Promise<Object>} Response from ZeptoMail API
   */
  async sendEmail({
    to,
    subject,
    htmlBody,
    from = {},
    trackClicks = true,
    trackOpens = true
  }) {
    const config = getEnvConfig();
    
    // Validate API key
    if (!config.apiKey) {
      throw new Error('ZeptoMail API key not configured. Please set ZEPTOMAIL_API_KEY or ZEPTO_API_KEY in environment variables.');
    }

    // Correct API endpoint for sending emails
    const url = "https://api.zeptomail.com/v1.1/email";
    
    // Prepare recipient list
    const recipients = Array.isArray(to) 
      ? to.map(email => ({ email_address: { address: email, name: "" } }))
      : [{ email_address: { address: to, name: "" } }];

    // Prepare sender information
    const fromEmail = from.email || config.fromEmail;
    const fromName = from.name || config.fromName;

    try {
      console.log(`ZeptoMailService: Sending email to ${Array.isArray(to) ? to.join(', ') : to}`);
      console.log(`ZeptoMailService: Subject: ${subject}`);
      console.log(`ZeptoMailService: From: ${fromName} <${fromEmail}>`);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Zoho-enczapikey ${config.apiKey}`,
        },
        body: JSON.stringify({
          // Use correct field names as per Zepto Mail API
          from: {
            address: fromEmail,
            name: fromName
          },
          to: recipients,
          subject,
          htmlbody: htmlBody,
          track_clicks: trackClicks,
          track_opens: trackOpens
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error("ZeptoMailService: API error:", errorData);
        throw new Error(`Failed to send email: ${response.status} ${response.statusText} - ${errorData}`);
      }

      const responseData = await response.json();
      console.log("ZeptoMailService: Email sent successfully:", responseData);
      return {
        success: true,
        data: responseData
      };
    } catch (error) {
      console.error("ZeptoMailService: Error sending email:", error);
      throw new Error(error instanceof Error ? error.message : "Unknown error occurred while sending email");
    }
  },

  /**
   * Test email configuration by sending a test email
   * @param {string} testEmail - Email address to send test email to
   * @returns {Promise<Object>} Test result
   */
  async testConfiguration(testEmail) {
    const config = getEnvConfig();
    
    if (!config.apiKey) {
      return {
        success: false,
        error: 'API key not configured'
      };
    }

    if (!testEmail) {
      return {
        success: false,
        error: 'Test email address is required'
      };
    }

    try {
      const result = await this.sendEmail({
        to: testEmail,
        subject: 'ZeptoMail Configuration Test',
        htmlBody: `
          <h2>ZeptoMail Configuration Test</h2>
          <p>This is a test email to verify your ZeptoMail configuration is working correctly.</p>
          <p><strong>Configuration Details:</strong></p>
          <ul>
            <li>From Email: ${config.fromEmail}</li>
            <li>From Name: ${config.fromName}</li>
            <li>API Key: ${config.apiKey ? 'Configured' : 'Not configured'}</li>
          </ul>
          <p>If you received this email, your ZeptoMail integration is working properly!</p>
          <hr>
          <p><small>Sent from Karmod Project Hub</small></p>
        `
      });

      return {
        success: true,
        message: 'Test email sent successfully',
        data: result.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Validate ZeptoMail configuration
   * @returns {Object} Validation result
   */
  validateConfiguration() {
    const config = getEnvConfig();
    const issues = [];

    if (!config.apiKey) {
      issues.push('API key is not configured');
    } else if (config.apiKey.length < 10) {
      issues.push('API key appears to be invalid (too short)');
    }

    if (!config.fromEmail) {
      issues.push('From email is not configured');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(config.fromEmail)) {
      issues.push('From email format is invalid');
    }

    if (!config.fromName) {
      issues.push('From name is not configured');
    }

    return {
      valid: issues.length === 0,
      issues,
      config: {
        hasApiKey: !!config.apiKey,
        fromEmail: config.fromEmail,
        fromName: config.fromName,
        hasBounceAddress: !!config.bounceAddress
      }
    };
  }
};

// CommonJS export for Node.js compatibility
module.exports = { zeptoMailService };
