const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('../config/environment');

/**
 * Hash a password using bcrypt
 * @param {string} password - Plain text password
 * @returns {Promise<string>} - Hashed password
 */
const hashPassword = async (password) => {
  return await bcrypt.hash(password, config.security.bcryptRounds);
};

/**
 * Compare a password with its hash
 * @param {string} password - Plain text password
 * @param {string} hash - Hashed password
 * @returns {Promise<boolean>} - True if password matches
 */
const comparePassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

/**
 * Generate JWT token
 * @param {object} payload - Token payload
 * @param {string} expiresIn - Token expiration time
 * @returns {string} - JWT token
 */
const generateToken = (payload, expiresIn = null) => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: expiresIn || config.jwt.expiresIn
  });
};

/**
 * Generate refresh token
 * @param {object} payload - Token payload
 * @returns {string} - Refresh token
 */
const generateRefreshToken = (payload) => {
  const refreshSecret = process.env.JWT_REFRESH_SECRET || config.jwt.secret;
  return jwt.sign(payload, refreshSecret, {
    expiresIn: config.jwt.refreshExpiresIn
  });
};

/**
 * Verify refresh token
 * @param {string} token - Refresh token
 * @returns {object} - Decoded token payload
 */
const verifyRefreshToken = (token) => {
  const refreshSecret = process.env.JWT_REFRESH_SECRET || config.jwt.secret;
  return jwt.verify(token, refreshSecret);
};

/**
 * Generate unique project ID
 * @returns {string} - Project ID in format KM-YYYY-XXX
 */
const generateProjectId = () => {
  const year = new Date().getFullYear();
  const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `KM-${year}-${randomNum}`;
};

/**
 * Calculate project progress based on phases
 * @param {Array} phases - Project phases
 * @returns {number} - Overall progress percentage
 */
const calculateProjectProgress = (phases) => {
  if (!phases || phases.length === 0) return 0;
  
  const totalProgress = phases.reduce((sum, phase) => sum + (phase.progress || 0), 0);
  return Math.round(totalProgress / phases.length);
};

/**
 * Format currency for Nigerian Naira
 * @param {number} amount - Amount to format
 * @returns {string} - Formatted currency string
 */
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN'
  }).format(amount);
};

/**
 * Format date for display
 * @param {Date|string} date - Date to format
 * @returns {string} - Formatted date string
 */
const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Generate pagination metadata
 * @param {number} page - Current page
 * @param {number} limit - Items per page
 * @param {number} total - Total items
 * @returns {object} - Pagination metadata
 */
const getPaginationMeta = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;

  return {
    currentPage: page,
    totalPages,
    totalItems: total,
    itemsPerPage: limit,
    hasNext,
    hasPrev,
    nextPage: hasNext ? page + 1 : null,
    prevPage: hasPrev ? page - 1 : null
  };
};

/**
 * Sanitize user object (remove sensitive data)
 * @param {object} user - User object
 * @returns {object} - Sanitized user object
 */
const sanitizeUser = (user) => {
  const { password, ...sanitizedUser } = user;
  return sanitizedUser;
};

/**
 * Check if user has access to project
 * @param {object} user - User object
 * @param {object} project - Project object
 * @returns {boolean} - True if user has access
 */
const hasProjectAccess = (user, project) => {
  switch (user.role) {
    case 'admin':
    case 'project_manager':
    case 'accountant':
    case 'store_keeper':
    case 'quantity_surveyor':
      return true;
    case 'project_lead':
      return project.projectLeadId === user.id;
    case 'team_member':
      return project.teamMembers?.some(member => member.userId === user.id);
    case 'client':
      return project.clientId === user.id;
    default:
      return false;
  }
};

module.exports = {
  hashPassword,
  comparePassword,
  generateToken,
  generateRefreshToken,
  verifyRefreshToken,
  generateProjectId,
  calculateProjectProgress,
  formatCurrency,
  formatDate,
  getPaginationMeta,
  sanitizeUser,
  hasProjectAccess
};
