// Import Prisma client from root directory (unified full-stack architecture)
// Since we have a single node_modules in root, this will resolve correctly
const { PrismaClient } = require('@prisma/client');

// Create a singleton Prisma client instance
let prisma;

if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient();
} else {
  // In development, use a global variable to preserve the instance
  // across hot reloads
  if (!global.__prisma) {
    global.__prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });
  }
  prisma = global.__prisma;
}

module.exports = prisma;
