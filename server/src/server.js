/**
 * Production Server Startup
 * Handles graceful startup and shutdown for production deployment
 */

const app = require('./app');
const config = require('./config/environment');
const fs = require('fs');
const path = require('path');

// Create necessary directories
const createDirectories = () => {
  const dirs = [
    config.upload.dir,
    path.dirname(config.logging.file),
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`Created directory: ${dir}`);
    }
  });
};

// Graceful shutdown handler
const gracefulShutdown = (signal) => {
  console.log(`\n${signal} received. Starting graceful shutdown...`);
  
  server.close((err) => {
    if (err) {
      console.error('Error during server shutdown:', err);
      process.exit(1);
    }
    
    console.log('Server closed successfully');
    process.exit(0);
  });

  // Force shutdown after 30 seconds
  setTimeout(() => {
    console.error('Forced shutdown after timeout');
    process.exit(1);
  }, 30000);
};

// Error handlers
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Graceful shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Initialize server
const startServer = async () => {
  try {
    // Create necessary directories
    createDirectories();

    // Start server
    const server = app.listen(config.app.port, () => {
      console.log(`
🚀 ${config.app.name} v${config.app.version}
📍 Environment: ${config.app.env}
🌐 Server running on port ${config.app.port}
📊 Database: ${config.database.url ? 'Connected' : 'Not configured'}
🔒 CORS Origin: ${config.cors.origin}
⚡ Rate Limiting: ${config.features.enableRateLimit ? 'Enabled' : 'Disabled'}
📁 Upload Directory: ${config.upload.dir}
📝 Log Level: ${config.logging.level}
      `);
    });

    // Store server reference for graceful shutdown
    global.server = server;

    return server;
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
if (require.main === module) {
  startServer();
}

module.exports = { startServer };
