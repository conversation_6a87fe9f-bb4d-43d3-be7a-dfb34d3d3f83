const express = require('express');
const { validate, validateQuery, schemas } = require('../middleware/validation');
const { authenticateToken, requireStaffOrAdmin } = require('../middleware/auth');
const projectController = require('../controllers/projectController');

const router = express.Router();

// GET /api/projects - Get accessible projects
router.get('/', 
  authenticateToken,
  validateQuery(schemas.pagination),
  projectController.getProjects
);

// GET /api/projects/:id - Get project by ID
router.get('/:id', 
  authenticateToken,
  projectController.getProjectById
);

// POST /api/projects - Create new project
router.post('/', 
  authenticateToken, 
  requireStaffOrAdmin,
  validate(schemas.projectCreate),
  projectController.createProject
);

// PUT /api/projects/:id - Update project
router.put('/:id', 
  authenticateToken,
  validate(schemas.projectUpdate),
  projectController.updateProject
);

// DELETE /api/projects/:id - Delete project
router.delete('/:id',
  authenticateToken,
  requireStaffOrAdmin,
  projectController.deleteProject
);

// GET /api/projects/:id/financials - Get project financials
router.get('/:id/financials',
  authenticateToken,
  projectController.getProjectFinancials
);

// PUT /api/projects/:id/financials - Update project financials
router.put('/:id/financials',
  authenticateToken,
  projectController.updateProjectFinancials
);

// POST /api/projects/:id/phases - Add new phase to project
router.post('/:id/phases',
  authenticateToken,
  projectController.addProjectPhase
);

// PUT /api/projects/:id/phases/:phaseId - Update project phase
router.put('/:id/phases/:phaseId',
  authenticateToken,
  projectController.updateProjectPhase
);

// DELETE /api/projects/:id/phases/:phaseId - Delete project phase
router.delete('/:id/phases/:phaseId',
  authenticateToken,
  projectController.deleteProjectPhase
);

// POST /api/projects/:id/phases/:phaseId/tasks - Add task to phase
router.post('/:id/phases/:phaseId/tasks', 
  authenticateToken,
  projectController.addPhaseTask
);

// PUT /api/projects/:id/phases/:phaseId/tasks/:taskId - Update phase task
router.put('/:id/phases/:phaseId/tasks/:taskId', 
  authenticateToken,
  projectController.updatePhaseTask
);

// DELETE /api/projects/:id/phases/:phaseId/tasks/:taskId - Delete phase task
router.delete('/:id/phases/:phaseId/tasks/:taskId', 
  authenticateToken,
  projectController.deletePhaseTask
);

module.exports = router;
