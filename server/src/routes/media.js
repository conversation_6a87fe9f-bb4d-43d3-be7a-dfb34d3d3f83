const express = require('express');
const multer = require('multer');
const path = require('path');
const jwt = require('jsonwebtoken');
const { authenticateToken } = require('../middleware/auth');
const mediaController = require('../controllers/mediaController');
const prisma = require('../lib/prisma');

const router = express.Router();

// Custom authentication middleware that supports both header and query token
const authenticateTokenFlexible = async (req, res, next) => {
  try {
    let token = null;

    // Try to get token from Authorization header first
    const authHeader = req.headers['authorization'];
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }

    // If no header token, try query parameter
    if (!token && req.query.token) {
      token = req.query.token;
    }

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    });

    if (!user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../../uploads'));
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  // Allow images, documents, and videos
  const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|xls|xlsx|mp4|mov|avi/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images, documents, and videos are allowed.'));
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: fileFilter
});

// GET /api/media - Get media files
router.get('/', 
  authenticateToken,
  mediaController.getMediaFiles
);

// GET /api/media/project/:projectId - Get media files for project
router.get('/project/:projectId', 
  authenticateToken,
  mediaController.getProjectMediaFiles
);

// POST /api/media/upload - Upload media file
router.post('/upload', 
  authenticateToken,
  upload.single('file'),
  mediaController.uploadMediaFile
);

// GET /api/media/:id/download - Download media file
router.get('/:id/download',
  authenticateToken,
  mediaController.downloadMediaFile
);

// GET /api/media/:id/view - View media file
router.get('/:id/view',
  authenticateTokenFlexible,
  mediaController.viewMediaFile
);

// DELETE /api/media/:id - Delete media file
router.delete('/:id',
  authenticateToken,
  mediaController.deleteMediaFile
);

module.exports = router;
