const express = require('express');
const router = express.Router();
const systemSettingsController = require('../controllers/systemSettingsController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// All routes require admin authentication
router.use(authenticateToken);
router.use(requireAdmin);

// GET /api/admin/settings - Get all system settings
router.get('/', systemSettingsController.getSystemSettings);

// GET /api/admin/settings/:category - Get settings by category
router.get('/:category', systemSettingsController.getSettingsByCategory);

// PUT /api/admin/settings - Update system settings
router.put('/', systemSettingsController.updateSystemSettings);

// GET /api/admin/settings/zeptomail - Get ZeptoMail settings
router.get('/email/zeptomail', systemSettingsController.getZeptomailSettings);

// PUT /api/admin/settings/zeptomail - Update ZeptoMail settings
router.put('/email/zeptomail', systemSettingsController.updateZeptomailSettings);

// DELETE /api/admin/settings/:key - Delete a system setting
router.delete('/:key', systemSettingsController.deleteSetting);

module.exports = router;
