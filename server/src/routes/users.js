const express = require('express');
const multer = require('multer');
const path = require('path');
const { validate, validateQuery, schemas } = require('../middleware/validation');
const { authenticateToken, requireAdmin, requireAdminOrProjectManager, requireStaffOrAdmin } = require('../middleware/auth');
const userController = require('../controllers/userController');

// Configure multer for avatar uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/avatars/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'avatar-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    // Check file type
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});

const router = express.Router();

// GET /api/users - Get all users (admin or project manager only)
router.get('/',
  authenticateToken,
  requireAdminOrProjectManager,
  validateQuery(schemas.pagination),
  userController.getAllUsers
);

// GET /api/users/staff - Get all staff members
router.get('/staff',
  authenticateToken,
  requireStaffOrAdmin,
  validateQuery(schemas.pagination),
  userController.getStaffUsers
);

// GET /api/users/clients - Get all clients
router.get('/clients',
  authenticateToken,
  requireStaffOrAdmin,
  validateQuery(schemas.pagination),
  userController.getClientUsers
);

// GET /api/users/:id - Get user by ID
router.get('/:id', 
  authenticateToken, 
  userController.getUserById
);

// POST /api/users - Create new user (admin or project manager only)
router.post('/',
  authenticateToken,
  requireAdminOrProjectManager,
  validate(schemas.userCreate),
  userController.createUser
);

// PUT /api/users/:id - Update user
router.put('/:id', 
  authenticateToken, 
  validate(schemas.userUpdate),
  userController.updateUser
);

// GET /api/users/:id/stats - Get user statistics
router.get('/:id/stats',
  authenticateToken,
  userController.getUserStats
);

// GET /api/users/:id/activity - Get user activity
router.get('/:id/activity',
  authenticateToken,
  userController.getUserActivity
);

// POST /api/users/:id/avatar - Upload user avatar
router.post('/:id/avatar',
  authenticateToken,
  upload.single('avatar'),
  userController.uploadUserAvatar
);

// DELETE /api/users/:id - Delete user (admin or project manager only)
router.delete('/:id',
  authenticateToken,
  requireAdminOrProjectManager,
  userController.deleteUser
);

module.exports = router;
