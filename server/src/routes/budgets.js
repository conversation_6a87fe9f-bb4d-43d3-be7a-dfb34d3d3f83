const express = require('express');
const { authenticateToken, requireRole } = require('../middleware/auth');
const budgetController = require('../controllers/budgetController');

const router = express.Router();

// GET /api/budgets - Get budgets
router.get('/', 
  authenticateToken,
  budgetController.getBudgets
);

// GET /api/budgets/project/:projectId - Get budgets for project
router.get('/project/:projectId', 
  authenticateToken,
  budgetController.getProjectBudgets
);

// POST /api/budgets - Create budget
router.post('/',
  authenticateToken,
  requireRole(['admin', 'project_manager', 'accountant', 'project_lead', 'quantity_surveyor']),
  budgetController.createBudget
);

// PUT /api/budgets/:id - Update budget
router.put('/:id',
  authenticateToken,
  requireRole(['admin', 'project_manager', 'accountant', 'project_lead', 'quantity_surveyor']),
  budgetController.updateBudget
);

// POST /api/budgets/:id/push-to-admin - Accountant pushes budget to admin
router.post('/:id/push-to-admin',
  authenticateToken,
  requireRole(['accountant']),
  budgetController.pushToAdmin
);

// POST /api/budgets/:id/return-to-lead - Accountant returns budget to lead
router.post('/:id/return-to-lead',
  authenticateToken,
  requireRole(['accountant']),
  budgetController.returnToLead
);

// POST /api/budgets/:id/approve - Admin or Project Manager approves budget
router.post('/:id/approve',
  authenticateToken,
  requireRole(['admin', 'project_manager']),
  budgetController.approveBudget
);

// DELETE /api/budgets/:id - Delete budget
router.delete('/:id',
  authenticateToken,
  requireRole(['admin', 'project_manager', 'accountant']),
  budgetController.deleteBudget
);

module.exports = router;
