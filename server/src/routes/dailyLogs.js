const express = require('express');
const { validate, validateQuery, schemas } = require('../middleware/validation');
const { authenticateToken } = require('../middleware/auth');
const dailyLogController = require('../controllers/dailyLogController');
const { uploadFiles, handleUploadError } = require('../middleware/upload');

const router = express.Router();

// GET /api/daily-logs - Get daily logs
router.get('/', 
  authenticateToken,
  validateQuery(schemas.pagination),
  dailyLogController.getDailyLogs
);

// GET /api/daily-logs/project/:projectId - Get daily logs for project
router.get('/project/:projectId', 
  authenticateToken,
  validateQuery(schemas.pagination),
  dailyLogController.getProjectDailyLogs
);

// POST /api/daily-logs - Create daily log
router.post('/',
  authenticateToken,
  uploadFiles,
  handleUploadError,
  validate(schemas.dailyLogCreate),
  dailyLogController.createDailyLog
);

// POST /api/daily-logs/:id/replies - Add reply to daily log
router.post('/:id/replies',
  authenticateToken,
  uploadFiles,
  handleUploadError,
  validate(schemas.dailyLogReply),
  dailyLogController.addReplyToDailyLog
);

// DELETE /api/daily-logs/:id - Delete daily log
router.delete('/:id',
  authenticateToken,
  dailyLogController.deleteDailyLog
);

// DELETE /api/daily-logs/:id/replies/:replyId - Delete daily log reply
router.delete('/:id/replies/:replyId',
  authenticateToken,
  dailyLogController.deleteDailyLogReply
);

module.exports = router;
