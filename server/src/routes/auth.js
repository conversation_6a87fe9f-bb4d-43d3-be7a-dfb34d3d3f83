const express = require('express');
const { validate, schemas } = require('../middleware/validation');
const { authenticateToken } = require('../middleware/auth');
const authController = require('../controllers/authController');

const router = express.Router();

// POST /api/auth/login
router.post('/login', validate(schemas.userLogin), authController.login);

// POST /api/auth/logout
router.post('/logout', authenticateToken, authController.logout);

// POST /api/auth/refresh
router.post('/refresh', authController.refreshToken);

// GET /api/auth/me
router.get('/me', authenticateToken, authController.getCurrentUser);

// POST /api/auth/change-password
router.post('/change-password', authenticateToken, validate(schemas.changePassword), authController.changePassword);

module.exports = router;
