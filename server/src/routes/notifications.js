const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const notificationController = require('../controllers/notificationController');

const router = express.Router();

// GET /api/notifications - Get user notifications
router.get('/', 
  authenticateToken,
  notificationController.getUserNotifications
);

// PUT /api/notifications/:id/read - Mark notification as read
router.put('/:id/read', 
  authenticateToken,
  notificationController.markNotificationAsRead
);

// PUT /api/notifications/read-all - Mark all notifications as read
router.put('/read-all', 
  authenticateToken,
  notificationController.markAllNotificationsAsRead
);

// DELETE /api/notifications/:id - Delete notification
router.delete('/:id',
  authenticateToken,
  notificationController.deleteNotification
);

// GET /api/notifications/settings - Get notification settings
router.get('/settings',
  authenticateToken,
  notificationController.getNotificationSettings
);

// PUT /api/notifications/settings - Update notification settings
router.put('/settings',
  authenticateToken,
  notificationController.updateNotificationSettings
);

// POST /api/notifications/test-email - Send test email
router.post('/test-email',
  authenticateToken,
  notificationController.testEmailSettings
);

module.exports = router;
