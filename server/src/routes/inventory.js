const express = require('express');
const { authenticateToken, requireRole } = require('../middleware/auth');
const inventoryController = require('../controllers/inventoryController');

const router = express.Router();

// GET /api/inventory - Get inventory items
router.get('/',
  authenticateToken,
  requireRole(['admin', 'project_manager', 'project_lead', 'store_keeper', 'team_member']),
  inventoryController.getInventoryItems
);

// POST /api/inventory - Create inventory item
router.post('/',
  authenticateToken,
  requireRole(['admin', 'project_manager', 'store_keeper']),
  inventoryController.createInventoryItem
);

// PUT /api/inventory/:id - Update inventory item
router.put('/:id',
  authenticateToken,
  requireRole(['admin', 'project_manager', 'store_keeper']),
  inventoryController.updateInventoryItem
);

// DELETE /api/inventory/:id - Delete inventory item
router.delete('/:id',
  authenticateToken,
  requireRole(['admin', 'project_manager']),
  inventoryController.deleteInventoryItem
);

// GET /api/inventory/requests - Get inventory requests
router.get('/requests', 
  authenticateToken,
  inventoryController.getInventoryRequests
);

// POST /api/inventory/requests - Create inventory request
router.post('/requests', 
  authenticateToken,
  inventoryController.createInventoryRequest
);

// PUT /api/inventory/requests/:id - Update inventory request
router.put('/requests/:id', 
  authenticateToken,
  inventoryController.updateInventoryRequest
);

module.exports = router;
