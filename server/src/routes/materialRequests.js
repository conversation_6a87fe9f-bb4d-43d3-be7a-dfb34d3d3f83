const express = require('express');
const { authenticateToken, requireRole } = require('../middleware/auth');
const materialRequestController = require('../controllers/materialRequestController');

const router = express.Router();

// GET /api/material-requests - Get material requests
router.get('/', 
  authenticateToken,
  materialRequestController.getMaterialRequests
);

// POST /api/material-requests - Create material request
router.post('/',
  authenticateToken,
  requireRole(['project_lead', 'team_member', 'store_keeper', 'admin', 'project_manager']),
  materialRequestController.createMaterialRequest
);

// PUT /api/material-requests/:id - Update material request
router.put('/:id', 
  authenticateToken,
  materialRequestController.updateMaterialRequest
);

// DELETE /api/material-requests/:id - Delete material request
router.delete('/:id', 
  authenticateToken,
  materialRequestController.deleteMaterialRequest
);

module.exports = router;
