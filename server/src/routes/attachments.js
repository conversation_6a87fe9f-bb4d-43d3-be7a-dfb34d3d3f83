const express = require('express');
const router = express.Router();
const attachmentController = require('../controllers/attachmentController');
const { authenticateToken } = require('../middleware/auth');
const jwt = require('jsonwebtoken');
const prisma = require('../lib/prisma');

// Custom authentication middleware that supports both header and query token
const authenticateTokenFlexible = async (req, res, next) => {
  try {
    let token = null;

    // Try to get token from Authorization header first
    const authHeader = req.headers['authorization'];
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }

    // If no header token, try query parameter
    if (!token && req.query.token) {
      token = req.query.token;
    }

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    });

    if (!user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// GET /api/attachments/:id/download - Download attachment
router.get('/:id/download', 
  authenticateToken,
  attachmentController.downloadAttachment
);

// GET /api/attachments/:id/view - View attachment
router.get('/:id/view',
  authenticateTokenFlexible,
  attachmentController.viewAttachment
);

// DELETE /api/attachments/:id - Delete attachment
router.delete('/:id', 
  authenticateToken,
  attachmentController.deleteAttachment
);

module.exports = router;
