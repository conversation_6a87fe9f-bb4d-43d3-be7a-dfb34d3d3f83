const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

// Simple decryption for sensitive settings (matching systemSettingsController)
const ENCRYPTION_KEY = process.env.SETTINGS_ENCRYPTION_KEY || 'your-32-character-secret-key-here';
const ALGORITHM = 'aes-256-cbc';

// Generate a proper 32-byte key from the encryption key
const getKey = () => {
  return crypto.createHash('sha256').update(ENCRYPTION_KEY).digest();
};

const decrypt = (encryptedText) => {
  if (!encryptedText) return null;

  console.log(`EmailService: Attempting to decrypt value: ${encryptedText.substring(0, 20)}...`);

  try {
    const key = getKey();
    const parts = encryptedText.split(':');

    if (parts.length === 2) {
      // New format with IV
      console.log('EmailService: Using new format decryption with IV');
      const iv = Buffer.from(parts[0], 'hex');
      const encryptedData = parts[1];
      const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      // Remove any padding characters (both null bytes and PKCS7 padding)
      decrypted = decrypted.replace(/[\x00-\x1F]+$/g, '').trim();

      console.log(`EmailService: Decrypted result preview: ${decrypted.substring(0, 10)}...`);
      console.log('EmailService: New format decryption successful');
      return decrypted;
    } else {
      // Old format - just return null since createDecipher is deprecated
      console.log('EmailService: Old format detected, but createDecipher is deprecated');
      console.log('EmailService: Encrypted text might need to be re-encrypted with new format');
      return null;
    }
  } catch (error) {
    console.error('EmailService: Decryption failed:', error.message);

    // Try without setAutoPadding as fallback
    try {
      console.log('EmailService: Trying decryption without padding adjustment');
      const key = getKey();
      const parts = encryptedText.split(':');

      if (parts.length === 2) {
        const iv = Buffer.from(parts[0], 'hex');
        const encryptedData = parts[1];
        const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
        let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
        decrypted += decipher.final('utf8');

        // Remove any padding characters
        decrypted = decrypted.replace(/[\x00-\x1F]+$/g, '').trim();

        console.log(`EmailService: Fallback decrypted result preview: ${decrypted.substring(0, 10)}...`);
        return decrypted;
      }
    } catch (fallbackError) {
      console.error('EmailService: Fallback decryption also failed:', fallbackError.message);
    }

    return null;
  }
};

class EmailService {
  constructor() {
    // Fixed: Use correct ZeptoMail API URL
    this.zeptomailApiUrl = 'https://api.zeptomail.com/v1.1/email';
    this.defaultFromEmail = '<EMAIL>';
    this.defaultFromName = 'Karmod Project Hub';
    this.settingsCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
  }

  // Get ZeptoMail settings from database with caching
  async getZeptomailSettings() {
    const cacheKey = 'zeptomail_settings';
    const cached = this.settingsCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }

    try {
      const settings = await prisma.systemSettings.findMany({
        where: {
          category: 'email',
          key: {
            in: ['zeptomail_api_key', 'zeptomail_from_email', 'zeptomail_from_name', 'zeptomail_test_email']
          }
        }
      });

      console.log('Raw settings from database:', settings.map(s => ({
        key: s.key,
        hasValue: !!s.value,
        encrypted: s.encrypted,
        valueLength: s.value ? s.value.length : 0
      })));

      const settingsMap = {};
      settings.forEach(setting => {
        const value = setting.encrypted && setting.value ? decrypt(setting.value) : setting.value;
        settingsMap[setting.key] = value;
        console.log(`Processed setting ${setting.key}: hasValue=${!!value}, encrypted=${setting.encrypted}`);
      });

      console.log('Final settings map:', {
        hasApiKey: !!settingsMap.zeptomail_api_key,
        fromEmail: settingsMap.zeptomail_from_email,
        fromName: settingsMap.zeptomail_from_name,
        testEmail: settingsMap.zeptomail_test_email
      });

      // Cache the settings
      this.settingsCache.set(cacheKey, {
        data: settingsMap,
        timestamp: Date.now()
      });

      return settingsMap;
    } catch (error) {
      console.error('Error getting ZeptoMail settings:', error);
      // Fallback to environment variables (support both naming conventions)
      return {
        zeptomail_api_key: process.env.ZEPTOMAIL_API_KEY || process.env.ZEPTO_API_KEY,
        zeptomail_from_email: process.env.ZEPTOMAIL_FROM_EMAIL || process.env.ZEPTO_FROM_EMAIL || this.defaultFromEmail,
        zeptomail_from_name: process.env.ZEPTOMAIL_FROM_NAME || this.defaultFromName,
        zeptomail_test_email: process.env.ZEPTOMAIL_TEST_EMAIL
      };
    }
  }

  // Clear settings cache (useful when settings are updated)
  clearSettingsCache() {
    this.settingsCache.clear();
  }

  // Validate API key format
  validateApiKey(apiKey) {
    if (!apiKey) {
      return { valid: false, error: 'API key is required' };
    }

    // ZeptoMail API keys typically start with 'wh_' or similar pattern
    if (typeof apiKey !== 'string' || apiKey.length < 10) {
      return { valid: false, error: 'API key appears to be invalid (too short)' };
    }

    return { valid: true };
  }

  // Send email using ZeptoMail API
  async sendEmail({ to, subject, template, data, from = null }) {
    try {
      console.log(`Starting email send process to: ${to}, subject: ${subject}, template: ${template}`);

      const settings = await this.getZeptomailSettings();
      let apiKey = settings.zeptomail_api_key;

      // If no API key from database, try environment variables (support both naming conventions)
      if (!apiKey) {
        console.log('No API key in database, checking environment variables...');
        apiKey = process.env.ZEPTOMAIL_API_KEY || process.env.ZEPTO_API_KEY;

        if (apiKey) {
          console.log('Using API key from environment variables');
          // Also use other env settings as fallback
          settings.zeptomail_from_email = settings.zeptomail_from_email || process.env.ZEPTOMAIL_FROM_EMAIL || process.env.ZEPTO_FROM_EMAIL || this.defaultFromEmail;
          settings.zeptomail_from_name = settings.zeptomail_from_name || process.env.ZEPTOMAIL_FROM_NAME || this.defaultFromName;
        }
      }

      console.log('Retrieved settings:', {
        hasApiKey: !!apiKey,
        apiKeySource: apiKey === process.env.ZEPTOMAIL_API_KEY ? 'environment' : 'database',
        apiKeyLength: apiKey ? apiKey.length : 0,
        apiKeyFirst10: apiKey ? apiKey.substring(0, 10) : 'N/A',
        apiKeyLast10: apiKey ? apiKey.substring(apiKey.length - 10) : 'N/A',
        fromEmail: settings.zeptomail_from_email,
        fromName: settings.zeptomail_from_name
      });

      if (!apiKey) {
        const error = 'ZeptoMail API key not configured. Please configure it in the admin settings or set ZEPTOMAIL_API_KEY in environment variables.';
        console.error(error);
        throw new Error(error);
      }

      // Validate API key format
      const validation = this.validateApiKey(apiKey);
      if (!validation.valid) {
        const error = `Invalid API key: ${validation.error}`;
        console.error(error);
        throw new Error(error);
      }

      const fromEmail = from?.email || settings.zeptomail_from_email || this.defaultFromEmail;
      const fromName = from?.name || settings.zeptomail_from_name || this.defaultFromName;

      console.log(`Email configuration: from=${fromEmail}, name=${fromName}`);

      // Generate HTML content based on template
      const htmlContent = this.generateEmailTemplate(template, data);

      const emailData = {
        from: {
          address: fromEmail,
          name: fromName
        },
        to: Array.isArray(to) ? to.map(email => ({ email_address: { address: email } })) : [{ email_address: { address: to } }],
        subject,
        htmlbody: htmlContent
      };

      const authHeader = `Zoho-enczapikey ${apiKey}`;

      console.log('Sending email with data:', {
        from: emailData.from,
        to: emailData.to,
        subject: emailData.subject,
        htmlLength: htmlContent.length,
        authHeaderLength: authHeader.length,
        authHeaderFirst20: authHeader.substring(0, 20),
        authHeaderLast10: authHeader.substring(authHeader.length - 10)
      });

      const response = await axios.post(this.zeptomailApiUrl, emailData, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        timeout: 30000 // 30 second timeout
      });

      console.log('Email sent successfully:', response.data);
      return { success: true, data: response.data };

    } catch (error) {
      console.error('Email sending failed:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        headers: error.response?.headers
      });

      // Provide more specific error messages
      let errorMessage = 'Failed to send email';

      if (error.response) {
        const status = error.response.status;
        const responseData = error.response.data;

        switch (status) {
          case 401:
            errorMessage = 'Invalid or unauthorized API key. Please check your ZeptoMail API key.';
            break;
          case 400:
            errorMessage = `Bad request: ${responseData?.message || 'Invalid email data'}`;
            break;
          case 403:
            errorMessage = 'Access forbidden. Please check your ZeptoMail account permissions.';
            break;
          case 429:
            errorMessage = 'Rate limit exceeded. Please try again later.';
            break;
          case 500:
            errorMessage = 'ZeptoMail server error. Please try again later.';
            break;
          default:
            errorMessage = `Email service error (${status}): ${responseData?.message || error.message}`;
        }
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = 'Email sending timeout. Please try again.';
      } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        errorMessage = 'Network error: Unable to connect to email service.';
      } else {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  }

  // Generate email templates
  generateEmailTemplate(template, data) {
    switch (template) {
      case 'test-email':
        return this.generateTestEmailTemplate(data);
      case 'notification':
        return this.generateNotificationTemplate(data);
      case 'project-update':
        return this.generateProjectUpdateTemplate(data);
      case 'daily-digest':
        return this.generateDailyDigestTemplate(data);
      case 'weekly-report':
        return this.generateWeeklyReportTemplate(data);
      case 'welcome':
        return this.generateWelcomeTemplate(data);
      case 'password-reset':
        return this.generatePasswordResetTemplate(data);
      default:
        return this.generateDefaultTemplate(data);
    }
  }

  // Test email template
  generateTestEmailTemplate(data) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Email Test - Karmod Project Hub</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Email Test Successful!</h1>
          </div>
          <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>This is a test email from Karmod Project Hub to verify your email configuration is working correctly.</p>
            <p><strong>Test Details:</strong></p>
            <ul>
              <li>Test Time: ${data.testTime}</li>
              <li>Email Service: ZeptoMail</li>
              <li>Status: ✅ Successfully Delivered</li>
            </ul>
            <p>Your email integration is now ready for production use!</p>
          </div>
          <div class="footer">
            <p>© 2024 Karmod Project Hub. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Notification email template
  generateNotificationTemplate(data) {
    const priorityColors = {
      high: '#ef4444',
      normal: '#3b82f6',
      low: '#10b981'
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${data.title} - Karmod Project Hub</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: ${priorityColors[data.priority] || '#3b82f6'}; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .priority { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
          .priority-${data.priority} { background: ${priorityColors[data.priority] || '#3b82f6'}; color: white; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📢 ${data.title}</h1>
          </div>
          <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p><span class="priority priority-${data.priority}">${data.priority} Priority</span></p>
            <p>${data.message}</p>
            ${data.projectId ? `<p><strong>Project:</strong> #${data.projectId}</p>` : ''}
            <p>Please log in to your Karmod Project Hub dashboard for more details.</p>
          </div>
          <div class="footer">
            <p>© 2024 Karmod Project Hub. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Project update template
  generateProjectUpdateTemplate(data) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Project Update - ${data.projectName}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .progress-bar { background: #e5e7eb; height: 20px; border-radius: 10px; overflow: hidden; }
          .progress-fill { background: #10b981; height: 100%; transition: width 0.3s ease; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🏗️ Project Update</h1>
          </div>
          <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <h3>${data.projectName} - Progress Update</h3>
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${data.progress || 0}%"></div>
            </div>
            <p><strong>Progress:</strong> ${data.progress || 0}%</p>
            <p><strong>Status:</strong> ${data.status}</p>
            <p><strong>Update:</strong> ${data.updateMessage}</p>
            ${data.nextMilestone ? `<p><strong>Next Milestone:</strong> ${data.nextMilestone}</p>` : ''}
          </div>
          <div class="footer">
            <p>© 2024 Karmod Project Hub. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Default template
  generateDefaultTemplate(data) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Karmod Project Hub</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Karmod Project Hub</h1>
          </div>
          <div class="content">
            <h2>Hello ${data.userName || 'User'}!</h2>
            <p>${data.message || 'You have received a notification from Karmod Project Hub.'}</p>
          </div>
          <div class="footer">
            <p>© 2024 Karmod Project Hub. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Daily digest template (placeholder)
  generateDailyDigestTemplate(data) {
    return this.generateDefaultTemplate({ ...data, message: 'Your daily digest is ready.' });
  }

  // Weekly report template (placeholder)
  generateWeeklyReportTemplate(data) {
    return this.generateDefaultTemplate({ ...data, message: 'Your weekly report is ready.' });
  }

  // Welcome template (placeholder)
  generateWelcomeTemplate(data) {
    return this.generateDefaultTemplate({ ...data, message: `Welcome to Karmod Project Hub, ${data.userName}!` });
  }

  // Password reset template (placeholder)
  generatePasswordResetTemplate(data) {
    return this.generateDefaultTemplate({ ...data, message: 'Password reset instructions have been sent.' });
  }
}

// Export singleton instance
const emailService = new EmailService();

module.exports = {
  sendEmail: emailService.sendEmail.bind(emailService),
  clearSettingsCache: emailService.clearSettingsCache.bind(emailService),
  EmailService
};
