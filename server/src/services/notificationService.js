const { PrismaClient } = require('@prisma/client');
const { sendEmail } = require('./emailService');

const prisma = new PrismaClient();

class NotificationService {
  constructor() {
    this.emailQueue = [];
    this.processing = false;
  }

  // Create notification and handle email sending
  async createNotification(notificationData) {
    try {
      // Create the notification in database
      const notification = await prisma.notification.create({
        data: notificationData,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              notificationSettings: true
            }
          },
          sender: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      // Check if user wants email notifications
      await this.handleEmailNotification(notification);

      return notification;
    } catch (error) {
      console.error('Create notification error:', error);
      throw error;
    }
  }

  // Create multiple notifications and handle email sending
  async createManyNotifications(notificationsData) {
    try {
      // Create notifications one by one to get proper IDs and handle email
      const createdNotifications = [];

      for (const notificationData of notificationsData) {
        const notification = await this.createNotification(notificationData);
        createdNotifications.push(notification);
      }

      return createdNotifications;
    } catch (error) {
      console.error('Create many notifications error:', error);
      throw error;
    }
  }

  // Handle email notification based on user preferences
  async handleEmailNotification(notification) {
    try {
      const user = notification.user;
      const settings = user.notificationSettings;

      // Skip if user doesn't exist or no email
      if (!user || !user.email) {
        return;
      }

      // Skip if user has disabled email notifications
      if (!settings || !settings.emailNotifications) {
        return;
      }

      // Check specific notification type preferences
      const notificationType = this.getNotificationType(notification);
      if (!this.shouldSendEmailForType(settings, notificationType)) {
        return;
      }

      // Add to email queue
      this.emailQueue.push({
        notification,
        user,
        notificationType
      });

      // Process email queue
      this.processEmailQueue();

    } catch (error) {
      console.error('Handle email notification error:', error);
    }
  }

  // Determine notification type based on content
  getNotificationType(notification) {
    const title = notification.title.toLowerCase();
    const message = notification.message.toLowerCase();

    if (title.includes('daily log') || title.includes('project') || message.includes('project')) {
      return 'projectUpdates';
    }
    
    if (title.includes('system') || title.includes('admin') || notification.type === 'error') {
      return 'systemAlerts';
    }

    // Default to project updates for most notifications
    return 'projectUpdates';
  }

  // Check if email should be sent for this notification type
  shouldSendEmailForType(settings, notificationType) {
    switch (notificationType) {
      case 'projectUpdates':
        return settings.projectUpdates;
      case 'systemAlerts':
        return settings.systemAlerts;
      case 'dailyDigest':
        return settings.dailyDigest;
      case 'weeklyReport':
        return settings.weeklyReport;
      default:
        return settings.projectUpdates; // Default fallback
    }
  }

  // Process email queue (prevents overwhelming email service)
  async processEmailQueue() {
    if (this.processing || this.emailQueue.length === 0) {
      return;
    }

    this.processing = true;

    try {
      while (this.emailQueue.length > 0) {
        const emailData = this.emailQueue.shift();
        await this.sendNotificationEmail(emailData);
        
        // Small delay to prevent overwhelming email service
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } catch (error) {
      console.error('Process email queue error:', error);
    } finally {
      this.processing = false;
    }
  }

  // Send individual notification email
  async sendNotificationEmail({ notification, user, notificationType }) {
    try {
      const emailData = {
        to: user.email,
        subject: `Karmod Project Hub - ${notification.title}`,
        template: 'notification',
        data: {
          userName: user.name,
          title: notification.title,
          message: notification.message,
          senderName: notification.sender?.name || 'System',
          notificationType: notificationType,
          timestamp: new Date(notification.createdAt).toLocaleString(),
          priority: notification.type === 'error' ? 'high' : 'normal'
        }
      };

      await sendEmail(emailData);
      
      console.log(`Email notification sent to ${user.email} for: ${notification.title}`);
    } catch (error) {
      console.error(`Failed to send email to ${user.email}:`, error);
    }
  }

  // Utility method to create notification with automatic email
  async notifyUsers(userIds, notificationData) {
    console.log(`[DEBUG] Creating notifications for users:`, userIds);
    console.log(`[DEBUG] Notification data:`, notificationData);

    const notifications = userIds.map(userId => ({
      userId,
      ...notificationData
    }));

    console.log(`[DEBUG] Final notification objects:`, notifications);
    return this.createManyNotifications(notifications);
  }

  // Utility method to notify project stakeholders
  async notifyProjectStakeholders(projectId, notificationData, excludeUserId = null) {
    try {
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true
        }
      });

      if (!project) {
        throw new Error('Project not found');
      }

      const stakeholders = [
        project.projectLeadId,
        project.clientId,
        ...project.teamMembers.map(tm => tm.userId)
      ].filter(id => id && id !== excludeUserId);

      return this.notifyUsers(stakeholders, {
        ...notificationData,
        data: {
          ...notificationData.data,
          projectId
        }
      });
    } catch (error) {
      console.error('Notify project stakeholders error:', error);
      throw error;
    }
  }

  // Utility method to notify users by role
  async notifyUsersByRole(roles, notificationData, excludeUserId = null) {
    try {
      const users = await prisma.user.findMany({
        where: {
          role: {
            in: Array.isArray(roles) ? roles : [roles]
          }
        },
        select: { id: true }
      });

      const userIds = users
        .map(u => u.id)
        .filter(id => id !== excludeUserId);

      return this.notifyUsers(userIds, notificationData);
    } catch (error) {
      console.error('Notify users by role error:', error);
      throw error;
    }
  }
}

// Export singleton instance
const notificationService = new NotificationService();

module.exports = {
  notificationService,
  NotificationService
};
