const Joi = require('joi');

const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    next();
  };
};

const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.query);
    if (error) {
      return res.status(400).json({
        error: 'Query Validation Error',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    next();
  };
};

// Common validation schemas
const schemas = {
  // User schemas
  userLogin: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required()
  }),

  userCreate: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    role: Joi.string().valid(
      'admin',
      'project_manager',
      'project_lead',
      'team_member',
      'client',
      'accountant',
      'store_keeper',
      'quantity_surveyor'
    ).required(),
    name: Joi.string().min(2).max(100).required(),
    phone: Joi.string().allow('').optional(),
    location: Joi.string().allow('').optional(),
    company: Joi.string().allow('').optional(),
    bio: Joi.string().max(500).allow('').optional()
  }),

  userUpdate: Joi.object({
    email: Joi.string().email().optional(),
    role: Joi.string().valid(
      'admin',
      'project_manager',
      'project_lead',
      'team_member',
      'client',
      'accountant',
      'store_keeper',
      'quantity_surveyor'
    ).optional(),
    name: Joi.string().min(2).max(100).optional(),
    phone: Joi.string().allow('').optional(),
    location: Joi.string().allow('').optional(),
    company: Joi.string().allow('').optional(),
    bio: Joi.string().max(500).allow('').optional(),
    password: Joi.string().min(6).optional()
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: Joi.string().min(6).required()
  }),

  // Project schemas
  projectCreate: Joi.object({
    title: Joi.string().min(3).max(200).required(),
    description: Joi.string().optional(),
    location: Joi.string().optional(),
    category: Joi.string().optional(),
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
    budget: Joi.number().positive().optional(),
    projectLeadId: Joi.number().integer().positive().required(),
    teamMemberIds: Joi.array().items(Joi.number().integer().positive()).optional(),
    clientId: Joi.number().integer().positive().optional()
  }),

  projectUpdate: Joi.object({
    title: Joi.string().min(3).max(200).optional(),
    description: Joi.string().optional(),
    location: Joi.string().optional(),
    category: Joi.string().optional(),
    status: Joi.string().valid('not_started', 'in_progress', 'completed', 'on_hold').optional(),
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
    budget: Joi.number().positive().optional(),
    spent: Joi.number().min(0).optional(),
    progress: Joi.number().min(0).max(100).optional(),
    projectLeadId: Joi.number().integer().positive().optional(),
    teamMemberIds: Joi.array().items(Joi.number().integer().positive()).optional(),
    clientId: Joi.number().integer().positive().optional()
  }),

  // Daily log schemas
  dailyLogCreate: Joi.object({
    projectId: Joi.string().required(),
    title: Joi.string().min(1).max(200).optional(),
    message: Joi.string().min(1).max(1000).required(),
    type: Joi.string().valid('update', 'issue', 'milestone', 'note').default('update')
  }),

  dailyLogReply: Joi.object({
    message: Joi.string().min(1).max(1000).required()
  }),

  // Pagination schema
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  })
};

module.exports = {
  validate,
  validateQuery,
  schemas
};
