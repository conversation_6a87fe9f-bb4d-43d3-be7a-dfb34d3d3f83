const jwt = require('jsonwebtoken');
const prisma = require('../lib/prisma');
const config = require('../config/environment');

const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, config.jwt.secret);
    
    // Get fresh user data from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        role: true,
        name: true,
        phone: true,
        location: true,
        company: true,
        bio: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    }
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' });
    }
    return res.status(500).json({ error: 'Token verification failed' });
  }
};

const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!userRoles.includes(req.user.role)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        required: userRoles,
        current: req.user.role
      });
    }

    next();
  };
};

const requireAdmin = requireRole('admin');

const requireAdminOrProjectManager = requireRole(['admin', 'project_manager']);

const requireStaffOrAdmin = requireRole([
  'admin',
  'project_manager',
  'project_lead',
  'team_member',
  'accountant',
  'store_keeper',
  'quantity_surveyor'
]);

module.exports = {
  authenticateToken,
  requireRole,
  requireAdmin,
  requireAdminOrProjectManager,
  requireStaffOrAdmin,
};
