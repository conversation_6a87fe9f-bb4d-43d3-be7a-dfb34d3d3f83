const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const path = require('path');
const config = require('./config/environment');

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

if (config.features.enableRateLimit) {
  app.use('/api', limiter);
}

// CORS configuration
app.use(cors({
  origin: config.cors.origin,
  credentials: config.cors.credentials,
}));

// Body parsing middleware
app.use(express.json({ limit: `${config.upload.maxFileSize}b` }));
app.use(express.urlencoded({ extended: true, limit: `${config.upload.maxFileSize}b` }));

// Compression middleware
app.use(compression());

// Logging middleware
if (config.app.env !== 'test') {
  const logFormat = config.app.env === 'production' ? 'combined' : 'dev';
  app.use(morgan(logFormat));
}

// Static file serving for uploads
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Health check endpoints (must be before other routes)
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Karmod Project Hub API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: '1.0.0'
  });
});

// API root endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'Karmod Project Hub API',
    version: '1.0.0',
    status: 'OK',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      projects: '/api/projects',
      dailyLogs: '/api/daily-logs',
      budgets: '/api/budgets',
      materialRequests: '/api/material-requests',
      inventory: '/api/inventory',
      media: '/api/media',
      notifications: '/api/notifications',
      admin: '/api/admin/settings'
    }
  });
});

// API routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/users', require('./routes/users'));
app.use('/api/projects', require('./routes/projects'));
app.use('/api/daily-logs', require('./routes/dailyLogs'));
app.use('/api/budgets', require('./routes/budgets'));
app.use('/api/material-requests', require('./routes/materialRequests'));
app.use('/api/inventory', require('./routes/inventory'));
app.use('/api/media', require('./routes/media'));
app.use('/api/notifications', require('./routes/notifications'));
app.use('/api/attachments', require('./routes/attachments'));
app.use('/api/admin/settings', require('./routes/systemSettings'));

// Serve React app in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../../dist')));

  // Catch-all handler: send back React's index.html file for non-API routes
  app.get('*', (req, res) => {
    // Don't serve index.html for API routes
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    res.sendFile(path.join(__dirname, '../../dist/index.html'));
  });
}



// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  
  if (err.name === 'ValidationError') {
    return res.status(400).json({ 
      error: 'Validation Error', 
      details: err.message 
    });
  }
  
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({ 
      error: 'Unauthorized', 
      message: 'Invalid token' 
    });
  }
  
  res.status(500).json({
    error: 'Internal Server Error',
    message: config.app.env === 'production'
      ? 'Something went wrong!'
      : err.message
  });
});



module.exports = app;
