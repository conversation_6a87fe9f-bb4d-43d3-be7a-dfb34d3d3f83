const prisma = require('../lib/prisma');
const { hashPassword, sanitizeUser, getPaginationMeta } = require('../utils/helpers');

const userController = {
  // GET /api/users
  getAllUsers: async (req, res) => {
    try {
      const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          select: {
            id: true,
            email: true,
            role: true,
            name: true,
            phone: true,
            location: true,
            company: true,
            createdAt: true,
            updatedAt: true,
          }
        }),
        prisma.user.count()
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        users,
        pagination
      });
    } catch (error) {
      console.error('Get all users error:', error);
      res.status(500).json({ error: 'Failed to fetch users' });
    }
  },

  // GET /api/users/staff
  getStaffUsers: async (req, res) => {
    try {
      const { page = 1, limit = 10 } = req.query;
      const skip = (page - 1) * limit;

      const staffRoles = ['project_lead', 'project_manager', 'team_member', 'accountant', 'admin', 'store_keeper', 'quantity_surveyor'];

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where: {
            role: { in: staffRoles }
          },
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { name: 'asc' },
          select: {
            id: true,
            email: true,
            role: true,
            name: true,
            phone: true,
            location: true,
            company: true,
            createdAt: true,
          }
        }),
        prisma.user.count({
          where: {
            role: { in: staffRoles }
          }
        })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        users,
        pagination
      });
    } catch (error) {
      console.error('Get staff users error:', error);
      res.status(500).json({ error: 'Failed to fetch staff users' });
    }
  },

  // GET /api/users/clients
  getClientUsers: async (req, res) => {
    try {
      const { page = 1, limit = 10 } = req.query;
      const skip = (page - 1) * limit;

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where: {
            role: 'client'
          },
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { name: 'asc' },
          select: {
            id: true,
            email: true,
            role: true,
            name: true,
            phone: true,
            location: true,
            company: true,
            createdAt: true,
          }
        }),
        prisma.user.count({
          where: {
            role: 'client'
          }
        })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        users,
        pagination
      });
    } catch (error) {
      console.error('Get client users error:', error);
      res.status(500).json({ error: 'Failed to fetch client users' });
    }
  },

  // GET /api/users/:id
  getUserById: async (req, res) => {
    try {
      const { id } = req.params;
      const userId = parseInt(id);

      // Check if user can access this user data
      if (!['admin', 'project_manager'].includes(req.user.role) && req.user.id !== userId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          role: true,
          name: true,
          phone: true,
          location: true,
          company: true,
          bio: true,
          avatar: true,
          createdAt: true,
          updatedAt: true,
        }
      });

      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      res.json({ user });
    } catch (error) {
      console.error('Get user by ID error:', error);
      res.status(500).json({ error: 'Failed to fetch user' });
    }
  },

  // POST /api/users
  createUser: async (req, res) => {
    try {
      const { email, password, role, name, phone, location, company, bio } = req.body;

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        return res.status(400).json({ error: 'User with this email already exists' });
      }

      // Hash password
      const hashedPassword = await hashPassword(password);

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          role,
          name,
          phone,
          location,
          company,
          bio
        },
        select: {
          id: true,
          email: true,
          role: true,
          name: true,
          phone: true,
          location: true,
          company: true,
          bio: true,
          createdAt: true,
        }
      });

      res.status(201).json({ user });
    } catch (error) {
      console.error('Create user error:', error);
      res.status(500).json({ error: 'Failed to create user' });
    }
  },

  // PUT /api/users/:id
  updateUser: async (req, res) => {
    try {
      const { id } = req.params;
      const userId = parseInt(id);
      const updateData = req.body;

      // Check permissions
      if (!['admin', 'project_manager'].includes(req.user.role) && req.user.id !== userId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Non-admin/project_manager users can't change their role
      if (!['admin', 'project_manager'].includes(req.user.role) && updateData.role) {
        delete updateData.role;
      }

      // Update user
      const user = await prisma.user.update({
        where: { id: userId },
        data: updateData,
        select: {
          id: true,
          email: true,
          role: true,
          name: true,
          phone: true,
          location: true,
          company: true,
          bio: true,
          updatedAt: true,
        }
      });

      res.json({ user });
    } catch (error) {
      console.error('Update user error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'User not found' });
      }
      res.status(500).json({ error: 'Failed to update user' });
    }
  },

  // DELETE /api/users/:id
  deleteUser: async (req, res) => {
    try {
      const { id } = req.params;
      const userId = parseInt(id);

      // Prevent user from deleting themselves
      if (req.user.id === userId) {
        return res.status(400).json({ error: 'Cannot delete your own account' });
      }

      // Get the user to be deleted
      const userToDelete = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!userToDelete) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Prevent project managers from deleting admins
      if (req.user.role === 'project_manager' && userToDelete.role === 'admin') {
        return res.status(403).json({ error: 'Project managers cannot delete administrators' });
      }

      await prisma.user.delete({
        where: { id: userId }
      });

      res.json({ message: 'User deleted successfully' });
    } catch (error) {
      console.error('Delete user error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'User not found' });
      }
      res.status(500).json({ error: 'Failed to delete user' });
    }
  },

  // GET /api/users/:id/stats
  getUserStats: async (req, res) => {
    try {
      const { id } = req.params;
      const userId = parseInt(id);

      // Check if user exists and user can access this data
      if (req.user.id !== userId && !['admin', 'project_manager'].includes(req.user.role)) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Get user statistics
      const [projectsCount, updatesCount, mediaFilesCount] = await Promise.all([
        // Count projects where user is involved
        prisma.project.count({
          where: {
            OR: [
              { projectLeadId: userId },
              { teamMembers: { some: { userId: userId } } },
              { clientId: userId }
            ]
          }
        }),

        // Count daily logs created by user
        prisma.dailyLog.count({
          where: { userId: userId }
        }),

        // Count media files uploaded by user
        prisma.mediaFile.count({
          where: { uploadedBy: userId }
        })
      ]);

      // Calculate completion rate based on projects
      const completedProjects = await prisma.project.count({
        where: {
          AND: [
            {
              OR: [
                { projectLeadId: userId },
                { teamMembers: { some: { userId: userId } } },
                { clientId: userId }
              ]
            },
            { status: 'completed' }
          ]
        }
      });

      const completionRate = projectsCount > 0 ? Math.round((completedProjects / projectsCount) * 100) : 0;

      res.json({
        projectsCount,
        updatesCount,
        mediaFilesCount,
        completionRate
      });
    } catch (error) {
      console.error('Get user stats error:', error);
      res.status(500).json({ error: 'Failed to fetch user statistics' });
    }
  },

  // GET /api/users/:id/activity
  getUserActivity: async (req, res) => {
    try {
      const { id } = req.params;
      const userId = parseInt(id);
      const { limit = 20 } = req.query;

      // Check if user exists and user can access this data
      if (req.user.id !== userId && !['admin', 'project_manager'].includes(req.user.role)) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Get recent activity from various sources
      const [dailyLogs, mediaUploads, projects] = await Promise.all([
        // Recent daily logs
        prisma.dailyLog.findMany({
          where: { userId: userId },
          orderBy: { createdAt: 'desc' },
          take: parseInt(limit) / 3,
          select: {
            id: true,
            message: true,
            createdAt: true,
            project: {
              select: { title: true }
            }
          }
        }),

        // Recent media uploads
        prisma.mediaFile.findMany({
          where: { uploadedBy: userId },
          orderBy: { createdAt: 'desc' },
          take: parseInt(limit) / 3,
          select: {
            id: true,
            filename: true,
            createdAt: true,
            project: {
              select: { title: true }
            }
          }
        }),

        // Recent projects
        prisma.project.findMany({
          where: {
            OR: [
              { projectLeadId: userId },
              { teamMembers: { some: { userId: userId } } }
            ]
          },
          orderBy: { updatedAt: 'desc' },
          take: parseInt(limit) / 3,
          select: {
            id: true,
            title: true,
            status: true,
            updatedAt: true
          }
        })
      ]);

      // Format activity items
      const activity = [
        ...dailyLogs.map(log => ({
          type: 'daily_log',
          description: `Updated daily log: ${log.message?.substring(0, 50)}...`,
          createdAt: log.createdAt,
          project: log.project?.title
        })),
        ...mediaUploads.map(media => ({
          type: 'media_upload',
          description: `Uploaded file: ${media.filename}`,
          createdAt: media.createdAt,
          project: media.project?.title
        })),
        ...projects.map(project => ({
          type: 'project_update',
          description: `Updated project: ${project.title}`,
          createdAt: project.updatedAt,
          status: project.status
        }))
      ].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
       .slice(0, parseInt(limit));

      res.json(activity);
    } catch (error) {
      console.error('Get user activity error:', error);
      res.status(500).json({ error: 'Failed to fetch user activity' });
    }
  },

  // POST /api/users/:id/avatar
  uploadUserAvatar: async (req, res) => {
    try {
      const { id } = req.params;
      const userId = parseInt(id);

      // Check if user can update this profile
      if (req.user.id !== userId && !['admin', 'project_manager'].includes(req.user.role)) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Check if file was uploaded
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      // Update user with avatar path
      const avatarUrl = `/uploads/${req.file.filename}`;

      const user = await prisma.user.update({
        where: { id: userId },
        data: { avatar: avatarUrl },
        select: {
          id: true,
          name: true,
          email: true,
          avatar: true
        }
      });

      res.json({
        success: true,
        avatarUrl: user.avatar,
        message: 'Avatar uploaded successfully'
      });
    } catch (error) {
      console.error('Upload avatar error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'User not found' });
      }
      res.status(500).json({ error: 'Failed to upload avatar' });
    }
  }
};

module.exports = userController;
