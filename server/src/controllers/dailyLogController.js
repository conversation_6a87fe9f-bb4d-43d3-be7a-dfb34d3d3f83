const prisma = require('../lib/prisma');
const { hasProjectAccess, getPaginationMeta } = require('../utils/helpers');
const { notificationService } = require('../services/notificationService');

const dailyLogController = {
  getDailyLogs: async (req, res) => {
    try {
      const { page = 1, limit = 10, type, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Build where clause based on user role
      let whereClause = {};

      // Role-based access control
      switch (user.role) {
        case 'admin':
        case 'project_manager':
        case 'accountant':
        case 'store_keeper':
        case 'quantity_surveyor':
          // Can see all daily logs
          break;
        case 'project_lead':
          whereClause.project = {
            projectLeadId: user.id
          };
          break;
        case 'team_member':
          whereClause.project = {
            teamMembers: {
              some: { userId: user.id }
            }
          };
          break;
        case 'client':
          whereClause.project = {
            clientId: user.id
          };
          break;
        default:
          return res.status(403).json({ error: 'Access denied' });
      }

      // Add type filter
      if (type) {
        whereClause.type = type;
      }

      const [dailyLogs, total] = await Promise.all([
        prisma.dailyLog.findMany({
          where: whereClause,
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            user: {
              select: { id: true, name: true, role: true }
            },
            project: {
              select: { id: true, title: true }
            },
            attachments: {
              select: {
                id: true,
                filename: true,
                originalName: true,
                mimeType: true,
                size: true,
                createdAt: true
              }
            },
            replies: {
              include: {
                user: {
                  select: { id: true, name: true, role: true }
                },
                attachments: {
                  select: {
                    id: true,
                    filename: true,
                    originalName: true,
                    mimeType: true,
                    size: true,
                    createdAt: true
                  }
                }
              },
              orderBy: { createdAt: 'asc' }
            }
          }
        }),
        prisma.dailyLog.count({ where: whereClause })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        dailyLogs,
        pagination
      });
    } catch (error) {
      console.error('Get daily logs error:', error);
      res.status(500).json({ error: 'Failed to fetch daily logs' });
    }
  },

  getProjectDailyLogs: async (req, res) => {
    try {
      const { projectId } = req.params;
      const { page = 1, limit = 10, type, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Build where clause
      let whereClause = { projectId };
      if (type) {
        whereClause.type = type;
      }

      const [dailyLogs, total] = await Promise.all([
        prisma.dailyLog.findMany({
          where: whereClause,
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            user: {
              select: { id: true, name: true, role: true }
            },
            replies: {
              include: {
                user: {
                  select: { id: true, name: true, role: true }
                }
              },
              orderBy: { createdAt: 'asc' }
            }
          }
        }),
        prisma.dailyLog.count({ where: whereClause })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        dailyLogs,
        pagination
      });
    } catch (error) {
      console.error('Get project daily logs error:', error);
      res.status(500).json({ error: 'Failed to fetch project daily logs' });
    }
  },

  createDailyLog: async (req, res) => {
    try {
      const { projectId, title, message, type = 'update' } = req.body;
      const user = req.user;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Create daily log
      const dailyLog = await prisma.dailyLog.create({
        data: {
          projectId,
          userId: user.id,
          title,
          message,
          type
        },
        include: {
          user: {
            select: { id: true, name: true, role: true }
          },
          project: {
            select: { id: true, title: true }
          }
        }
      });

      // Handle file attachments if any
      if (req.files && req.files.length > 0) {
        const attachmentPromises = req.files.map(file =>
          prisma.attachment.create({
            data: {
              filename: file.filename,
              originalName: file.originalname,
              mimeType: file.mimetype,
              size: file.size,
              path: file.path,
              dailyLogId: dailyLog.id
            }
          })
        );

        await Promise.all(attachmentPromises);
      }

      // Create notification for project stakeholders with email
      await notificationService.notifyProjectStakeholders(
        projectId,
        {
          senderId: user.id,
          title: `New ${type} in ${project.title}`,
          message: `${user.name} posted: ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`,
          type: 'info',
          data: {
            dailyLogId: dailyLog.id
          }
        },
        user.id // Exclude the creator
      );

      res.status(201).json({ dailyLog });
    } catch (error) {
      console.error('Create daily log error:', error);
      res.status(500).json({ error: 'Failed to create daily log' });
    }
  },

  addReplyToDailyLog: async (req, res) => {
    try {
      const { id: dailyLogId } = req.params;
      const { message } = req.body;
      const user = req.user;

      // Get daily log and check access
      const dailyLog = await prisma.dailyLog.findUnique({
        where: { id: parseInt(dailyLogId) },
        include: {
          project: {
            include: {
              teamMembers: true
            }
          }
        }
      });

      if (!dailyLog) {
        return res.status(404).json({ error: 'Daily log not found' });
      }

      if (!hasProjectAccess(user, dailyLog.project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Create reply
      const reply = await prisma.dailyLogReply.create({
        data: {
          dailyLogId: parseInt(dailyLogId),
          userId: user.id,
          message
        },
        include: {
          user: {
            select: { id: true, name: true, role: true }
          }
        }
      });

      // Handle file attachments if any
      if (req.files && req.files.length > 0) {
        const attachmentPromises = req.files.map(file =>
          prisma.attachment.create({
            data: {
              filename: file.filename,
              originalName: file.originalname,
              mimeType: file.mimetype,
              size: file.size,
              path: file.path,
              dailyLogReplyId: reply.id
            }
          })
        );

        await Promise.all(attachmentPromises);
      }

      // Create notification for original poster and other stakeholders with email
      const stakeholders = [
        dailyLog.userId,
        dailyLog.project.projectLeadId,
        dailyLog.project.clientId,
        ...dailyLog.project.teamMembers.map(tm => tm.userId)
      ].filter(id => id && id !== user.id); // Exclude the replier and null values

      if (stakeholders.length > 0) {
        await notificationService.notifyUsers(stakeholders, {
          senderId: user.id,
          title: `Reply to daily log in ${dailyLog.project.title}`,
          message: `${user.name} replied: ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`,
          type: 'info',
          data: {
            projectId: dailyLog.projectId,
            dailyLogId: dailyLog.id,
            replyId: reply.id
          }
        });
      }

      res.status(201).json({ reply });
    } catch (error) {
      console.error('Add reply to daily log error:', error);
      res.status(500).json({ error: 'Failed to add reply to daily log' });
    }
  },

  deleteDailyLog: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get daily log and check access
      const dailyLog = await prisma.dailyLog.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: {
            include: {
              teamMembers: true
            }
          }
        }
      });

      if (!dailyLog) {
        return res.status(404).json({ error: 'Daily log not found' });
      }

      // Only the creator, project lead, or admin can delete
      if (dailyLog.userId !== user.id &&
          dailyLog.project.projectLeadId !== user.id &&
          user.role !== 'admin') {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Delete daily log (cascade will handle replies)
      await prisma.dailyLog.delete({
        where: { id: parseInt(id) }
      });

      res.json({ message: 'Daily log deleted successfully' });
    } catch (error) {
      console.error('Delete daily log error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Daily log not found' });
      }
      res.status(500).json({ error: 'Failed to delete daily log' });
    }
  },

  deleteDailyLogReply: async (req, res) => {
    try {
      const { id: dailyLogId, replyId } = req.params;
      const user = req.user;

      // Get reply and check access
      const reply = await prisma.dailyLogReply.findUnique({
        where: { id: parseInt(replyId) },
        include: {
          dailyLog: {
            include: {
              project: {
                include: {
                  teamMembers: true
                }
              }
            }
          }
        }
      });

      if (!reply) {
        return res.status(404).json({ error: 'Reply not found' });
      }

      // Check if reply belongs to the specified daily log
      if (reply.dailyLogId !== parseInt(dailyLogId)) {
        return res.status(400).json({ error: 'Reply does not belong to this daily log' });
      }

      // Only the creator, project lead, or admin can delete
      if (reply.userId !== user.id &&
          reply.dailyLog.project.projectLeadId !== user.id &&
          user.role !== 'admin') {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Delete reply
      await prisma.dailyLogReply.delete({
        where: { id: parseInt(replyId) }
      });

      res.json({ message: 'Reply deleted successfully' });
    } catch (error) {
      console.error('Delete daily log reply error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Reply not found' });
      }
      res.status(500).json({ error: 'Failed to delete reply' });
    }
  }
};

module.exports = dailyLogController;
