const prisma = require('../lib/prisma');
const { generateProjectId, hasProjectAccess, getPaginationMeta } = require('../utils/helpers');
const { notificationService } = require('../services/notificationService');

const projectController = {
  // GET /api/projects
  getProjects: async (req, res) => {
    try {
      const { page = 1, limit = 10, status, category, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Build where clause based on user role and filters
      let whereClause = {};

      // Role-based access control
      switch (user.role) {
        case 'admin':
        case 'project_manager':
        case 'accountant':
        case 'store_keeper':
        case 'quantity_surveyor':
          // Can see all projects
          break;
        case 'project_lead':
          whereClause.projectLeadId = user.id;
          break;
        case 'team_member':
          whereClause.teamMembers = {
            some: { userId: user.id }
          };
          break;
        case 'client':
          whereClause.clientId = user.id;
          break;
        default:
          return res.status(403).json({ error: 'Access denied' });
      }

      // Add filters
      if (status) {
        whereClause.status = status;
      }
      if (category) {
        whereClause.category = category;
      }

      const [projects, total] = await Promise.all([
        prisma.project.findMany({
          where: whereClause,
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            projectLead: {
              select: { id: true, name: true, email: true }
            },
            client: {
              select: { id: true, name: true, company: true }
            },
            teamMembers: {
              include: {
                user: {
                  select: { id: true, name: true, role: true }
                }
              }
            },
            phases: {
              select: { id: true, name: true, status: true, progress: true }
            },
            _count: {
              select: {
                dailyLogs: true,
                budgets: true,
                materialRequests: true
              }
            }
          }
        }),
        prisma.project.count({ where: whereClause })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        projects,
        pagination
      });
    } catch (error) {
      console.error('Get projects error:', error);
      res.status(500).json({ error: 'Failed to fetch projects' });
    }
  },

  // GET /api/projects/:id
  getProjectById: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      const project = await prisma.project.findUnique({
        where: { id },
        include: {
          projectLead: {
            select: { id: true, name: true, email: true, phone: true }
          },
          client: {
            select: { id: true, name: true, email: true, company: true, phone: true }
          },
          teamMembers: {
            include: {
              user: {
                select: { id: true, name: true, email: true, role: true }
              }
            }
          },
          phases: {
            include: {
              tasks: {
                orderBy: { createdAt: 'asc' }
              }
            },
            orderBy: { id: 'asc' }
          },
          projectFinancials: true,
          dailyLogs: {
            take: 5,
            orderBy: { createdAt: 'desc' },
            include: {
              user: {
                select: { id: true, name: true }
              }
            }
          },
          budgets: {
            take: 3,
            orderBy: { createdAt: 'desc' },
            include: {
              user: {
                select: { id: true, name: true }
              }
            }
          },
          _count: {
            select: {
              dailyLogs: true,
              budgets: true,
              materialRequests: true,
              mediaFiles: true
            }
          }
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      // Check access permissions
      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      res.json({ project });
    } catch (error) {
      console.error('Get project by ID error:', error);
      res.status(500).json({ error: 'Failed to fetch project' });
    }
  },

  // POST /api/projects
  createProject: async (req, res) => {
    try {
      const {
        title,
        description,
        location,
        category,
        startDate,
        endDate,
        budget,
        projectLeadId,
        clientId,
        teamMemberIds = []
      } = req.body;

      // Generate unique project ID
      let projectId;
      let isUnique = false;
      while (!isUnique) {
        projectId = generateProjectId();
        const existing = await prisma.project.findUnique({
          where: { id: projectId }
        });
        if (!existing) isUnique = true;
      }

      // Verify project lead exists and has correct role
      const projectLead = await prisma.user.findUnique({
        where: { id: projectLeadId }
      });

      if (!projectLead || !['project_lead', 'admin', 'project_manager'].includes(projectLead.role)) {
        return res.status(400).json({
          error: 'Invalid project lead. Must be a user with project_lead, admin, or project_manager role.'
        });
      }

      // Verify client exists if provided
      if (clientId) {
        const client = await prisma.user.findUnique({
          where: { id: clientId }
        });

        if (!client || client.role !== 'client') {
          return res.status(400).json({
            error: 'Invalid client. Must be a user with client role.'
          });
        }
      }

      // Create project with default phases
      const project = await prisma.project.create({
        data: {
          id: projectId,
          title,
          description,
          location,
          category,
          startDate: startDate ? new Date(startDate) : null,
          endDate: endDate ? new Date(endDate) : null,
          budget: budget ? parseFloat(budget) : null,
          projectLeadId,
          clientId,
          phases: {
            create: [
              {
                name: 'Foundation',
                customLabel: 'Foundation Works',
                status: 'not_started',
                progress: 0
              },
              {
                name: 'Structure',
                customLabel: 'Structural Works',
                status: 'not_started',
                progress: 0
              },
              {
                name: 'Finishing',
                customLabel: 'Finishing Works',
                status: 'not_started',
                progress: 0
              }
            ]
          },
          teamMembers: {
            create: teamMemberIds.map(userId => ({ userId }))
          },
          projectFinancials: {
            create: {
              totalProjectValue: budget ? parseFloat(budget) : 0,
              initialPayment: 0,
              outstandingBalance: budget ? parseFloat(budget) : 0
            }
          }
        },
        include: {
          projectLead: {
            select: { id: true, name: true, email: true }
          },
          client: {
            select: { id: true, name: true, company: true }
          },
          teamMembers: {
            include: {
              user: {
                select: { id: true, name: true, role: true }
              }
            }
          },
          phases: true,
          projectFinancials: true
        }
      });

      // Create notifications for project stakeholders
      const stakeholders = [
        projectLeadId,
        clientId,
        ...teamMemberIds
      ].filter(id => id && id !== req.user.id); // Exclude the creator

      if (stakeholders.length > 0) {
        await notificationService.notifyUsers(stakeholders, {
          senderId: req.user.id,
          title: `New Project Created: ${title}`,
          message: `${req.user.name} created a new project "${title}". You have been assigned to this project.`,
          type: 'info',
          data: {
            projectId: project.id,
            action: 'project_created'
          }
        });
      }

      // Notify admins about new project
      await notificationService.notifyUsersByRole(
        'admin',
        {
          senderId: req.user.id,
          title: `New Project Created: ${title}`,
          message: `${req.user.name} created a new project "${title}" with budget ₦${budget ? parseFloat(budget).toLocaleString() : 'TBD'}`,
          type: 'info',
          data: {
            projectId: project.id,
            action: 'project_created'
          }
        },
        req.user.id // Exclude the creator if they're admin
      );

      res.status(201).json({ project });
    } catch (error) {
      console.error('Create project error:', error);
      res.status(500).json({ error: 'Failed to create project' });
    }
  },

  // PUT /api/projects/:id
  updateProject: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;
      const updateData = req.body;

      // Get existing project
      const existingProject = await prisma.project.findUnique({
        where: { id },
        include: {
          teamMembers: true
        }
      });

      if (!existingProject) {
        return res.status(404).json({ error: 'Project not found' });
      }

      // Check permissions - only admin, project manager, project lead, or accountant can update
      if (!['admin', 'project_manager', 'accountant'].includes(user.role) &&
          existingProject.projectLeadId !== user.id) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Prepare update data
      const updateFields = {};

      // Basic fields
      if (updateData.title) updateFields.title = updateData.title;
      if (updateData.description !== undefined) updateFields.description = updateData.description;
      if (updateData.location !== undefined) updateFields.location = updateData.location;
      if (updateData.category !== undefined) updateFields.category = updateData.category;
      if (updateData.status) updateFields.status = updateData.status;
      if (updateData.startDate) updateFields.startDate = new Date(updateData.startDate);
      if (updateData.endDate) updateFields.endDate = new Date(updateData.endDate);
      if (updateData.budget !== undefined) updateFields.budget = updateData.budget ? parseFloat(updateData.budget) : null;
      if (updateData.progress !== undefined) updateFields.progress = parseInt(updateData.progress);

      // Role-based field restrictions
      if (updateData.projectLeadId && ['admin'].includes(user.role)) {
        updateFields.projectLeadId = updateData.projectLeadId;
      }
      if (updateData.clientId && ['admin'].includes(user.role)) {
        updateFields.clientId = updateData.clientId;
      }

      // Handle team member updates
      let teamMemberOperations = {};
      if (updateData.teamMemberIds && ['admin', 'project_lead'].includes(user.role)) {
        // Delete existing team members and create new ones
        teamMemberOperations = {
          teamMembers: {
            deleteMany: {},
            create: updateData.teamMemberIds.map(userId => ({ userId }))
          }
        };
      }

      // Update project
      const project = await prisma.project.update({
        where: { id },
        data: {
          ...updateFields,
          ...teamMemberOperations
        },
        include: {
          projectLead: {
            select: { id: true, name: true, email: true }
          },
          client: {
            select: { id: true, name: true, company: true }
          },
          teamMembers: {
            include: {
              user: {
                select: { id: true, name: true, role: true }
              }
            }
          },
          phases: true
        }
      });

      // Create notifications for project updates
      const updateMessages = [];

      // Check what was updated and create appropriate messages
      if (updateData.status && updateData.status !== existingProject.status) {
        updateMessages.push(`Status changed to ${updateData.status}`);
      }

      if (updateData.title && updateData.title !== existingProject.title) {
        updateMessages.push(`Title updated to "${updateData.title}"`);
      }

      if (updateData.budget && parseFloat(updateData.budget) !== existingProject.budget) {
        updateMessages.push(`Budget updated to ₦${parseFloat(updateData.budget).toLocaleString()}`);
      }

      if (updateData.endDate && new Date(updateData.endDate).getTime() !== new Date(existingProject.endDate).getTime()) {
        updateMessages.push(`End date updated to ${new Date(updateData.endDate).toLocaleDateString()}`);
      }

      // Notify about team member changes
      if (updateData.teamMemberIds) {
        const currentTeamIds = existingProject.teamMembers.map(tm => tm.userId);
        const newTeamIds = updateData.teamMemberIds;

        const addedMembers = newTeamIds.filter(id => !currentTeamIds.includes(id));
        const removedMembers = currentTeamIds.filter(id => !newTeamIds.includes(id));

        if (addedMembers.length > 0) {
          updateMessages.push(`${addedMembers.length} team member(s) added`);

          // Notify newly added team members
          await notificationService.notifyUsers(addedMembers, {
            senderId: user.id,
            title: `Added to Project: ${project.title}`,
            message: `${user.name} added you to the project "${project.title}". Welcome to the team!`,
            type: 'info',
            data: {
              projectId: id,
              action: 'team_member_added'
            }
          });
        }

        if (removedMembers.length > 0) {
          updateMessages.push(`${removedMembers.length} team member(s) removed`);

          // Notify removed team members
          await notificationService.notifyUsers(removedMembers, {
            senderId: user.id,
            title: `Removed from Project: ${existingProject.title}`,
            message: `${user.name} removed you from the project "${existingProject.title}".`,
            type: 'warning',
            data: {
              projectId: id,
              action: 'team_member_removed'
            }
          });
        }
      }

      // Notify project stakeholders about general updates (if there are any changes)
      if (updateMessages.length > 0) {
        await notificationService.notifyProjectStakeholders(
          id,
          {
            senderId: user.id,
            title: `Project Updated: ${project.title}`,
            message: `${user.name} updated the project: ${updateMessages.join(', ')}`,
            type: 'info',
            data: {
              action: 'project_updated',
              changes: updateMessages
            }
          },
          user.id // Exclude the updater
        );
      }

      res.json({ project });
    } catch (error) {
      console.error('Update project error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Project not found' });
      }
      res.status(500).json({ error: 'Failed to update project' });
    }
  },

  // DELETE /api/projects/:id
  deleteProject: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get existing project
      const existingProject = await prisma.project.findUnique({
        where: { id }
      });

      if (!existingProject) {
        return res.status(404).json({ error: 'Project not found' });
      }

      // Only admin can delete projects
      if (user.role !== 'admin') {
        return res.status(403).json({ error: 'Only administrators can delete projects' });
      }

      // Delete project (cascade will handle related records)
      await prisma.project.delete({
        where: { id }
      });

      res.json({ message: 'Project deleted successfully' });
    } catch (error) {
      console.error('Delete project error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Project not found' });
      }
      res.status(500).json({ error: 'Failed to delete project' });
    }
  },

  // GET /api/projects/:id/financials
  getProjectFinancials: async (req, res) => {
    try {
      const { id: projectId } = req.params;
      const user = req.user;

      // Get project with financials
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          projectFinancials: true,
          teamMembers: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      // Check access permissions
      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      res.json({
        projectFinancials: project.projectFinancials || {
          totalProjectValue: project.budget || 0,
          initialPayment: 0,
          outstandingBalance: project.budget || 0,
          installments: []
        }
      });
    } catch (error) {
      console.error('Get project financials error:', error);
      res.status(500).json({ error: 'Failed to fetch project financials' });
    }
  },

  // PUT /api/projects/:id/financials
  updateProjectFinancials: async (req, res) => {
    try {
      const { id: projectId } = req.params;
      const { totalProjectValue, initialPayment, outstandingBalance, installments } = req.body;
      const user = req.user;

      // Get project
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true,
          projectFinancials: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      // Check permissions - only admin, accountant, or project lead can update financials
      const canUpdate = (
        user.role === 'admin' ||
        user.role === 'accountant' ||
        (user.role === 'project_lead' && project.projectLeadId === user.id)
      );

      if (!canUpdate) {
        return res.status(403).json({ error: 'Access denied. Only admin, accountant, or project lead can update financials.' });
      }

      // Update or create project financials
      const financialData = {
        totalProjectValue: parseFloat(totalProjectValue) || 0,
        initialPayment: parseFloat(initialPayment) || 0,
        outstandingBalance: parseFloat(outstandingBalance) || 0,
        installments: installments || []
      };

      let updatedFinancials;
      if (project.projectFinancials) {
        // Update existing financials
        updatedFinancials = await prisma.projectFinancial.update({
          where: { projectId },
          data: financialData
        });
      } else {
        // Create new financials
        updatedFinancials = await prisma.projectFinancial.create({
          data: {
            projectId,
            ...financialData
          }
        });
      }

      res.json({ projectFinancials: updatedFinancials });
    } catch (error) {
      console.error('Update project financials error:', error);
      res.status(500).json({ error: 'Failed to update project financials' });
    }
  },

  // POST /api/projects/:id/phases - Add new phase to project
  addProjectPhase: async (req, res) => {
    try {
      const { id: projectId } = req.params;
      const user = req.user;
      const { name, customLabel, startDate, endDate } = req.body;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Only project lead, admin, or team members can add phases
      if (!['admin', 'project_lead', 'team_member'].includes(user.role)) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Validate required fields
      if (!name) {
        return res.status(400).json({ error: 'Phase name is required' });
      }

      // Check if phase with same name already exists
      const existingPhase = await prisma.projectPhase.findFirst({
        where: {
          projectId,
          OR: [
            { name },
            { customLabel: name }
          ]
        }
      });

      if (existingPhase) {
        return res.status(400).json({ error: 'A phase with this name already exists' });
      }

      // Create new phase
      const phase = await prisma.projectPhase.create({
        data: {
          projectId,
          name,
          customLabel: customLabel || name,
          status: 'not_started',
          progress: 0,
          startDate: startDate ? new Date(startDate) : null,
          endDate: endDate ? new Date(endDate) : null
        },
        include: {
          tasks: true
        }
      });

      // Create notification for project stakeholders
      await notificationService.notifyProjectStakeholders(
        projectId,
        {
          senderId: user.id,
          title: `New Phase Added - ${project.title}`,
          message: `${user.name} added a new phase "${phase.customLabel || phase.name}" to the project`,
          type: 'info',
          data: {
            phaseId: phase.id,
            action: 'phase_added'
          }
        },
        user.id // Exclude the creator
      );

      res.status(201).json({ phase });
    } catch (error) {
      console.error('Add project phase error:', error);
      res.status(500).json({ error: 'Failed to add project phase' });
    }
  },

  // DELETE /api/projects/:id/phases/:phaseId - Delete project phase
  deleteProjectPhase: async (req, res) => {
    try {
      const { id: projectId, phaseId } = req.params;
      const user = req.user;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Only project lead or admin can delete phases
      if (!['admin', 'project_lead'].includes(user.role)) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Get phase details before deletion
      const phase = await prisma.projectPhase.findUnique({
        where: {
          id: parseInt(phaseId),
          projectId: projectId
        }
      });

      if (!phase) {
        return res.status(404).json({ error: 'Phase not found' });
      }

      // Delete the phase (tasks will be deleted automatically due to cascade)
      await prisma.projectPhase.delete({
        where: {
          id: parseInt(phaseId),
          projectId: projectId
        }
      });

      // Recalculate project progress based on remaining phases
      const remainingPhases = await prisma.projectPhase.findMany({
        where: { projectId }
      });

      let averageProgress = 0;
      if (remainingPhases.length > 0) {
        const totalProgress = remainingPhases.reduce((sum, p) => sum + p.progress, 0);
        averageProgress = Math.round(totalProgress / remainingPhases.length);
      }

      // Update project progress
      await prisma.project.update({
        where: { id: projectId },
        data: { progress: averageProgress }
      });

      // Create notification for project stakeholders
      await notificationService.notifyProjectStakeholders(
        projectId,
        {
          senderId: user.id,
          title: `Phase Deleted - ${project.title}`,
          message: `${user.name} deleted the phase "${phase.customLabel || phase.name}" from the project`,
          type: 'warning',
          data: {
            phaseId: parseInt(phaseId),
            action: 'phase_deleted'
          }
        },
        user.id // Exclude the deleter
      );

      res.json({ message: 'Phase deleted successfully' });
    } catch (error) {
      console.error('Delete project phase error:', error);
      res.status(500).json({ error: 'Failed to delete project phase' });
    }
  },

  // PUT /api/projects/:id/phases/:phaseId
  updateProjectPhase: async (req, res) => {
    try {
      const { id: projectId, phaseId } = req.params;
      const user = req.user;
      const { status, progress, customLabel, startDate, endDate } = req.body;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Only project lead, admin, or team members can update phases
      if (!['admin', 'project_lead', 'team_member'].includes(user.role)) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Get existing phase data for comparison (for notifications)
      const existingPhase = await prisma.projectPhase.findUnique({
        where: {
          id: parseInt(phaseId),
          projectId: projectId
        }
      });

      if (!existingPhase) {
        return res.status(404).json({ error: 'Phase not found' });
      }

      // Update phase
      const updateData = {};
      if (status) updateData.status = status;
      if (progress !== undefined) updateData.progress = parseInt(progress);
      if (customLabel !== undefined) updateData.customLabel = customLabel;
      if (startDate) updateData.startDate = new Date(startDate);
      if (endDate) updateData.endDate = new Date(endDate);

      const phase = await prisma.projectPhase.update({
        where: {
          id: parseInt(phaseId),
          projectId: projectId
        },
        data: updateData,
        include: {
          tasks: true
        }
      });

      // Recalculate project progress based on all phases
      const allPhases = await prisma.projectPhase.findMany({
        where: { projectId }
      });

      const totalProgress = allPhases.reduce((sum, p) => sum + p.progress, 0);
      const averageProgress = Math.round(totalProgress / allPhases.length);

      // Update project progress
      await prisma.project.update({
        where: { id: projectId },
        data: { progress: averageProgress }
      });

      // Create notifications for phase updates
      const updateMessages = [];

      if (status && status !== existingPhase.status) {
        updateMessages.push(`Status changed to ${status}`);
      }

      if (progress !== undefined && progress !== existingPhase.progress) {
        updateMessages.push(`Progress updated to ${progress}%`);
      }

      if (customLabel && customLabel !== existingPhase.customLabel) {
        updateMessages.push(`Label updated to "${customLabel}"`);
      }

      // Notify project stakeholders about phase updates
      if (updateMessages.length > 0) {
        await notificationService.notifyProjectStakeholders(
          projectId,
          {
            senderId: user.id,
            title: `Phase Updated: ${phase.customLabel || phase.name} - ${project.title}`,
            message: `${user.name} updated the "${phase.customLabel || phase.name}" phase: ${updateMessages.join(', ')}`,
            type: 'info',
            data: {
              phaseId: parseInt(phaseId),
              action: 'phase_updated',
              changes: updateMessages
            }
          },
          user.id // Exclude the updater
        );
      }

      res.json({ phase });
    } catch (error) {
      console.error('Update project phase error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Phase not found' });
      }
      res.status(500).json({ error: 'Failed to update project phase' });
    }
  },

  // POST /api/projects/:id/phases/:phaseId/tasks
  addPhaseTask: async (req, res) => {
    try {
      const { id: projectId, phaseId } = req.params;
      const user = req.user;
      const { name, assignedTo, notes, startDate, endDate, progress } = req.body;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Only project lead, admin, or team members can add tasks
      if (!['admin', 'project_lead', 'team_member'].includes(user.role)) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Verify phase exists
      const phase = await prisma.projectPhase.findFirst({
        where: {
          id: parseInt(phaseId),
          projectId: projectId
        }
      });

      if (!phase) {
        return res.status(404).json({ error: 'Phase not found' });
      }

      // Create task
      const taskData = {
        phaseId: parseInt(phaseId),
        name,
        assignedTo,
        notes,
        progress: progress ? parseInt(progress) : 0
      };

      // Add dates if provided
      if (startDate) {
        taskData.startDate = new Date(startDate);
      }
      if (endDate) {
        taskData.endDate = new Date(endDate);
      }

      const task = await prisma.phaseTask.create({
        data: taskData
      });

      // Get phase information for notification
      const phaseInfo = await prisma.projectPhase.findUnique({
        where: { id: parseInt(phaseId) },
        select: { name: true, customLabel: true }
      });

      // Notify project stakeholders about new task
      await notificationService.notifyProjectStakeholders(
        projectId,
        {
          senderId: user.id,
          title: `New Task Added: ${name} - ${project.title}`,
          message: `${user.name} added a new task "${name}" to the "${phaseInfo.customLabel || phaseInfo.name}" phase${assignedTo ? ` and assigned it to a team member` : ''}`,
          type: 'info',
          data: {
            phaseId: parseInt(phaseId),
            taskId: task.id,
            action: 'task_created'
          }
        },
        user.id // Exclude the creator
      );

      // If task is assigned to someone, notify them specifically
      if (assignedTo && assignedTo !== user.id) {
        // Check if assignedTo is a user ID (number) or user name (string)
        let assignedUserId = null;

        if (typeof assignedTo === 'number' || !isNaN(parseInt(assignedTo))) {
          // assignedTo is a user ID
          assignedUserId = parseInt(assignedTo);
        } else {
          // assignedTo is a user name, find the user ID
          const assignedUser = await prisma.user.findFirst({
            where: { name: assignedTo },
            select: { id: true }
          });
          assignedUserId = assignedUser?.id;
        }

        // Only send notification if we found a valid user ID
        if (assignedUserId && assignedUserId !== user.id) {
          await notificationService.notifyUsers([assignedUserId], {
            senderId: user.id,
            title: `Task Assigned: ${name} - ${project.title}`,
            message: `${user.name} assigned you a new task "${name}" in the "${phaseInfo.customLabel || phaseInfo.name}" phase`,
            type: 'info',
            data: {
              phaseId: parseInt(phaseId),
              taskId: task.id,
              action: 'task_assigned'
            }
          });
        }
      }

      res.status(201).json({ task });
    } catch (error) {
      console.error('Add phase task error:', error);
      res.status(500).json({ error: 'Failed to add phase task' });
    }
  },

  // PUT /api/projects/:id/phases/:phaseId/tasks/:taskId
  updatePhaseTask: async (req, res) => {
    try {
      const { id: projectId, phaseId, taskId } = req.params;
      const user = req.user;
      const { name, assignedTo, progress, notes, startDate, endDate } = req.body;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Get existing task for comparison
      const existingTask = await prisma.phaseTask.findUnique({
        where: { id: parseInt(taskId) },
        include: {
          phase: {
            select: { name: true, customLabel: true }
          }
        }
      });

      if (!existingTask) {
        return res.status(404).json({ error: 'Task not found' });
      }

      // Prepare update data
      const updateData = {};
      if (name) updateData.name = name;
      if (assignedTo !== undefined) updateData.assignedTo = assignedTo;
      if (progress !== undefined) updateData.progress = parseInt(progress);
      if (notes !== undefined) updateData.notes = notes;
      if (startDate !== undefined) {
        updateData.startDate = startDate ? new Date(startDate) : null;
      }
      if (endDate !== undefined) {
        updateData.endDate = endDate ? new Date(endDate) : null;
      }

      // Update task
      const task = await prisma.phaseTask.update({
        where: {
          id: parseInt(taskId),
          phase: {
            id: parseInt(phaseId),
            projectId: projectId
          }
        },
        data: updateData
      });

      // Recalculate phase progress based on tasks
      const phaseTasks = await prisma.phaseTask.findMany({
        where: { phaseId: parseInt(phaseId) }
      });

      if (phaseTasks.length > 0) {
        const totalTaskProgress = phaseTasks.reduce((sum, t) => sum + t.progress, 0);
        const averageTaskProgress = Math.round(totalTaskProgress / phaseTasks.length);

        await prisma.projectPhase.update({
          where: { id: parseInt(phaseId) },
          data: { progress: averageTaskProgress }
        });
      }

      // Create notifications for task updates
      const updateMessages = [];

      if (name && name !== existingTask.name) {
        updateMessages.push(`Name changed to "${name}"`);
      }

      if (progress !== undefined && progress !== existingTask.progress) {
        updateMessages.push(`Progress updated to ${progress}%`);
      }

      if (assignedTo !== undefined && assignedTo !== existingTask.assignedTo) {
        if (assignedTo) {
          updateMessages.push(`Assigned to team member`);

          // Notify the newly assigned person
          if (assignedTo !== user.id) {
            // Check if assignedTo is a user ID (number) or user name (string)
            let assignedUserId = null;

            if (typeof assignedTo === 'number' || !isNaN(parseInt(assignedTo))) {
              // assignedTo is a user ID
              assignedUserId = parseInt(assignedTo);
            } else {
              // assignedTo is a user name, find the user ID
              const assignedUser = await prisma.user.findFirst({
                where: { name: assignedTo },
                select: { id: true }
              });
              assignedUserId = assignedUser?.id;
            }

            // Only send notification if we found a valid user ID
            if (assignedUserId && assignedUserId !== user.id) {
              await notificationService.notifyUsers([assignedUserId], {
                senderId: user.id,
                title: `Task Assigned: ${task.name} - ${project.title}`,
                message: `${user.name} assigned you the task "${task.name}" in the "${existingTask.phase.customLabel || existingTask.phase.name}" phase`,
                type: 'info',
                data: {
                  phaseId: parseInt(phaseId),
                  taskId: parseInt(taskId),
                  action: 'task_assigned'
                }
              });
            }
          }
        } else {
          updateMessages.push(`Assignment removed`);
        }

        // Notify the previously assigned person if different
        if (existingTask.assignedTo && existingTask.assignedTo !== assignedTo && existingTask.assignedTo !== user.id) {
          // Check if existingTask.assignedTo is a user ID (number) or user name (string)
          let previousAssignedUserId = null;

          if (typeof existingTask.assignedTo === 'number' || !isNaN(parseInt(existingTask.assignedTo))) {
            // existingTask.assignedTo is a user ID
            previousAssignedUserId = parseInt(existingTask.assignedTo);
          } else {
            // existingTask.assignedTo is a user name, find the user ID
            const previousAssignedUser = await prisma.user.findFirst({
              where: { name: existingTask.assignedTo },
              select: { id: true }
            });
            previousAssignedUserId = previousAssignedUser?.id;
          }

          // Only send notification if we found a valid user ID
          if (previousAssignedUserId && previousAssignedUserId !== user.id) {
            await notificationService.notifyUsers([previousAssignedUserId], {
              senderId: user.id,
              title: `Task Assignment Changed: ${task.name} - ${project.title}`,
              message: `${user.name} changed the assignment for task "${task.name}" in the "${existingTask.phase.customLabel || existingTask.phase.name}" phase`,
              type: 'warning',
              data: {
                phaseId: parseInt(phaseId),
                taskId: parseInt(taskId),
                action: 'task_assignment_changed'
              }
            });
          }
        }
      }

      // Notify project stakeholders about task updates (if there are significant changes)
      if (updateMessages.length > 0) {
        await notificationService.notifyProjectStakeholders(
          projectId,
          {
            senderId: user.id,
            title: `Task Updated: ${task.name} - ${project.title}`,
            message: `${user.name} updated the task "${task.name}" in the "${existingTask.phase.customLabel || existingTask.phase.name}" phase: ${updateMessages.join(', ')}`,
            type: 'info',
            data: {
              phaseId: parseInt(phaseId),
              taskId: parseInt(taskId),
              action: 'task_updated',
              changes: updateMessages
            }
          },
          user.id // Exclude the updater
        );
      }

      res.json({ task });
    } catch (error) {
      console.error('Update phase task error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Task not found' });
      }
      res.status(500).json({ error: 'Failed to update phase task' });
    }
  },

  // DELETE /api/projects/:id/phases/:phaseId/tasks/:taskId
  deletePhaseTask: async (req, res) => {
    try {
      const { id: projectId, phaseId, taskId } = req.params;
      const user = req.user;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Only project lead or admin can delete tasks
      if (!['admin', 'project_lead'].includes(user.role)) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Delete task
      await prisma.phaseTask.delete({
        where: {
          id: parseInt(taskId),
          phase: {
            id: parseInt(phaseId),
            projectId: projectId
          }
        }
      });

      res.json({ message: 'Task deleted successfully' });
    } catch (error) {
      console.error('Delete phase task error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Task not found' });
      }
      res.status(500).json({ error: 'Failed to delete phase task' });
    }
  }
};

module.exports = projectController;
