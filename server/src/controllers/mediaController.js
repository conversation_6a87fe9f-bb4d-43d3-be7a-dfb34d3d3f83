const prisma = require('../lib/prisma');
const { hasProjectAccess, getPaginationMeta } = require('../utils/helpers');
const { notificationService } = require('../services/notificationService');
const path = require('path');
const fs = require('fs').promises;

const mediaController = {
  getMediaFiles: async (req, res) => {
    try {
      const { page = 1, limit = 10, projectId, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Build where clause based on user role
      let whereClause = {};

      // Role-based access control
      switch (user.role) {
        case 'admin':
        case 'project_manager':
        case 'store_keeper':
          // Can see all media files
          break;
        case 'project_lead':
          whereClause.OR = [
            { uploadedBy: user.id },
            {
              project: {
                projectLeadId: user.id
              }
            }
          ];
          break;
        case 'team_member':
          whereClause.OR = [
            { uploadedBy: user.id },
            {
              project: {
                teamMembers: {
                  some: { userId: user.id }
                }
              }
            }
          ];
          break;
        case 'client':
          whereClause.OR = [
            { uploadedBy: user.id },
            {
              project: {
                clientId: user.id
              }
            }
          ];
          break;
        default:
          return res.status(403).json({ error: 'Access denied' });
      }

      // Add project filter
      if (projectId) {
        whereClause.projectId = projectId;
      }

      const [mediaFiles, total] = await Promise.all([
        prisma.mediaFile.findMany({
          where: whereClause,
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            user: {
              select: { id: true, name: true, role: true }
            },
            project: {
              select: { id: true, title: true }
            }
          }
        }),
        prisma.mediaFile.count({ where: whereClause })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        mediaFiles,
        pagination
      });
    } catch (error) {
      console.error('Get media files error:', error);
      res.status(500).json({ error: 'Failed to fetch media files' });
    }
  },

  getProjectMediaFiles: async (req, res) => {
    try {
      const { projectId } = req.params;
      const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      const [mediaFiles, total] = await Promise.all([
        prisma.mediaFile.findMany({
          where: { projectId },
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            user: {
              select: { id: true, name: true, role: true }
            }
          }
        }),
        prisma.mediaFile.count({ where: { projectId } })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        mediaFiles,
        pagination
      });
    } catch (error) {
      console.error('Get project media files error:', error);
      res.status(500).json({ error: 'Failed to fetch project media files' });
    }
  },

  uploadMediaFile: async (req, res) => {
    try {
      const { projectId, description } = req.body;
      const user = req.user;
      const file = req.file;

      if (!file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      // Check project access if projectId is provided
      if (projectId) {
        const project = await prisma.project.findUnique({
          where: { id: projectId },
          include: {
            teamMembers: true
          }
        });

        if (!project) {
          return res.status(404).json({ error: 'Project not found' });
        }

        if (!hasProjectAccess(user, project)) {
          return res.status(403).json({ error: 'Access denied to this project' });
        }
      }

      // Create media file record
      const mediaFile = await prisma.mediaFile.create({
        data: {
          projectId: projectId || null,
          uploadedBy: user.id,
          filename: file.filename,
          originalName: file.originalname,
          mimeType: file.mimetype,
          size: file.size,
          path: file.path,
          description: description || null
        },
        include: {
          user: {
            select: { id: true, name: true, role: true }
          },
          project: {
            select: { id: true, title: true }
          }
        }
      });

      // Create notification for project stakeholders if it's a project file with email
      if (projectId && mediaFile.project) {
        await notificationService.notifyProjectStakeholders(
          projectId,
          {
            senderId: user.id,
            title: `New File Uploaded - ${mediaFile.project.title}`,
            message: `${user.name} uploaded ${file.originalname}`,
            type: 'info',
            data: {
              mediaFileId: mediaFile.id
            }
          },
          user.id // Exclude the uploader
        );
      }

      res.status(201).json({ mediaFile });
    } catch (error) {
      console.error('Upload media file error:', error);
      res.status(500).json({ error: 'Failed to upload media file' });
    }
  },

  deleteMediaFile: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get existing media file
      const existingFile = await prisma.mediaFile.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: {
            include: {
              teamMembers: true
            }
          }
        }
      });

      if (!existingFile) {
        return res.status(404).json({ error: 'Media file not found' });
      }

      // Check permissions
      const canDelete = (
        user.role === 'admin' ||
        existingFile.uploadedBy === user.id ||
        (existingFile.project && existingFile.project.projectLeadId === user.id)
      );

      if (!canDelete) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Delete physical file
      try {
        await fs.unlink(existingFile.path);
      } catch (fileError) {
        console.warn('Failed to delete physical file:', fileError);
        // Continue with database deletion even if file deletion fails
      }

      // Delete database record
      await prisma.mediaFile.delete({
        where: { id: parseInt(id) }
      });

      res.json({ message: 'Media file deleted successfully' });
    } catch (error) {
      console.error('Delete media file error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Media file not found' });
      }
      res.status(500).json({ error: 'Failed to delete media file' });
    }
  },

  downloadMediaFile: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get media file with project info
      const mediaFile = await prisma.mediaFile.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: {
            include: {
              teamMembers: true
            }
          }
        }
      });

      if (!mediaFile) {
        return res.status(404).json({ error: 'Media file not found' });
      }

      // Check access permissions
      const hasAccess = (
        user.role === 'admin' ||
        mediaFile.uploadedBy === user.id ||
        (mediaFile.project && hasProjectAccess(user, mediaFile.project))
      );

      if (!hasAccess) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Check if file exists
      try {
        await fs.access(mediaFile.path);
      } catch (error) {
        return res.status(404).json({ error: 'File not found on server' });
      }

      // Set headers for download
      res.setHeader('Content-Disposition', `attachment; filename="${mediaFile.originalName}"`);
      res.setHeader('Content-Type', mediaFile.mimeType);

      // Send file
      res.sendFile(path.resolve(mediaFile.path));
    } catch (error) {
      console.error('Download media file error:', error);
      res.status(500).json({ error: 'Failed to download media file' });
    }
  },

  viewMediaFile: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user; // User is already authenticated by middleware

      // Get media file with project info
      const mediaFile = await prisma.mediaFile.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: {
            include: {
              teamMembers: true
            }
          }
        }
      });

      if (!mediaFile) {
        return res.status(404).json({ error: 'Media file not found' });
      }

      // Check access permissions
      const hasAccess = (
        user.role === 'admin' ||
        mediaFile.uploadedBy === user.id ||
        (mediaFile.project && hasProjectAccess(user, mediaFile.project))
      );

      if (!hasAccess) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Check if file exists
      try {
        await fs.access(mediaFile.path);
      } catch (error) {
        return res.status(404).json({ error: 'File not found on server' });
      }

      // Set headers for inline viewing
      res.setHeader('Content-Type', mediaFile.mimeType);
      res.setHeader('Content-Disposition', `inline; filename="${mediaFile.originalName}"`);

      // Send file
      res.sendFile(path.resolve(mediaFile.path));
    } catch (error) {
      console.error('View media file error:', error);
      res.status(500).json({ error: 'Failed to view media file' });
    }
  }
};

module.exports = mediaController;
