const { PrismaClient } = require('@prisma/client');
const path = require('path');
const fs = require('fs');

const prisma = new PrismaClient();

const attachmentController = {
  // GET /api/attachments/:id - Download attachment
  downloadAttachment: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get attachment with related daily log
      const attachment = await prisma.attachment.findUnique({
        where: { id: parseInt(id) },
        include: {
          dailyLog: {
            include: {
              project: {
                include: {
                  teamMembers: true
                }
              }
            }
          },
          dailyLogReply: {
            include: {
              dailyLog: {
                include: {
                  project: {
                    include: {
                      teamMembers: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!attachment) {
        return res.status(404).json({ error: 'Attachment not found' });
      }

      // Get the project from either daily log or reply
      const project = attachment.dailyLog?.project || attachment.dailyLogReply?.dailyLog?.project;
      
      if (!project) {
        return res.status(404).json({ error: 'Associated project not found' });
      }

      // Check access permissions
      const hasAccess = (
        user.role === 'admin' ||
        user.role === 'project_lead' ||
        user.role === 'team_member' ||
        user.role === 'accountant' ||
        user.role === 'store_keeper' ||
        user.role === 'quantity_surveyor' ||
        (user.role === 'client' && project.clientId === user.id)
      );

      if (!hasAccess) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Check if file exists
      const filePath = path.join(__dirname, '../../uploads', attachment.filename);
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ error: 'File not found on server' });
      }

      // Set appropriate headers
      res.setHeader('Content-Disposition', `attachment; filename="${attachment.originalName}"`);
      res.setHeader('Content-Type', attachment.mimeType);

      // Stream the file
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

    } catch (error) {
      console.error('Download attachment error:', error);
      res.status(500).json({ error: 'Failed to download attachment' });
    }
  },

  // GET /api/attachments/:id/view - View attachment (for images/PDFs)
  viewAttachment: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get attachment with related daily log
      const attachment = await prisma.attachment.findUnique({
        where: { id: parseInt(id) },
        include: {
          dailyLog: {
            include: {
              project: {
                include: {
                  teamMembers: true
                }
              }
            }
          },
          dailyLogReply: {
            include: {
              dailyLog: {
                include: {
                  project: {
                    include: {
                      teamMembers: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!attachment) {
        return res.status(404).json({ error: 'Attachment not found' });
      }

      // Get the project from either daily log or reply
      const project = attachment.dailyLog?.project || attachment.dailyLogReply?.dailyLog?.project;
      
      if (!project) {
        return res.status(404).json({ error: 'Associated project not found' });
      }

      // Check access permissions
      const hasAccess = (
        user.role === 'admin' ||
        user.role === 'project_lead' ||
        user.role === 'team_member' ||
        user.role === 'accountant' ||
        user.role === 'store_keeper' ||
        user.role === 'quantity_surveyor' ||
        (user.role === 'client' && project.clientId === user.id)
      );

      if (!hasAccess) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Check if file exists
      const filePath = path.join(__dirname, '../../uploads', attachment.filename);
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ error: 'File not found on server' });
      }

      // Set appropriate headers for viewing
      res.setHeader('Content-Type', attachment.mimeType);
      res.setHeader('Content-Disposition', `inline; filename="${attachment.originalName}"`);

      // Stream the file
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

    } catch (error) {
      console.error('View attachment error:', error);
      res.status(500).json({ error: 'Failed to view attachment' });
    }
  },

  // DELETE /api/attachments/:id - Delete attachment
  deleteAttachment: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get attachment with related data
      const attachment = await prisma.attachment.findUnique({
        where: { id: parseInt(id) },
        include: {
          dailyLog: {
            include: {
              project: true
            }
          },
          dailyLogReply: {
            include: {
              dailyLog: {
                include: {
                  project: true
                }
              },
              user: true
            }
          }
        }
      });

      if (!attachment) {
        return res.status(404).json({ error: 'Attachment not found' });
      }

      // Check permissions - only admin or the creator can delete
      const canDelete = (
        user.role === 'admin' ||
        (attachment.dailyLog && attachment.dailyLog.userId === user.id) ||
        (attachment.dailyLogReply && attachment.dailyLogReply.userId === user.id)
      );

      if (!canDelete) {
        return res.status(403).json({ error: 'Access denied. Only admin or the creator can delete attachments.' });
      }

      // Delete file from filesystem
      const filePath = path.join(__dirname, '../../uploads', attachment.filename);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      // Delete from database
      await prisma.attachment.delete({
        where: { id: parseInt(id) }
      });

      res.json({ message: 'Attachment deleted successfully' });

    } catch (error) {
      console.error('Delete attachment error:', error);
      res.status(500).json({ error: 'Failed to delete attachment' });
    }
  }
};

module.exports = attachmentController;
