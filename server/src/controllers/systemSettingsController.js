const prisma = require('../lib/prisma');
const crypto = require('crypto');

// Simple encryption for sensitive settings (in production, use proper encryption service)
const ENCRYPTION_KEY = process.env.SETTINGS_ENCRYPTION_KEY || 'your-32-character-secret-key-here';
const ALGORITHM = 'aes-256-cbc';

// Generate a proper 32-byte key from the encryption key
const getKey = () => {
  return crypto.createHash('sha256').update(ENCRYPTION_KEY).digest();
};

const encrypt = (text) => {
  if (!text) return null;
  try {
    const key = getKey();
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error('Encryption error:', error);
    return null;
  }
};

const decrypt = (encryptedText) => {
  if (!encryptedText) return null;

  console.log(`Controller: Attempting to decrypt value: ${encryptedText.substring(0, 20)}...`);

  try {
    const key = getKey();
    const parts = encryptedText.split(':');

    if (parts.length === 2) {
      // New format with IV
      console.log('Controller: Using new format decryption with IV');
      const iv = Buffer.from(parts[0], 'hex');
      const encryptedData = parts[1];
      const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      // Remove any padding characters (both null bytes and PKCS7 padding)
      decrypted = decrypted.replace(/[\x00-\x1F]+$/g, '').trim();

      console.log(`Controller: Decrypted result preview: ${decrypted.substring(0, 10)}...`);
      console.log(`Controller: Decrypted length: ${decrypted.length}`);
      console.log('Controller: New format decryption successful');
      return decrypted;
    } else {
      // Old format - just return null since createDecipher is deprecated
      console.log('Controller: Old format detected, but createDecipher is deprecated');
      console.log('Controller: Encrypted text might need to be re-encrypted with new format');
      return null;
    }
  } catch (error) {
    console.error('Controller: Decryption failed:', error.message);
    return null;
  }
};

const systemSettingsController = {
  // GET /api/admin/settings - Get all system settings (admin only)
  getSystemSettings: async (req, res) => {
    try {
      const settings = await prisma.systemSettings.findMany({
        orderBy: [
          { category: 'asc' },
          { key: 'asc' }
        ]
      });

      // Decrypt encrypted values for admin viewing
      const decryptedSettings = settings.map(setting => ({
        ...setting,
        value: setting.encrypted && setting.value ? decrypt(setting.value) : setting.value
      }));

      // Group by category
      const groupedSettings = decryptedSettings.reduce((acc, setting) => {
        if (!acc[setting.category]) {
          acc[setting.category] = [];
        }
        acc[setting.category].push(setting);
        return acc;
      }, {});

      res.json({ settings: groupedSettings });
    } catch (error) {
      console.error('Get system settings error:', error);
      res.status(500).json({ error: 'Failed to get system settings' });
    }
  },

  // GET /api/admin/settings/:category - Get settings by category
  getSettingsByCategory: async (req, res) => {
    try {
      const { category } = req.params;

      const settings = await prisma.systemSettings.findMany({
        where: { category },
        orderBy: { key: 'asc' }
      });

      // Decrypt encrypted values
      const decryptedSettings = settings.map(setting => ({
        ...setting,
        value: setting.encrypted && setting.value ? decrypt(setting.value) : setting.value
      }));

      res.json({ settings: decryptedSettings });
    } catch (error) {
      console.error('Get settings by category error:', error);
      res.status(500).json({ error: 'Failed to get settings' });
    }
  },

  // PUT /api/admin/settings - Update system settings (admin only)
  updateSystemSettings: async (req, res) => {
    try {
      const { settings } = req.body;
      const userId = req.user.id;

      const updatedSettings = [];

      for (const setting of settings) {
        const { key, value, encrypted = false, description, category = 'general' } = setting;

        // Encrypt sensitive values
        const finalValue = encrypted && value ? encrypt(value) : value;

        const updatedSetting = await prisma.systemSettings.upsert({
          where: { key },
          update: {
            value: finalValue,
            encrypted,
            description,
            category,
            updatedBy: userId,
            updatedAt: new Date()
          },
          create: {
            key,
            value: finalValue,
            encrypted,
            description,
            category,
            createdBy: userId,
            updatedBy: userId
          }
        });

        updatedSettings.push({
          ...updatedSetting,
          value: encrypted && updatedSetting.value ? decrypt(updatedSetting.value) : updatedSetting.value
        });
      }

      res.json({ 
        success: true, 
        settings: updatedSettings,
        message: 'System settings updated successfully'
      });
    } catch (error) {
      console.error('Update system settings error:', error);
      res.status(500).json({ error: 'Failed to update system settings' });
    }
  },

  // GET /api/admin/settings/zeptomail - Get ZeptoMail settings specifically
  getZeptomailSettings: async (req, res) => {
    try {
      const zeptomailSettings = await prisma.systemSettings.findMany({
        where: {
          category: 'email',
          key: {
            in: ['zeptomail_api_key', 'zeptomail_from_email', 'zeptomail_from_name', 'zeptomail_test_email']
          }
        }
      });

      const settings = {};
      zeptomailSettings.forEach(setting => {
        const value = setting.encrypted && setting.value ? decrypt(setting.value) : setting.value;
        settings[setting.key] = value;
      });

      console.log('ZeptoMail settings retrieved:', {
        hasApiKey: !!settings.zeptomail_api_key,
        fromEmail: settings.zeptomail_from_email,
        fromName: settings.zeptomail_from_name,
        testEmail: settings.zeptomail_test_email
      });

      res.json({
        success: true,
        settings,
        message: 'ZeptoMail settings retrieved successfully'
      });
    } catch (error) {
      console.error('Get ZeptoMail settings error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get ZeptoMail settings',
        details: error.message
      });
    }
  },

  // PUT /api/admin/settings/zeptomail - Update ZeptoMail settings specifically
  updateZeptomailSettings: async (req, res) => {
    try {
      const { apiKey, fromEmail, fromName, testEmail } = req.body;
      const userId = req.user.id;

      console.log('Updating ZeptoMail settings:', {
        hasApiKey: !!apiKey,
        apiKeyLength: apiKey ? apiKey.length : 0,
        fromEmail,
        fromName,
        testEmail
      });

      // Test encrypt/decrypt cycle for API key
      if (apiKey) {
        const encrypted = encrypt(apiKey);
        const decrypted = decrypt(encrypted);
        console.log('Encrypt/Decrypt test:', {
          original: apiKey.substring(0, 10) + '...',
          encrypted: encrypted ? encrypted.substring(0, 20) + '...' : 'null',
          decrypted: decrypted ? decrypted.substring(0, 10) + '...' : 'null',
          matches: apiKey === decrypted
        });
      }

      const settingsToUpdate = [
        {
          key: 'zeptomail_api_key',
          value: apiKey,
          encrypted: true,
          description: 'ZeptoMail API Key for email sending',
          category: 'email'
        },
        {
          key: 'zeptomail_from_email',
          value: fromEmail,
          encrypted: false,
          description: 'Default from email address',
          category: 'email'
        },
        {
          key: 'zeptomail_from_name',
          value: fromName,
          encrypted: false,
          description: 'Default from name for emails',
          category: 'email'
        },
        {
          key: 'zeptomail_test_email',
          value: testEmail,
          encrypted: false,
          description: 'Test email address for email configuration testing',
          category: 'email'
        }
      ];

      const updatedSettings = [];

      for (const setting of settingsToUpdate) {
        if (setting.value) { // Only update if value is provided
          const finalValue = setting.encrypted ? encrypt(setting.value) : setting.value;

          const updatedSetting = await prisma.systemSettings.upsert({
            where: { key: setting.key },
            update: {
              value: finalValue,
              encrypted: setting.encrypted,
              description: setting.description,
              category: setting.category,
              updatedBy: userId,
              updatedAt: new Date()
            },
            create: {
              key: setting.key,
              value: finalValue,
              encrypted: setting.encrypted,
              description: setting.description,
              category: setting.category,
              createdBy: userId,
              updatedBy: userId
            }
          });

          updatedSettings.push({
            ...updatedSetting,
            value: setting.encrypted ? decrypt(updatedSetting.value) : updatedSetting.value
          });
        }
      }

      // Clear email service cache after updating settings
      try {
        const { clearSettingsCache } = require('../services/emailService');
        clearSettingsCache();
        console.log('Email service cache cleared after settings update');
      } catch (error) {
        console.error('Failed to clear email service cache:', error);
      }

      console.log('ZeptoMail settings updated successfully, cache cleared');

      res.json({
        success: true,
        settings: updatedSettings,
        message: 'ZeptoMail settings updated successfully'
      });
    } catch (error) {
      console.error('Update ZeptoMail settings error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update ZeptoMail settings',
        details: error.message
      });
    }
  },

  // DELETE /api/admin/settings/:key - Delete a system setting
  deleteSetting: async (req, res) => {
    try {
      const { key } = req.params;

      await prisma.systemSettings.delete({
        where: { key }
      });

      res.json({ 
        success: true, 
        message: 'Setting deleted successfully'
      });
    } catch (error) {
      console.error('Delete setting error:', error);
      res.status(500).json({ error: 'Failed to delete setting' });
    }
  }
};

module.exports = systemSettingsController;
