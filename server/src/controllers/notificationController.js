const prisma = require('../lib/prisma');
const { getPaginationMeta } = require('../utils/helpers');

const notificationController = {
  getUserNotifications: async (req, res) => {
    try {
      const { page = 1, limit = 20, isRead, type, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Build where clause
      let whereClause = { userId: user.id };

      if (isRead !== undefined) {
        whereClause.isRead = isRead === 'true';
      }

      if (type) {
        whereClause.type = type;
      }

      const [notifications, total, unreadCount] = await Promise.all([
        prisma.notification.findMany({
          where: whereClause,
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            sender: {
              select: { id: true, name: true, role: true }
            }
          }
        }),
        prisma.notification.count({ where: whereClause }),
        prisma.notification.count({
          where: {
            userId: user.id,
            isRead: false
          }
        })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        notifications,
        pagination,
        unreadCount
      });
    } catch (error) {
      console.error('Get user notifications error:', error);
      res.status(500).json({ error: 'Failed to fetch user notifications' });
    }
  },

  markNotificationAsRead: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get notification and verify ownership
      const notification = await prisma.notification.findUnique({
        where: { id: parseInt(id) }
      });

      if (!notification) {
        return res.status(404).json({ error: 'Notification not found' });
      }

      if (notification.userId !== user.id) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Mark as read
      const updatedNotification = await prisma.notification.update({
        where: { id: parseInt(id) },
        data: { isRead: true },
        include: {
          sender: {
            select: { id: true, name: true, role: true }
          }
        }
      });

      res.json({ notification: updatedNotification });
    } catch (error) {
      console.error('Mark notification as read error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Notification not found' });
      }
      res.status(500).json({ error: 'Failed to mark notification as read' });
    }
  },

  markAllNotificationsAsRead: async (req, res) => {
    try {
      const user = req.user;

      // Mark all user's notifications as read
      const result = await prisma.notification.updateMany({
        where: {
          userId: user.id,
          isRead: false
        },
        data: { isRead: true }
      });

      res.json({
        message: 'All notifications marked as read',
        updatedCount: result.count
      });
    } catch (error) {
      console.error('Mark all notifications as read error:', error);
      res.status(500).json({ error: 'Failed to mark all notifications as read' });
    }
  },

  deleteNotification: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get notification and verify ownership
      const notification = await prisma.notification.findUnique({
        where: { id: parseInt(id) }
      });

      if (!notification) {
        return res.status(404).json({ error: 'Notification not found' });
      }

      if (notification.userId !== user.id) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Delete notification
      await prisma.notification.delete({
        where: { id: parseInt(id) }
      });

      res.json({ message: 'Notification deleted successfully' });
    } catch (error) {
      console.error('Delete notification error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Notification not found' });
      }
      res.status(500).json({ error: 'Failed to delete notification' });
    }
  },

  // GET /api/notifications/settings - Get user notification preferences
  getNotificationSettings: async (req, res) => {
    try {
      const userId = req.user.id;

      let settings = await prisma.notificationSettings.findUnique({
        where: { userId }
      });

      // Create default settings if none exist
      if (!settings) {
        settings = await prisma.notificationSettings.create({
          data: {
            userId,
            emailNotifications: true,
            pushNotifications: false,
            projectUpdates: true,
            dailyDigest: false,
            weeklyReport: true,
            systemAlerts: true
          }
        });
      }

      res.json({ settings });
    } catch (error) {
      console.error('Get notification settings error:', error);
      res.status(500).json({ error: 'Failed to get notification settings' });
    }
  },

  // PUT /api/notifications/settings - Update user notification preferences
  updateNotificationSettings: async (req, res) => {
    try {
      const userId = req.user.id;
      const {
        emailNotifications,
        pushNotifications,
        projectUpdates,
        dailyDigest,
        weeklyReport,
        systemAlerts
      } = req.body;

      const settings = await prisma.notificationSettings.upsert({
        where: { userId },
        update: {
          emailNotifications,
          pushNotifications,
          projectUpdates,
          dailyDigest,
          weeklyReport,
          systemAlerts,
          updatedAt: new Date()
        },
        create: {
          userId,
          emailNotifications,
          pushNotifications,
          projectUpdates,
          dailyDigest,
          weeklyReport,
          systemAlerts
        }
      });

      res.json({
        success: true,
        settings,
        message: 'Notification settings updated successfully'
      });
    } catch (error) {
      console.error('Update notification settings error:', error);
      res.status(500).json({ error: 'Failed to update notification settings' });
    }
  },

  // POST /api/notifications/test-email - Send test email
  testEmailSettings: async (req, res) => {
    try {
      const { testEmail } = req.body;
      const user = req.user;

      console.log(`Test email request from user ${user.id} (${user.name}) to ${testEmail}`);

      if (!testEmail) {
        console.log('Test email failed: No email address provided');
        return res.status(400).json({
          success: false,
          error: 'Test email address is required'
        });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(testEmail)) {
        console.log(`Test email failed: Invalid email format - ${testEmail}`);
        return res.status(400).json({
          success: false,
          error: 'Please provide a valid email address'
        });
      }

      // Import email service dynamically to avoid circular dependencies
      const { sendEmail } = require('../services/emailService');

      console.log(`Attempting to send test email to ${testEmail}...`);

      // Send test email - the email service will handle fallback to env variables
      const result = await sendEmail({
        to: testEmail,
        subject: 'Karmod Project Hub - Email Test (from ' + (process.env.NODE_ENV || 'development') + ')',
        template: 'test-email',
        data: {
          userName: user.name,
          testTime: new Date().toLocaleString(),
          environment: process.env.NODE_ENV || 'development'
        }
      });

      console.log(`Test email sent successfully to ${testEmail}:`, result);

      res.json({
        success: true,
        message: `Test email sent successfully to ${testEmail}`
      });
    } catch (error) {
      console.error('Test email error details:', {
        message: error.message,
        stack: error.stack,
        response: error.response?.data,
        status: error.response?.status
      });

      // Provide more specific error messages based on error type
      let errorMessage = 'Failed to send test email. Please check your email configuration.';
      let statusCode = 500;

      if (error.message.includes('API key not configured')) {
        errorMessage = 'ZeptoMail API key is not configured. Please configure it in the admin settings.';
        statusCode = 400;
      } else if (error.message.includes('unauthorized') || error.message.includes('invalid api key')) {
        errorMessage = 'Invalid ZeptoMail API key. Please check your API key configuration.';
        statusCode = 401;
      } else if (error.message.includes('network') || error.message.includes('timeout')) {
        errorMessage = 'Network error occurred while sending email. Please try again.';
        statusCode = 503;
      }

      res.status(statusCode).json({
        success: false,
        error: errorMessage,
        details: error.message
      });
    }
  }
};

module.exports = notificationController;
