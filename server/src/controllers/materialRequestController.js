const prisma = require('../lib/prisma');
const { hasProjectAccess, getPaginationMeta } = require('../utils/helpers');
const { notificationService } = require('../services/notificationService');

const materialRequestController = {
  getMaterialRequests: async (req, res) => {
    try {
      const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Build where clause based on user role
      let whereClause = {};

      // Role-based access control
      switch (user.role) {
        case 'admin':
        case 'project_manager':
        case 'store_keeper':
          // Can see all material requests
          break;
        case 'project_lead':
          whereClause.project = {
            projectLeadId: user.id
          };
          break;
        case 'team_member':
          whereClause.OR = [
            { requestedBy: user.id },
            {
              project: {
                teamMembers: {
                  some: { userId: user.id }
                }
              }
            }
          ];
          break;
        case 'client':
          whereClause.project = {
            clientId: user.id
          };
          break;
        default:
          return res.status(403).json({ error: 'Access denied' });
      }

      // Add status filter
      if (status) {
        whereClause.status = status;
      }

      const [materialRequests, total] = await Promise.all([
        prisma.materialRequest.findMany({
          where: whereClause,
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            user: {
              select: { id: true, name: true, role: true }
            },
            project: {
              select: { id: true, title: true }
            }
          }
        }),
        prisma.materialRequest.count({ where: whereClause })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        materialRequests,
        pagination
      });
    } catch (error) {
      console.error('Get material requests error:', error);
      res.status(500).json({ error: 'Failed to fetch material requests' });
    }
  },

  createMaterialRequest: async (req, res) => {
    try {
      const { projectId, items, notes } = req.body;
      const user = req.user;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Validate items
      if (!items || !Array.isArray(items) || items.length === 0) {
        return res.status(400).json({ error: 'Material items are required' });
      }

      // Create material request
      const materialRequest = await prisma.materialRequest.create({
        data: {
          projectId,
          requestedBy: user.id,
          items,
          notes,
          status: 'pending_lead_approval'
        },
        include: {
          user: {
            select: { id: true, name: true, role: true }
          },
          project: {
            select: { id: true, title: true }
          }
        }
      });

      // Create notification for project lead
      if (project.projectLeadId !== user.id) {
        await prisma.notification.create({
          data: {
            userId: project.projectLeadId,
            senderId: user.id,
            title: `Material Request for ${project.title}`,
            message: `${user.name} submitted a material request requiring approval`,
            type: 'info',
            data: {
              projectId,
              materialRequestId: materialRequest.id
            }
          }
        });
      }

      res.status(201).json({ materialRequest });
    } catch (error) {
      console.error('Create material request error:', error);
      res.status(500).json({ error: 'Failed to create material request' });
    }
  },

  updateMaterialRequest: async (req, res) => {
    try {
      const { id } = req.params;
      const { status, storeKeeperNotes, storeStatus } = req.body;
      const user = req.user;

      // Get existing material request
      const existingRequest = await prisma.materialRequest.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: {
            include: {
              teamMembers: true
            }
          }
        }
      });

      if (!existingRequest) {
        return res.status(404).json({ error: 'Material request not found' });
      }

      // Check permissions based on current status and user role
      const updateData = {};
      let notificationData = null;

      if (status) {
        switch (status) {
          case 'approved_by_lead':
          case 'rejected_by_lead':
            // Only project lead or admin can approve/reject at lead level
            if (user.role !== 'admin' && existingRequest.project.projectLeadId !== user.id) {
              return res.status(403).json({ error: 'Only project lead can approve/reject this request' });
            }
            updateData.status = status;
            updateData.leadApprovalTimestamp = new Date();

            // Notify store keeper if approved
            if (status === 'approved_by_lead') {
              const storeKeepers = await prisma.user.findMany({
                where: { role: 'store_keeper' },
                select: { id: true }
              });

              if (storeKeepers.length > 0) {
                notificationData = {
                  recipients: storeKeepers.map(sk => sk.id),
                  title: `Material Request Approved - ${existingRequest.project.title}`,
                  message: `Project lead approved material request, awaiting store keeper action`
                };
              }
            }
            break;

          case 'approved_by_store':
          case 'rejected_by_store':
          case 'delivered':
            // Only store keeper or admin can handle store-level actions
            if (!['admin', 'store_keeper'].includes(user.role)) {
              return res.status(403).json({ error: 'Only store keeper can handle this action' });
            }
            updateData.status = status;
            updateData.storeTimestamp = new Date();

            // Notify requester and project lead
            notificationData = {
              recipients: [existingRequest.requestedBy, existingRequest.project.projectLeadId].filter(id => id !== user.id),
              title: `Material Request ${status.replace('_', ' ').toUpperCase()} - ${existingRequest.project.title}`,
              message: `Store keeper ${status.replace('_', ' ')} the material request`
            };
            break;

          default:
            return res.status(400).json({ error: 'Invalid status transition' });
        }
      }

      // Store keeper can add notes and status
      if (storeKeeperNotes !== undefined && ['admin', 'store_keeper'].includes(user.role)) {
        updateData.storeKeeperNotes = storeKeeperNotes;
      }

      if (storeStatus !== undefined && ['admin', 'store_keeper'].includes(user.role)) {
        updateData.storeStatus = storeStatus;
      }

      // Update material request
      const materialRequest = await prisma.materialRequest.update({
        where: { id: parseInt(id) },
        data: updateData,
        include: {
          user: {
            select: { id: true, name: true, role: true }
          },
          project: {
            select: { id: true, title: true }
          }
        }
      });

      // Send notifications with email
      if (notificationData && notificationData.recipients.length > 0) {
        await notificationService.notifyUsers(notificationData.recipients, {
          senderId: user.id,
          title: notificationData.title,
          message: notificationData.message,
          type: 'info',
          data: {
            projectId: existingRequest.projectId,
            materialRequestId: materialRequest.id
          }
        });
      }

      res.json({ materialRequest });
    } catch (error) {
      console.error('Update material request error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Material request not found' });
      }
      res.status(500).json({ error: 'Failed to update material request' });
    }
  },

  deleteMaterialRequest: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get existing material request
      const existingRequest = await prisma.materialRequest.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: true
        }
      });

      if (!existingRequest) {
        return res.status(404).json({ error: 'Material request not found' });
      }

      // Only the requester, project lead, or admin can delete
      if (existingRequest.requestedBy !== user.id &&
          existingRequest.project.projectLeadId !== user.id &&
          user.role !== 'admin') {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Can only delete if not yet delivered
      if (existingRequest.status === 'delivered') {
        return res.status(400).json({ error: 'Cannot delete delivered material request' });
      }

      // Delete material request
      await prisma.materialRequest.delete({
        where: { id: parseInt(id) }
      });

      res.json({ message: 'Material request deleted successfully' });
    } catch (error) {
      console.error('Delete material request error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Material request not found' });
      }
      res.status(500).json({ error: 'Failed to delete material request' });
    }
  }
};

module.exports = materialRequestController;
