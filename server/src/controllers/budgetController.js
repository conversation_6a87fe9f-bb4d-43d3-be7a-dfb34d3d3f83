const prisma = require('../lib/prisma');
const { hasProjectAccess, getPaginationMeta } = require('../utils/helpers');
const { notificationService } = require('../services/notificationService');

const budgetController = {
  getBudgets: async (req, res) => {
    try {
      const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Build where clause based on user role
      let whereClause = {};

      // Role-based access control
      switch (user.role) {
        case 'admin':
        case 'accountant':
          // Can see all budgets
          break;
        case 'project_lead':
          whereClause.project = {
            projectLeadId: user.id
          };
          break;
        case 'quantity_surveyor':
          // Can see all budgets for review
          break;
        case 'team_member':
          whereClause.project = {
            teamMembers: {
              some: { userId: user.id }
            }
          };
          break;
        case 'client':
          whereClause.project = {
            clientId: user.id
          };
          break;
        default:
          return res.status(403).json({ error: 'Access denied' });
      }

      // Add status filter
      if (status) {
        whereClause.status = status;
      }

      const [budgets, total] = await Promise.all([
        prisma.budget.findMany({
          where: whereClause,
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            user: {
              select: { id: true, name: true, role: true }
            },
            project: {
              select: { id: true, title: true }
            },
            items: {
              orderBy: { id: 'asc' }
            }
          }
        }),
        prisma.budget.count({ where: whereClause })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        budgets,
        pagination
      });
    } catch (error) {
      console.error('Get budgets error:', error);
      res.status(500).json({ error: 'Failed to fetch budgets' });
    }
  },

  getProjectBudgets: async (req, res) => {
    try {
      const { projectId } = req.params;
      const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Build where clause
      let whereClause = { projectId };
      if (status) {
        whereClause.status = status;
      }

      const [budgets, total] = await Promise.all([
        prisma.budget.findMany({
          where: whereClause,
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            user: {
              select: { id: true, name: true, role: true }
            },
            items: {
              orderBy: { id: 'asc' }
            }
          }
        }),
        prisma.budget.count({ where: whereClause })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        budgets,
        pagination
      });
    } catch (error) {
      console.error('Get project budgets error:', error);
      res.status(500).json({ error: 'Failed to fetch project budgets' });
    }
  },

  createBudget: async (req, res) => {
    try {
      const { projectId, items, remarks } = req.body;
      const user = req.user;

      // Check project access
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      if (!hasProjectAccess(user, project)) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Validate items and calculate total
      if (!items || !Array.isArray(items) || items.length === 0) {
        return res.status(400).json({ error: 'Budget items are required' });
      }

      let totalAmount = 0;
      const budgetItems = items.map(item => {
        const quantity = parseInt(item.quantity) || 0;
        const unitPrice = parseFloat(item.unitPrice) || 0;
        const total = quantity * unitPrice;
        totalAmount += total;

        return {
          description: item.description,
          quantity,
          unitPrice,
          total,
          notes: item.notes || null
        };
      });

      // Create budget with items
      // When project lead creates budget, status should be "pending_accountant"
      const initialStatus = user.role === 'project_lead' ? 'pending_accountant' : 'draft';

      const budget = await prisma.budget.create({
        data: {
          projectId,
          submittedBy: user.id,
          totalAmount,
          remarks,
          status: initialStatus,
          items: {
            create: budgetItems
          }
        },
        include: {
          user: {
            select: { id: true, name: true, role: true }
          },
          project: {
            select: { id: true, title: true }
          },
          items: true
        }
      });

      // Create notifications based on budget status and workflow
      let notificationRecipients = [];
      let notificationTitle = '';
      let notificationMessage = '';

      if (initialStatus === 'pending_accountant') {
        // When project lead creates budget, notify accountants
        const accountants = await prisma.user.findMany({
          where: { role: 'accountant' },
          select: { id: true, email: true, name: true }
        });
        notificationRecipients = accountants.map(a => a.id);
        notificationTitle = `New Budget Pending Review - ${project.title}`;
        notificationMessage = `${user.name} submitted a budget worth ₦${totalAmount.toLocaleString()} for review`;
      } else {
        // For draft status, notify project lead and admin
        notificationRecipients = [project.projectLeadId];
        const adminUsers = await prisma.user.findMany({
          where: { role: 'admin' },
          select: { id: true, email: true, name: true }
        });
        notificationRecipients.push(...adminUsers.map(u => u.id));
        notificationTitle = `New Budget Created - ${project.title}`;
        notificationMessage = `${user.name} created a budget worth ₦${totalAmount.toLocaleString()}`;
      }

      // Filter out the sender and send notifications
      const filteredRecipients = notificationRecipients.filter(id => id !== user.id);
      if (filteredRecipients.length > 0) {
        await notificationService.notifyUsers(filteredRecipients, {
          senderId: user.id,
          title: notificationTitle,
          message: notificationMessage,
          type: 'info',
          data: {
            projectId,
            budgetId: budget.id,
            action: 'budget_created',
            status: initialStatus
          }
        }); // Email notifications handled automatically by notification service
      }

      res.status(201).json({ budget });
    } catch (error) {
      console.error('Create budget error:', error);
      res.status(500).json({ error: 'Failed to create budget' });
    }
  },

  updateBudget: async (req, res) => {
    try {
      const { id } = req.params;
      const { status, remarks, items, pushedByAccountant } = req.body;
      const user = req.user;

      // Get existing budget
      const existingBudget = await prisma.budget.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: {
            include: {
              teamMembers: true
            }
          },
          items: true
        }
      });

      if (!existingBudget) {
        return res.status(404).json({ error: 'Budget not found' });
      }

      // Check permissions
      const canEdit = (
        user.role === 'admin' ||
        user.role === 'accountant' ||
        (user.role === 'project_lead' && existingBudget.project.projectLeadId === user.id) ||
        (user.role === 'quantity_surveyor')
      );

      if (!canEdit) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Prepare update data
      const updateData = {};

      // Status updates with role-based restrictions
      if (status) {
        // Project lead can only submit for review or save as draft
        if (user.role === 'project_lead' && ['pending_accountant', 'draft'].includes(status)) {
          updateData.status = status;
        }
        // Accountant can push to admin, return to lead, or reject
        else if (user.role === 'accountant' && ['pending_admin', 'revision_needed', 'rejected'].includes(status)) {
          updateData.status = status;
          if (status === 'pending_admin') {
            updateData.pushedByAccountant = true;
          } else if (status === 'revision_needed') {
            updateData.pushedByAccountant = false;
          } else if (status === 'rejected') {
            updateData.pushedByAccountant = false;
          }
        }
        // Admin can approve or reject
        else if (user.role === 'admin' && ['approved', 'rejected'].includes(status)) {
          updateData.status = status;
        }
        // Quantity surveyor can review and approve
        else if (user.role === 'quantity_surveyor' && ['pending_admin', 'revision_needed'].includes(status)) {
          updateData.status = status;
        }
      }

      if (remarks !== undefined) {
        updateData.remarks = remarks;
      }

      if (pushedByAccountant !== undefined && user.role === 'accountant') {
        updateData.pushedByAccountant = pushedByAccountant;
      }

      // Handle items update (only if budget is still in draft or pending)
      let itemsUpdate = {};
      if (items && ['draft', 'pending_approval'].includes(existingBudget.status)) {
        let totalAmount = 0;
        const budgetItems = items.map(item => {
          const quantity = parseInt(item.quantity) || 0;
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const total = quantity * unitPrice;
          totalAmount += total;

          return {
            description: item.description,
            quantity,
            unitPrice,
            total,
            notes: item.notes || null
          };
        });

        updateData.totalAmount = totalAmount;
        itemsUpdate = {
          items: {
            deleteMany: {},
            create: budgetItems
          }
        };
      }

      // Update budget
      const budget = await prisma.budget.update({
        where: { id: parseInt(id) },
        data: {
          ...updateData,
          ...itemsUpdate
        },
        include: {
          user: {
            select: { id: true, name: true, role: true }
          },
          project: {
            select: { id: true, title: true }
          },
          items: true
        }
      });

      res.json({ budget });
    } catch (error) {
      console.error('Update budget error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Budget not found' });
      }
      res.status(500).json({ error: 'Failed to update budget' });
    }
  },

  deleteBudget: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Get existing budget
      const existingBudget = await prisma.budget.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: true
        }
      });

      if (!existingBudget) {
        return res.status(404).json({ error: 'Budget not found' });
      }

      // Only admin, project manager, or accountant can delete budgets
      if (!['admin', 'project_manager', 'accountant'].includes(user.role)) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Delete budget (cascade will handle items)
      await prisma.budget.delete({
        where: { id: parseInt(id) }
      });

      res.json({ message: 'Budget deleted successfully' });
    } catch (error) {
      console.error('Delete budget error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Budget not found' });
      }
      res.status(500).json({ error: 'Failed to delete budget' });
    }
  },

  // POST /api/budgets/:id/push-to-admin - Accountant pushes budget to admin
  pushToAdmin: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      if (user.role !== 'accountant') {
        return res.status(403).json({ error: 'Only accountants can push budgets to admin' });
      }

      const budget = await prisma.budget.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: true,
          user: true,
          items: true
        }
      });

      if (!budget) {
        return res.status(404).json({ error: 'Budget not found' });
      }

      if (budget.status !== 'pending_accountant') {
        return res.status(400).json({ error: 'Budget is not in pending accountant status' });
      }

      const updatedBudget = await prisma.budget.update({
        where: { id: parseInt(id) },
        data: {
          status: 'pending_admin',
          pushedByAccountant: true
        },
        include: {
          user: {
            select: { id: true, name: true, role: true }
          },
          project: {
            select: { id: true, title: true }
          },
          items: true
        }
      });

      // Notify admins that budget is pending their approval
      const adminUsers = await prisma.user.findMany({
        where: { role: 'admin' },
        select: { id: true, email: true, name: true }
      });

      if (adminUsers.length > 0) {
        await notificationService.notifyUsers(
          adminUsers.map(a => a.id),
          {
            senderId: user.id,
            title: `Budget Pending Admin Approval - ${budget.project.title}`,
            message: `${user.name} (Accountant) has reviewed and forwarded a budget worth ₦${budget.totalAmount.toLocaleString()} for your approval`,
            type: 'info',
            data: {
              projectId: budget.projectId,
              budgetId: budget.id,
              action: 'budget_pushed_to_admin',
              status: 'pending_admin'
            }
          },
          // Email notifications handled automatically
        );
      }

      res.json({ budget: updatedBudget });
    } catch (error) {
      console.error('Push to admin error:', error);
      res.status(500).json({ error: 'Failed to push budget to admin' });
    }
  },

  // POST /api/budgets/:id/return-to-lead - Accountant returns budget to lead
  returnToLead: async (req, res) => {
    try {
      const { id } = req.params;
      const { remarks } = req.body;
      const user = req.user;

      if (user.role !== 'accountant') {
        return res.status(403).json({ error: 'Only accountants can return budgets to lead' });
      }

      const budget = await prisma.budget.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: true,
          user: true,
          items: true
        }
      });

      if (!budget) {
        return res.status(404).json({ error: 'Budget not found' });
      }

      if (budget.status !== 'pending_accountant') {
        return res.status(400).json({ error: 'Budget is not in pending accountant status' });
      }

      const updatedBudget = await prisma.budget.update({
        where: { id: parseInt(id) },
        data: {
          status: 'revision_needed',
          pushedByAccountant: false,
          remarks: remarks || budget.remarks
        },
        include: {
          user: {
            select: { id: true, name: true, role: true }
          },
          project: {
            select: { id: true, title: true }
          },
          items: true
        }
      });

      // Notify project lead that budget needs revision
      if (budget.user.id !== user.id) {
        await notificationService.notifyUsers(
          [budget.user.id], // Original budget submitter (project lead)
          {
            senderId: user.id,
            title: `Budget Revision Required - ${budget.project.title}`,
            message: `${user.name} (Accountant) has returned your budget for revision. ${remarks ? `Remarks: ${remarks}` : ''}`,
            type: 'warning',
            data: {
              projectId: budget.projectId,
              budgetId: budget.id,
              action: 'budget_returned_to_lead',
              status: 'revision_needed',
              remarks: remarks
            }
          },
          // Email notifications handled automatically
        );
      }

      res.json({ budget: updatedBudget });
    } catch (error) {
      console.error('Return to lead error:', error);
      res.status(500).json({ error: 'Failed to return budget to lead' });
    }
  },

  // POST /api/budgets/:id/approve - Admin approves budget
  approveBudget: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      if (user.role !== 'admin') {
        return res.status(403).json({ error: 'Only admins can approve budgets' });
      }

      const budget = await prisma.budget.findUnique({
        where: { id: parseInt(id) },
        include: {
          project: true,
          user: true,
          items: true
        }
      });

      if (!budget) {
        return res.status(404).json({ error: 'Budget not found' });
      }

      if (budget.status !== 'pending_admin') {
        return res.status(400).json({ error: 'Budget is not in pending admin status' });
      }

      const updatedBudget = await prisma.budget.update({
        where: { id: parseInt(id) },
        data: {
          status: 'approved'
        },
        include: {
          user: {
            select: { id: true, name: true, role: true }
          },
          project: {
            select: { id: true, title: true }
          },
          items: true
        }
      });

      // Notify project lead and accountants that budget is approved
      const notificationRecipients = [];

      // Add project lead (original submitter)
      if (budget.user.id !== user.id) {
        notificationRecipients.push(budget.user.id);
      }

      // Add accountants who were involved in the process
      const accountants = await prisma.user.findMany({
        where: { role: 'accountant' },
        select: { id: true, email: true, name: true }
      });
      notificationRecipients.push(...accountants.map(a => a.id).filter(id => id !== user.id));

      if (notificationRecipients.length > 0) {
        await notificationService.notifyUsers(
          notificationRecipients,
          {
            senderId: user.id,
            title: `Budget Approved - ${budget.project.title}`,
            message: `${user.name} (Admin) has approved the budget worth ₦${budget.totalAmount.toLocaleString()}`,
            type: 'success',
            data: {
              projectId: budget.projectId,
              budgetId: budget.id,
              action: 'budget_approved',
              status: 'approved'
            }
          },
          // Email notifications handled automatically
        );
      }

      res.json({ budget: updatedBudget });
    } catch (error) {
      console.error('Approve budget error:', error);
      res.status(500).json({ error: 'Failed to approve budget' });
    }
  }
};

module.exports = budgetController;
