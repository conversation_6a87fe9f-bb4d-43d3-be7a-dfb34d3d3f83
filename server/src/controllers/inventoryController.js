const prisma = require('../lib/prisma');
const { getPaginationMeta } = require('../utils/helpers');
const { notificationService } = require('../services/notificationService');

const inventoryController = {
  getInventoryItems: async (req, res) => {
    try {
      const { page = 1, limit = 10, category, lowStock, sortBy = 'name', sortOrder = 'asc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Check permissions
      if (!['admin', 'project_manager', 'project_lead', 'store_keeper', 'team_member'].includes(user.role)) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Build where clause
      let whereClause = {};

      if (category) {
        whereClause.category = category;
      }

      if (lowStock === 'true') {
        whereClause.quantity = {
          lte: prisma.inventoryItem.fields.lowStockThreshold
        };
      }

      const [inventoryItems, total] = await Promise.all([
        prisma.inventoryItem.findMany({
          where: whereClause,
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            _count: {
              select: {
                inventoryRequests: true
              }
            }
          }
        }),
        prisma.inventoryItem.count({ where: whereClause })
      ]);

      // Add low stock indicator
      const itemsWithStatus = inventoryItems.map(item => ({
        ...item,
        isLowStock: item.quantity <= item.lowStockThreshold
      }));

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        inventoryItems: itemsWithStatus,
        pagination
      });
    } catch (error) {
      console.error('Get inventory items error:', error);
      res.status(500).json({ error: 'Failed to fetch inventory items' });
    }
  },

  createInventoryItem: async (req, res) => {
    try {
      const { name, category, quantity, unit, lowStockThreshold = 10 } = req.body;
      const user = req.user;

      // Only admin, project manager, or store keeper can create inventory items
      if (!['admin', 'project_manager', 'store_keeper'].includes(user.role)) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Check if item already exists (case-insensitive for SQLite)
      const existingItem = await prisma.inventoryItem.findFirst({
        where: {
          name: {
            equals: name
          }
        }
      });

      if (existingItem) {
        return res.status(400).json({ error: 'Inventory item with this name already exists' });
      }

      // Create inventory item
      const inventoryItem = await prisma.inventoryItem.create({
        data: {
          name,
          category,
          quantity: parseInt(quantity),
          unit,
          lowStockThreshold: parseInt(lowStockThreshold)
        }
      });

      res.status(201).json({ inventoryItem });
    } catch (error) {
      console.error('Create inventory item error:', error);
      res.status(500).json({ error: 'Failed to create inventory item' });
    }
  },

  updateInventoryItem: async (req, res) => {
    try {
      const { id } = req.params;
      const { name, category, quantity, unit, lowStockThreshold } = req.body;
      const user = req.user;

      // Only admin, project manager, or store keeper can update inventory items
      if (!['admin', 'project_manager', 'store_keeper'].includes(user.role)) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Prepare update data
      const updateData = {};
      if (name) updateData.name = name;
      if (category) updateData.category = category;
      if (quantity !== undefined) updateData.quantity = parseInt(quantity);
      if (unit) updateData.unit = unit;
      if (lowStockThreshold !== undefined) updateData.lowStockThreshold = parseInt(lowStockThreshold);

      // Update inventory item
      const inventoryItem = await prisma.inventoryItem.update({
        where: { id: parseInt(id) },
        data: updateData
      });

      res.json({ inventoryItem });
    } catch (error) {
      console.error('Update inventory item error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Inventory item not found' });
      }
      res.status(500).json({ error: 'Failed to update inventory item' });
    }
  },

  deleteInventoryItem: async (req, res) => {
    try {
      const { id } = req.params;
      const user = req.user;

      // Only admin or project manager can delete inventory items
      if (!['admin', 'project_manager'].includes(user.role)) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Check if item has pending requests
      const pendingRequests = await prisma.inventoryRequest.count({
        where: {
          inventoryItemId: parseInt(id),
          status: 'pending_store_keeper_approval'
        }
      });

      if (pendingRequests > 0) {
        return res.status(400).json({
          error: 'Cannot delete inventory item with pending requests'
        });
      }

      // Delete inventory item
      await prisma.inventoryItem.delete({
        where: { id: parseInt(id) }
      });

      res.json({ message: 'Inventory item deleted successfully' });
    } catch (error) {
      console.error('Delete inventory item error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Inventory item not found' });
      }
      res.status(500).json({ error: 'Failed to delete inventory item' });
    }
  },

  getInventoryRequests: async (req, res) => {
    try {
      const { page = 1, limit = 10, status, projectId, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      const skip = (page - 1) * limit;
      const user = req.user;

      // Build where clause based on user role
      let whereClause = {};

      // Role-based access control
      switch (user.role) {
        case 'admin':
        case 'project_manager':
        case 'store_keeper':
          // Can see all inventory requests
          break;
        case 'project_lead':
          whereClause.project = {
            projectLeadId: user.id
          };
          break;
        case 'team_member':
          whereClause.OR = [
            { requestedBy: user.id },
            {
              project: {
                teamMembers: {
                  some: { userId: user.id }
                }
              }
            }
          ];
          break;
        default:
          return res.status(403).json({ error: 'Access denied' });
      }

      // Add filters
      if (status) {
        whereClause.status = status;
      }
      if (projectId) {
        whereClause.projectId = projectId;
      }

      const [inventoryRequests, total] = await Promise.all([
        prisma.inventoryRequest.findMany({
          where: whereClause,
          skip: parseInt(skip),
          take: parseInt(limit),
          orderBy: { [sortBy]: sortOrder },
          include: {
            inventoryItem: {
              select: { id: true, name: true, category: true, unit: true, quantity: true }
            },
            project: {
              select: { id: true, title: true }
            },
            user: {
              select: { id: true, name: true, role: true }
            }
          }
        }),
        prisma.inventoryRequest.count({ where: whereClause })
      ]);

      const pagination = getPaginationMeta(parseInt(page), parseInt(limit), total);

      res.json({
        inventoryRequests,
        pagination
      });
    } catch (error) {
      console.error('Get inventory requests error:', error);
      res.status(500).json({ error: 'Failed to fetch inventory requests' });
    }
  },

  createInventoryRequest: async (req, res) => {
    try {
      const { inventoryItemId, projectId, quantityRequested, notes } = req.body;
      const user = req.user;

      // Verify inventory item exists
      const inventoryItem = await prisma.inventoryItem.findUnique({
        where: { id: parseInt(inventoryItemId) }
      });

      if (!inventoryItem) {
        return res.status(404).json({ error: 'Inventory item not found' });
      }

      // Verify project exists and user has access
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          teamMembers: true
        }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      // Check if user has access to project
      const hasAccess = (
        user.role === 'admin' ||
        project.projectLeadId === user.id ||
        project.teamMembers.some(tm => tm.userId === user.id)
      );

      if (!hasAccess) {
        return res.status(403).json({ error: 'Access denied to this project' });
      }

      // Create inventory request
      const inventoryRequest = await prisma.inventoryRequest.create({
        data: {
          inventoryItemId: parseInt(inventoryItemId),
          projectId,
          requestedBy: user.id,
          quantityRequested: parseInt(quantityRequested),
          notes,
          status: 'pending_store_keeper_approval'
        },
        include: {
          inventoryItem: {
            select: { id: true, name: true, category: true, unit: true }
          },
          project: {
            select: { id: true, title: true }
          },
          user: {
            select: { id: true, name: true, role: true }
          }
        }
      });

      // Create notification for store keepers with email
      await notificationService.notifyUsersByRole(
        'store_keeper',
        {
          senderId: user.id,
          title: `Inventory Request - ${inventoryItem.name}`,
          message: `${user.name} requested ${quantityRequested} ${inventoryItem.unit} of ${inventoryItem.name} for ${project.title}`,
          type: 'info',
          data: {
            projectId,
            inventoryRequestId: inventoryRequest.id,
            inventoryItemId: inventoryItem.id
          }
        },
        user.id // Exclude the requester
      );

      res.status(201).json({ inventoryRequest });
    } catch (error) {
      console.error('Create inventory request error:', error);
      res.status(500).json({ error: 'Failed to create inventory request' });
    }
  },

  updateInventoryRequest: async (req, res) => {
    try {
      const { id } = req.params;
      console.log('Update inventory request - Request ID:', id);
      console.log('Update inventory request - Request body:', req.body);

      const { status, quantityApproved, storeKeeperNotes, adminNotes, projectLeadNotes } = req.body;
      const user = req.user;

      // Get existing inventory request
      const existingRequest = await prisma.inventoryRequest.findUnique({
        where: { id: parseInt(id) },
        include: {
          inventoryItem: true,
          project: true,
          user: {
            select: { id: true, name: true }
          }
        }
      });

      if (!existingRequest) {
        return res.status(404).json({ error: 'Inventory request not found' });
      }

      // Only store keeper or admin can update inventory requests
      if (!['admin', 'store_keeper'].includes(user.role)) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Prepare update data
      const updateData = {};

      if (status) {
        updateData.status = status;
        updateData.lastActionTimestamp = new Date();
      }

      if (quantityApproved !== undefined) {
        updateData.quantityApproved = parseInt(quantityApproved);
      }

      if (storeKeeperNotes !== undefined) {
        updateData.storeKeeperNotes = storeKeeperNotes;
      }

      if (adminNotes !== undefined) {
        updateData.adminNotes = adminNotes;
      }

      if (projectLeadNotes !== undefined) {
        updateData.projectLeadNotes = projectLeadNotes;
      }

      // If approving, check inventory availability and update stock
      if (status === 'approved' && quantityApproved) {
        const approvedQty = parseInt(quantityApproved);

        if (approvedQty > existingRequest.inventoryItem.quantity) {
          return res.status(400).json({
            error: `Insufficient stock. Available: ${existingRequest.inventoryItem.quantity}, Requested: ${approvedQty}`
          });
        }

        // Update inventory quantity
        await prisma.inventoryItem.update({
          where: { id: existingRequest.inventoryItemId },
          data: {
            quantity: {
              decrement: approvedQty
            }
          }
        });
      }

      // Update inventory request
      const inventoryRequest = await prisma.inventoryRequest.update({
        where: { id: parseInt(id) },
        data: updateData,
        include: {
          inventoryItem: {
            select: { id: true, name: true, category: true, unit: true }
          },
          project: {
            select: { id: true, title: true }
          },
          user: {
            select: { id: true, name: true, role: true }
          }
        }
      });

      // Create notification for requester with email
      if (status && existingRequest.requestedBy !== user.id) {
        await notificationService.notifyUsers([existingRequest.requestedBy], {
          senderId: user.id,
          title: `Inventory Request ${status.toUpperCase()} - ${existingRequest.inventoryItem.name}`,
          message: `Your request for ${existingRequest.inventoryItem.name} has been ${status}`,
          type: status === 'approved' ? 'success' : 'warning',
          data: {
            projectId: existingRequest.projectId,
            inventoryRequestId: inventoryRequest.id,
            inventoryItemId: existingRequest.inventoryItemId
          }
        });
      }

      res.json({ inventoryRequest });
    } catch (error) {
      console.error('Update inventory request error:', error);
      if (error.code === 'P2025') {
        return res.status(404).json({ error: 'Inventory request not found' });
      }
      res.status(500).json({ error: 'Failed to update inventory request' });
    }
  }
};

module.exports = inventoryController;
