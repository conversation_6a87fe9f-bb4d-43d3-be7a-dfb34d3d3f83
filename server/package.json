{"name": "karmod-project-hub-api", "version": "1.0.0", "description": "Backend API for Karmod Project Hub - Part of unified full-stack application", "main": "src/server.js", "private": true, "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js"}, "keywords": ["construction", "project-management", "api", "nodejs", "express"], "author": "Karmod Nigeria", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "note": "This is part of a unified full-stack application. All dependencies are managed in the root package.json. Run 'pnpm install' from the root directory only."}