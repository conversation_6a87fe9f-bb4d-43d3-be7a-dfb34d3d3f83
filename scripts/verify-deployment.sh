#!/bin/bash

# Deployment Verification Script
# Verifies that the deployment is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
FRONTEND_URL=${FRONTEND_URL:-"http://localhost"}
API_URL=${API_URL:-"http://localhost:3001/api"}
MAX_RETRIES=30
RETRY_INTERVAL=10

echo -e "${BLUE}🔍 Starting deployment verification...${NC}"

# Function to check service health
check_service_health() {
    local service_name=$1
    local url=$2
    local retries=0
    
    echo -e "${BLUE}🌐 Checking $service_name at $url...${NC}"
    
    while [ $retries -lt $MAX_RETRIES ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is healthy${NC}"
            return 0
        else
            echo -e "${YELLOW}⏳ $service_name not ready, retrying in ${RETRY_INTERVAL}s... (${retries}/${MAX_RETRIES})${NC}"
            sleep $RETRY_INTERVAL
            ((retries++))
        fi
    done
    
    echo -e "${RED}❌ $service_name failed health check after $MAX_RETRIES attempts${NC}"
    return 1
}

# Function to check API endpoints
check_api_endpoints() {
    echo -e "${BLUE}🔗 Testing API endpoints...${NC}"
    
    # Health check endpoint
    if curl -f -s "${API_URL}/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Health endpoint responding${NC}"
    else
        echo -e "${RED}❌ Health endpoint not responding${NC}"
        return 1
    fi
    
    # Test authentication endpoint (should return 401 without credentials)
    local auth_status=$(curl -s -o /dev/null -w "%{http_code}" "${API_URL}/auth/me")
    if [ "$auth_status" = "401" ]; then
        echo -e "${GREEN}✅ Authentication endpoint responding correctly${NC}"
    else
        echo -e "${RED}❌ Authentication endpoint not responding correctly (got $auth_status)${NC}"
        return 1
    fi
    
    # Test projects endpoint (should return 401 without credentials)
    local projects_status=$(curl -s -o /dev/null -w "%{http_code}" "${API_URL}/projects")
    if [ "$projects_status" = "401" ]; then
        echo -e "${GREEN}✅ Projects endpoint responding correctly${NC}"
    else
        echo -e "${RED}❌ Projects endpoint not responding correctly (got $projects_status)${NC}"
        return 1
    fi
}

# Function to check database connectivity
check_database() {
    echo -e "${BLUE}🗄️  Checking database connectivity...${NC}"
    
    # Use docker-compose to run a database check
    if docker-compose exec -T backend node -e "
        const { PrismaClient } = require('@prisma/client');
        const prisma = new PrismaClient();
        prisma.\$connect()
          .then(() => {
            console.log('Database connection successful');
            return prisma.\$disconnect();
          })
          .catch((error) => {
            console.error('Database connection failed:', error);
            process.exit(1);
          });
    " > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Database connectivity verified${NC}"
    else
        echo -e "${RED}❌ Database connectivity failed${NC}"
        return 1
    fi
}

# Function to check Docker containers
check_containers() {
    echo -e "${BLUE}🐳 Checking Docker containers...${NC}"
    
    local required_services=("database" "backend" "frontend")
    
    for service in "${required_services[@]}"; do
        if docker-compose ps "$service" | grep -q "Up"; then
            echo -e "${GREEN}✅ $service container is running${NC}"
        else
            echo -e "${RED}❌ $service container is not running${NC}"
            docker-compose ps "$service"
            return 1
        fi
    done
}

# Function to check resource usage
check_resources() {
    echo -e "${BLUE}📊 Checking resource usage...${NC}"
    
    # Check disk space
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 90 ]; then
        echo -e "${GREEN}✅ Disk usage: ${disk_usage}%${NC}"
    else
        echo -e "${YELLOW}⚠️  High disk usage: ${disk_usage}%${NC}"
    fi
    
    # Check memory usage
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$memory_usage" -lt 90 ]; then
        echo -e "${GREEN}✅ Memory usage: ${memory_usage}%${NC}"
    else
        echo -e "${YELLOW}⚠️  High memory usage: ${memory_usage}%${NC}"
    fi
}

# Function to check logs for errors
check_logs() {
    echo -e "${BLUE}📝 Checking recent logs for errors...${NC}"
    
    # Check backend logs for errors in the last 5 minutes
    local error_count=$(docker-compose logs --since=5m backend 2>&1 | grep -i error | wc -l)
    if [ "$error_count" -eq 0 ]; then
        echo -e "${GREEN}✅ No recent errors in backend logs${NC}"
    else
        echo -e "${YELLOW}⚠️  Found $error_count error(s) in recent backend logs${NC}"
        docker-compose logs --since=5m backend | grep -i error | tail -5
    fi
    
    # Check frontend logs for errors
    local frontend_error_count=$(docker-compose logs --since=5m frontend 2>&1 | grep -i error | wc -l)
    if [ "$frontend_error_count" -eq 0 ]; then
        echo -e "${GREEN}✅ No recent errors in frontend logs${NC}"
    else
        echo -e "${YELLOW}⚠️  Found $frontend_error_count error(s) in recent frontend logs${NC}"
        docker-compose logs --since=5m frontend | grep -i error | tail -5
    fi
}

# Function to run performance tests
run_performance_tests() {
    echo -e "${BLUE}⚡ Running basic performance tests...${NC}"
    
    # Test API response time
    local api_response_time=$(curl -o /dev/null -s -w "%{time_total}" "${API_URL}/health")
    if (( $(echo "$api_response_time < 2.0" | bc -l) )); then
        echo -e "${GREEN}✅ API response time: ${api_response_time}s${NC}"
    else
        echo -e "${YELLOW}⚠️  Slow API response time: ${api_response_time}s${NC}"
    fi
    
    # Test frontend response time
    local frontend_response_time=$(curl -o /dev/null -s -w "%{time_total}" "${FRONTEND_URL}/")
    if (( $(echo "$frontend_response_time < 3.0" | bc -l) )); then
        echo -e "${GREEN}✅ Frontend response time: ${frontend_response_time}s${NC}"
    else
        echo -e "${YELLOW}⚠️  Slow frontend response time: ${frontend_response_time}s${NC}"
    fi
}

# Main verification function
main() {
    local failed_checks=0
    
    echo -e "${BLUE}🎯 Starting comprehensive deployment verification...${NC}"
    echo ""
    
    # Run all checks
    check_containers || ((failed_checks++))
    echo ""
    
    check_service_health "Frontend" "$FRONTEND_URL" || ((failed_checks++))
    echo ""
    
    check_service_health "API" "${API_URL}/health" || ((failed_checks++))
    echo ""
    
    check_api_endpoints || ((failed_checks++))
    echo ""
    
    check_database || ((failed_checks++))
    echo ""
    
    check_resources || ((failed_checks++))
    echo ""
    
    check_logs || ((failed_checks++))
    echo ""
    
    run_performance_tests || ((failed_checks++))
    echo ""
    
    # Summary
    if [ $failed_checks -eq 0 ]; then
        echo -e "${GREEN}🎉 All verification checks passed!${NC}"
        echo -e "${GREEN}✅ Deployment is healthy and ready for use${NC}"
        echo -e "${GREEN}🌐 Frontend: $FRONTEND_URL${NC}"
        echo -e "${GREEN}🔗 API: $API_URL${NC}"
        return 0
    else
        echo -e "${RED}❌ $failed_checks verification check(s) failed${NC}"
        echo -e "${RED}🚨 Deployment may have issues${NC}"
        return 1
    fi
}

# Run main function
main "$@"
