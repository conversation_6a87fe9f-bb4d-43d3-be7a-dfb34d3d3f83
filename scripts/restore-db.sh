#!/bin/bash

# Database Restore Script
# Restores database from backup file

set -e  # Exit on any error

# Check if backup file is provided
if [ -z "$1" ]; then
    echo "❌ ERROR: Please provide backup file path"
    echo "Usage: $0 <backup_file.sql.gz>"
    echo ""
    echo "Available backups:"
    ls -lh backups/karmod_backup_*.sql.gz 2>/dev/null || echo "No backups found"
    exit 1
fi

BACKUP_FILE="$1"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ ERROR: Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is not set"
    exit 1
fi

echo "🔄 Starting database restore..."
echo "📁 Backup file: $BACKUP_FILE"
echo "🗄️  Target database: $DATABASE_URL"

# Confirm restore operation
read -p "⚠️  This will OVERWRITE the current database. Are you sure? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Restore cancelled"
    exit 1
fi

# Create a backup of current database before restore
echo "📦 Creating backup of current database..."
CURRENT_BACKUP="backups/pre_restore_backup_$(date +%Y%m%d_%H%M%S).sql"
pg_dump "$DATABASE_URL" > "$CURRENT_BACKUP"
gzip "$CURRENT_BACKUP"
echo "✅ Current database backed up to: $CURRENT_BACKUP.gz"

# Drop and recreate database (if using local PostgreSQL)
# Note: This requires appropriate permissions
echo "🗑️  Dropping current database..."
# This step depends on your database setup and permissions

# Restore from backup
echo "🔄 Restoring database from backup..."
if [[ "$BACKUP_FILE" == *.gz ]]; then
    # Decompress and restore
    gunzip -c "$BACKUP_FILE" | psql "$DATABASE_URL"
else
    # Restore directly
    psql "$DATABASE_URL" < "$BACKUP_FILE"
fi

# Run migrations to ensure schema is up to date
echo "🚀 Running migrations..."
npx prisma migrate deploy

# Generate Prisma client
echo "⚙️  Generating Prisma client..."
npx prisma generate

# Verify database
echo "🔍 Verifying database..."
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.\$connect()
  .then(() => {
    console.log('✅ Database connection successful');
    return prisma.\$disconnect();
  })
  .catch((error) => {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  });
"

echo "✅ Database restore completed successfully!"
