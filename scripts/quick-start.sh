#!/bin/bash

# Quick Start Script for Karmod Project Hub
# Sets up the development environment quickly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Karmod Project Hub - Quick Start${NC}"
echo -e "${BLUE}====================================${NC}"
echo ""

# Check if required tools are installed
check_requirements() {
    echo -e "${BLUE}🔍 Checking requirements...${NC}"
    
    local missing_tools=()
    
    if ! command -v node &> /dev/null; then
        missing_tools+=("Node.js (v18+)")
    fi
    
    if ! command -v pnpm &> /dev/null; then
        missing_tools+=("pnpm")
    fi
    
    if ! command -v docker &> /dev/null; then
        missing_tools+=("Docker")
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        missing_tools+=("Docker Compose")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo -e "${RED}❌ Missing required tools:${NC}"
        for tool in "${missing_tools[@]}"; do
            echo -e "${RED}   - $tool${NC}"
        done
        echo ""
        echo -e "${YELLOW}Please install the missing tools and run this script again.${NC}"
        echo -e "${YELLOW}Installation guides:${NC}"
        echo -e "${YELLOW}   - Node.js: https://nodejs.org/${NC}"
        echo -e "${YELLOW}   - pnpm: npm install -g pnpm${NC}"
        echo -e "${YELLOW}   - Docker: https://docs.docker.com/get-docker/${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All requirements satisfied${NC}"
}

# Setup environment files
setup_environment() {
    echo -e "${BLUE}📋 Setting up environment files...${NC}"
    
    # Frontend environment
    if [ ! -f ".env.local" ]; then
        cp .env.frontend.example .env.local
        echo -e "${GREEN}✅ Created .env.local for frontend${NC}"
    else
        echo -e "${YELLOW}ℹ️  .env.local already exists${NC}"
    fi
    
    # Backend environment
    if [ ! -f "server/.env" ]; then
        cat > server/.env << EOF
# Backend Environment Variables - Development
NODE_ENV=development
PORT=3001
APP_NAME=Karmod Project Hub API
APP_VERSION=1.0.0

# Database Configuration
DATABASE_URL="postgresql://postgres:password@localhost:5432/karmod_db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL=debug
LOG_FILE=logs/app.log

# Security
BCRYPT_ROUNDS=10
SESSION_SECRET=your-session-secret-for-development
EOF
        echo -e "${GREEN}✅ Created server/.env for backend${NC}"
    else
        echo -e "${YELLOW}ℹ️  server/.env already exists${NC}"
    fi
}

# Install dependencies
install_dependencies() {
    echo -e "${BLUE}📦 Installing dependencies...${NC}"
    
    # Install frontend dependencies
    echo -e "${BLUE}   Installing frontend dependencies...${NC}"
    pnpm install
    
    # Install backend dependencies
    echo -e "${BLUE}   Installing backend dependencies...${NC}"
    cd server && pnpm install && cd ..
    
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# Setup database
setup_database() {
    echo -e "${BLUE}🗄️  Setting up database...${NC}"
    
    # Start PostgreSQL with Docker
    echo -e "${BLUE}   Starting PostgreSQL container...${NC}"
    docker-compose up -d database
    
    # Wait for database to be ready
    echo -e "${BLUE}   Waiting for database to be ready...${NC}"
    sleep 10
    
    # Generate Prisma client
    echo -e "${BLUE}   Generating Prisma client...${NC}"
    npx prisma generate
    
    # Run migrations
    echo -e "${BLUE}   Running database migrations...${NC}"
    npx prisma migrate dev --name init
    
    # Seed database
    echo -e "${BLUE}   Seeding database with sample data...${NC}"
    npx prisma db seed
    
    echo -e "${GREEN}✅ Database setup completed${NC}"
}

# Start development servers
start_development() {
    echo -e "${BLUE}🚀 Starting development servers...${NC}"
    
    # Create necessary directories
    mkdir -p server/uploads server/logs
    
    echo -e "${BLUE}   Starting backend server...${NC}"
    cd server && pnpm dev &
    BACKEND_PID=$!
    cd ..
    
    # Wait a moment for backend to start
    sleep 5
    
    echo -e "${BLUE}   Starting frontend development server...${NC}"
    pnpm dev &
    FRONTEND_PID=$!
    
    # Wait for servers to start
    sleep 10
    
    echo -e "${GREEN}✅ Development servers started${NC}"
    echo ""
    echo -e "${GREEN}🎉 Quick start completed successfully!${NC}"
    echo ""
    echo -e "${GREEN}📍 Your application is now running:${NC}"
    echo -e "${GREEN}   🌐 Frontend: http://localhost:5173${NC}"
    echo -e "${GREEN}   🔗 Backend API: http://localhost:3001/api${NC}"
    echo -e "${GREEN}   🗄️  Database: postgresql://postgres:password@localhost:5432/karmod_db${NC}"
    echo ""
    echo -e "${GREEN}👤 Default login credentials:${NC}"
    echo -e "${GREEN}   📧 Email: <EMAIL>${NC}"
    echo -e "${GREEN}   🔑 Password: admin123${NC}"
    echo ""
    echo -e "${YELLOW}💡 Useful commands:${NC}"
    echo -e "${YELLOW}   📊 Database Studio: npx prisma studio${NC}"
    echo -e "${YELLOW}   🔄 Reset Database: npx prisma migrate reset${NC}"
    echo -e "${YELLOW}   🛑 Stop Servers: Ctrl+C${NC}"
    echo ""
    echo -e "${BLUE}📖 For more information, check the README.md file${NC}"
    
    # Keep script running
    wait
}

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🛑 Shutting down development servers...${NC}"
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Handle script interruption
trap cleanup INT TERM

# Main function
main() {
    check_requirements
    setup_environment
    install_dependencies
    setup_database
    start_development
}

# Run main function
main "$@"
