#!/usr/bin/env node

/**
 * ZeptoMail Test Script
 * 
 * This script tests the ZeptoMail configuration and sends a test email.
 * Run this script to verify your ZeptoMail setup is working correctly.
 * 
 * Usage:
 *   node scripts/test-zeptomail.js [<EMAIL>]
 */

require('dotenv').config();
const path = require('path');

// Add the server src directory to the require path
const serverSrcPath = path.join(__dirname, '..', 'server', 'src');
process.env.NODE_PATH = serverSrcPath;
require('module').Module._initPaths();

const { zeptoMailService } = require('../server/src/utils/zeptoMailService');

async function testZeptoMail() {
  console.log('🧪 ZeptoMail Configuration Test\n');
  
  // Get test email from command line argument or use default
  const testEmail = process.argv[2] || process.env.ZEPTOMAIL_TEST_EMAIL || process.env.ZEPTO_FROM_EMAIL;
  
  if (!testEmail) {
    console.error('❌ Error: No test email provided');
    console.log('Usage: node scripts/test-zeptomail.js <EMAIL>');
    console.log('Or set ZEPTOMAIL_TEST_EMAIL in your .env file');
    process.exit(1);
  }

  console.log(`📧 Test email address: ${testEmail}\n`);

  // Step 1: Validate configuration
  console.log('1️⃣ Validating ZeptoMail configuration...');
  const validation = zeptoMailService.validateConfiguration();
  
  if (validation.valid) {
    console.log('✅ Configuration is valid');
    console.log(`   - API Key: ${validation.config.hasApiKey ? 'Configured' : 'Not configured'}`);
    console.log(`   - From Email: ${validation.config.fromEmail}`);
    console.log(`   - From Name: ${validation.config.fromName}`);
    console.log(`   - Bounce Address: ${validation.config.hasBounceAddress ? 'Configured' : 'Not configured'}`);
  } else {
    console.log('❌ Configuration has issues:');
    validation.issues.forEach(issue => {
      console.log(`   - ${issue}`);
    });
    
    if (validation.issues.some(issue => issue.includes('API key'))) {
      console.log('\n💡 To fix API key issues:');
      console.log('   - Set ZEPTOMAIL_API_KEY in your .env file, or');
      console.log('   - Set ZEPTO_API_KEY in your .env file, or');
      console.log('   - Configure via admin settings in the web interface');
    }
    
    console.log('\n⚠️  Proceeding with test anyway...');
  }

  console.log('\n2️⃣ Sending test email...');
  
  try {
    const result = await zeptoMailService.testConfiguration(testEmail);
    
    if (result.success) {
      console.log('✅ Test email sent successfully!');
      console.log(`   Message: ${result.message}`);
      if (result.data) {
        console.log(`   Response: ${JSON.stringify(result.data, null, 2)}`);
      }
      console.log(`\n📬 Check your inbox at ${testEmail} for the test email.`);
    } else {
      console.log('❌ Test email failed');
      console.log(`   Error: ${result.error}`);
      
      // Provide helpful error messages
      if (result.error.includes('API key not configured')) {
        console.log('\n💡 Solution: Configure your ZeptoMail API key');
        console.log('   1. Get your API key from ZeptoMail dashboard');
        console.log('   2. Add it to your .env file: ZEPTOMAIL_API_KEY=your-key-here');
        console.log('   3. Or configure via admin settings in the web interface');
      } else if (result.error.includes('unauthorized') || result.error.includes('401')) {
        console.log('\n💡 Solution: Check your API key');
        console.log('   - Ensure the API key is correct and not expired');
        console.log('   - Verify the API key has email sending permissions');
      } else if (result.error.includes('400')) {
        console.log('\n💡 Solution: Check email configuration');
        console.log('   - Verify the from email is authorized in ZeptoMail');
        console.log('   - Check that the test email format is valid');
      }
    }
  } catch (error) {
    console.log('❌ Unexpected error occurred');
    console.log(`   Error: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
  }

  console.log('\n🏁 Test completed');
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the test
if (require.main === module) {
  testZeptoMail().catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testZeptoMail };
