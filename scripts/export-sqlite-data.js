/**
 * Export SQLite data for migration to PostgreSQL
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

// Create Prisma client for SQLite
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./dev.db'
    }
  }
});

async function exportData() {
  try {
    console.log('🔄 Starting SQLite data export...');

    // Export all data
    const users = await prisma.user.findMany();
    console.log(`✅ Exported ${users.length} users`);

    const projects = await prisma.project.findMany({
      include: {
        phases: {
          include: {
            tasks: true
          }
        },
        teamMembers: true,
        projectFinancials: true
      }
    });
    console.log(`✅ Exported ${projects.length} projects`);

    const dailyLogs = await prisma.dailyLog.findMany({
      include: {
        replies: true,
        attachments: true
      }
    });
    console.log(`✅ Exported ${dailyLogs.length} daily logs`);

    const budgets = await prisma.budget.findMany({
      include: {
        items: true
      }
    });
    console.log(`✅ Exported ${budgets.length} budgets`);

    const materialRequests = await prisma.materialRequest.findMany();
    console.log(`✅ Exported ${materialRequests.length} material requests`);

    const inventoryItems = await prisma.inventoryItem.findMany();
    console.log(`✅ Exported ${inventoryItems.length} inventory items`);

    const inventoryRequests = await prisma.inventoryRequest.findMany();
    console.log(`✅ Exported ${inventoryRequests.length} inventory requests`);

    const mediaFiles = await prisma.mediaFile.findMany();
    console.log(`✅ Exported ${mediaFiles.length} media files`);

    const notifications = await prisma.notification.findMany();
    console.log(`✅ Exported ${notifications.length} notifications`);

    const notificationSettings = await prisma.notificationSettings.findMany();
    console.log(`✅ Exported ${notificationSettings.length} notification settings`);

    const systemSettings = await prisma.systemSettings.findMany();
    console.log(`✅ Exported ${systemSettings.length} system settings`);

    // Prepare export data
    const exportData = {
      users,
      projects,
      dailyLogs,
      budgets,
      materialRequests,
      inventoryItems,
      inventoryRequests,
      mediaFiles,
      notifications,
      notificationSettings,
      systemSettings,
      exportedAt: new Date().toISOString(),
      totalRecords: users.length + projects.length + dailyLogs.length + budgets.length + 
                   materialRequests.length + inventoryItems.length + inventoryRequests.length +
                   mediaFiles.length + notifications.length + notificationSettings.length + systemSettings.length
    };

    // Write to file
    const exportPath = path.join(__dirname, 'migration-data.json');
    fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2));

    console.log(`🎉 Data export completed successfully!`);
    console.log(`📁 Export file: ${exportPath}`);
    console.log(`📊 Total records exported: ${exportData.totalRecords}`);

  } catch (error) {
    console.error('❌ Error during data export:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run export
exportData();
