#!/bin/bash

# Database Backup Script
# Creates backups of the production database

set -e  # Exit on any error

echo "📦 Starting database backup..."

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is not set"
    exit 1
fi

# Create backups directory if it doesn't exist
mkdir -p backups

# Generate backup filename with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="backups/karmod_backup_$TIMESTAMP.sql"

# Create backup
echo "🗄️  Creating backup: $BACKUP_FILE"
pg_dump "$DATABASE_URL" > "$BACKUP_FILE"

# Compress backup
echo "🗜️  Compressing backup..."
gzip "$BACKUP_FILE"
COMPRESSED_FILE="$BACKUP_FILE.gz"

# Get file size
FILE_SIZE=$(du -h "$COMPRESSED_FILE" | cut -f1)
echo "✅ Backup created successfully: $COMPRESSED_FILE ($FILE_SIZE)"

# Clean up old backups (keep last 7 days)
echo "🧹 Cleaning up old backups..."
find backups/ -name "karmod_backup_*.sql.gz" -mtime +7 -delete

# List remaining backups
echo "📋 Available backups:"
ls -lh backups/karmod_backup_*.sql.gz 2>/dev/null || echo "No backups found"

echo "✅ Backup process completed!"
