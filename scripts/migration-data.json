{"users": [{"id": 1, "email": "<EMAIL>", "password": "$2b$08$uBgH4ko4bA5bakWP212z5ubGtF9ghxgivPrAxm6cde.fwHtKHwMCK", "role": "admin", "name": "Admin User", "phone": "08012345678", "location": "Lagos", "company": "Karmod Nigeria HQ", "bio": null, "avatar": null, "createdAt": "2025-06-27T05:51:16.720Z", "updatedAt": "2025-06-27T05:51:16.720Z"}, {"id": 2, "email": "<EMAIL>", "password": "$2b$08$B9lPRCZaEVVlgRCSuaJDneXKINYevM7RVMEapZiLBbZgYLiEZhmrS", "role": "project_lead", "name": "Project Lead Alpha", "phone": "08023456789", "location": "<PERSON>ja", "company": "Karmod Nigeria", "bio": null, "avatar": null, "createdAt": "2025-06-27T05:51:16.839Z", "updatedAt": "2025-06-27T05:51:16.839Z"}, {"id": 3, "email": "<EMAIL>", "password": "$2b$08$0UIhTGFFURZRsHeZsEkOzegD5GFQ..vK9oUx92QZHCS1wRWX47nOK", "role": "team_member", "name": "Site Engineer Bravo", "phone": "08034567890", "location": "Port Harcourt", "company": "Karmod Nigeria", "bio": null, "avatar": null, "createdAt": "2025-06-27T05:51:16.857Z", "updatedAt": "2025-06-27T05:51:16.857Z"}, {"id": 4, "email": "<EMAIL>", "password": "$2b$08$VJqV.GFzd91uILDZbZ4vUu.Snr1H3nOJeNbw/l.0RpYAZTsup5LlW", "role": "client", "name": "Client User Charlie", "phone": "***********", "location": "<PERSON><PERSON>", "company": "TechCorp Nigeria", "bio": null, "avatar": null, "createdAt": "2025-06-27T05:51:16.880Z", "updatedAt": "2025-06-27T05:51:16.880Z"}, {"id": 5, "email": "<EMAIL>", "password": "$2b$08$XkwpqCoqUohWMPwu26AdVeXNH5Dp3sBvVbUJonDtQAFc78siPa5vu", "role": "accountant", "name": "Accountant Delta", "phone": "***********", "location": "Lagos", "company": "Karmod Nigeria HQ", "bio": null, "avatar": null, "createdAt": "2025-06-27T05:51:16.916Z", "updatedAt": "2025-06-27T05:51:16.916Z"}, {"id": 6, "email": "<EMAIL>", "password": "$2b$08$GmhGgQMAlX/KzwdSJ/SEzeEc9Mst3S42OZKl0hSDvwtZ8RCzREkJO", "role": "store_keeper", "name": "Store Keeper Zulu", "phone": "***********", "location": "Lagos Warehouse", "company": "Karmod Nigeria HQ", "bio": null, "avatar": null, "createdAt": "2025-06-27T05:51:18.231Z", "updatedAt": "2025-06-27T05:51:18.231Z"}, {"id": 7, "email": "<EMAIL>", "password": "$2b$08$Ji8YJ6PV9RSBNth4jzvZUOIdrFHWZO0Z0aQCsGhYWf0zyeu9AwzFO", "role": "quantity_surveyor", "name": "Quantity Surveyor Lima", "phone": "***********", "location": "Lagos", "company": "Karmod Nigeria HQ", "bio": null, "avatar": null, "createdAt": "2025-06-27T05:51:18.603Z", "updatedAt": "2025-06-27T05:51:18.603Z"}], "projects": [{"id": "KM-2024-001", "title": "Lagos Office Complex", "description": "Modern prefabricated office building with 20 units", "location": "Victoria Island, Lagos", "category": "Commercial", "status": "in_progress", "startDate": "2024-01-15T00:00:00.000Z", "endDate": "2024-06-30T00:00:00.000Z", "budget": 15000000, "spent": 8500000, "progress": 65, "createdAt": "2025-06-27T05:51:18.896Z", "updatedAt": "2025-06-27T05:51:18.896Z", "projectLeadId": 2, "clientId": 4, "phases": [{"id": 1, "projectId": "KM-2024-001", "name": "Foundation", "customLabel": "Foundation Works", "status": "completed", "progress": 100, "startDate": null, "endDate": null, "createdAt": "2025-06-27T05:51:18.896Z", "updatedAt": "2025-06-27T05:51:18.896Z", "tasks": [{"id": 1, "phaseId": 1, "name": "Clear Site", "assignedTo": "Site Engineer Bravo", "progress": 100, "notes": "Site cleared and leveled.", "createdAt": "2025-06-27T05:51:18.896Z", "updatedAt": "2025-06-27T05:51:18.896Z"}]}, {"id": 2, "projectId": "KM-2024-001", "name": "Structure", "customLabel": "Steel Framework", "status": "in_progress", "progress": 70, "startDate": null, "endDate": null, "createdAt": "2025-06-27T05:51:18.896Z", "updatedAt": "2025-06-27T05:51:18.896Z", "tasks": [{"id": 2, "phaseId": 2, "name": "Install Steel Frame", "assignedTo": "Site Engineer Bravo", "progress": 80, "notes": "Main framework 80% complete.", "createdAt": "2025-06-27T05:51:18.896Z", "updatedAt": "2025-06-27T05:51:18.896Z"}]}, {"id": 3, "projectId": "KM-2024-001", "name": "Finishing", "customLabel": "Interior & Exterior", "status": "not_started", "progress": 0, "startDate": null, "endDate": null, "createdAt": "2025-06-27T05:51:18.896Z", "updatedAt": "2025-06-27T05:51:18.896Z", "tasks": []}], "teamMembers": [{"id": 1, "projectId": "KM-2024-001", "userId": 3, "createdAt": "2025-06-27T05:51:18.896Z"}], "projectFinancials": {"id": 1, "projectId": "KM-2024-001", "totalProjectValue": 16000000, "initialPayment": 0, "outstandingBalance": 16000000, "installments": [{"amount": 20000, "dueDate": "2025-06-28", "status": "pending"}], "createdAt": "2025-06-27T06:11:54.366Z", "updatedAt": "2025-06-27T06:17:27.025Z"}}], "dailyLogs": [{"id": 1, "projectId": "KM-2024-001", "userId": 1, "title": "Test Project", "message": "test", "type": "update", "createdAt": "2025-06-27T05:52:20.166Z", "updatedAt": "2025-06-27T05:52:20.166Z", "replies": [{"id": 1, "dailyLogId": 1, "userId": 1, "message": "Hello", "createdAt": "2025-06-27T05:52:45.284Z"}], "attachments": []}], "budgets": [], "materialRequests": [], "inventoryItems": [{"id": 1, "name": "Cement Bags (50kg)", "category": "Building Materials", "quantity": 200, "unit": "bags", "lowStockThreshold": 50, "createdAt": "2025-06-27T05:51:19.073Z", "updatedAt": "2025-06-27T05:51:19.073Z"}, {"id": 2, "name": "Steel Rods (12mm)", "category": "Metals", "quantity": 500, "unit": "lengths", "lowStockThreshold": 100, "createdAt": "2025-06-27T05:51:19.261Z", "updatedAt": "2025-06-27T05:51:19.261Z"}, {"id": 3, "name": "<PERSON>t (White Emulsion)", "category": "Finishing", "quantity": 50, "unit": "gallons", "lowStockThreshold": 10, "createdAt": "2025-06-27T05:51:19.502Z", "updatedAt": "2025-06-27T05:51:19.502Z"}, {"id": 4, "name": "Safety Helmets", "category": "Safety Gear", "quantity": 30, "unit": "pieces", "lowStockThreshold": 5, "createdAt": "2025-06-27T05:51:19.600Z", "updatedAt": "2025-06-27T05:51:19.600Z"}, {"id": 5, "name": "Cement Bags (50kg)", "category": "Building Materials", "quantity": 200, "unit": "bags", "lowStockThreshold": 50, "createdAt": "2025-06-27T06:11:08.113Z", "updatedAt": "2025-06-27T06:11:08.113Z"}, {"id": 6, "name": "Steel Rods (12mm)", "category": "Metals", "quantity": 500, "unit": "lengths", "lowStockThreshold": 100, "createdAt": "2025-06-27T06:11:08.201Z", "updatedAt": "2025-06-27T06:11:08.201Z"}, {"id": 7, "name": "<PERSON>t (White Emulsion)", "category": "Finishing", "quantity": 50, "unit": "gallons", "lowStockThreshold": 10, "createdAt": "2025-06-27T06:11:08.326Z", "updatedAt": "2025-06-27T06:11:08.326Z"}, {"id": 8, "name": "Safety Helmets", "category": "Safety Gear", "quantity": 30, "unit": "pieces", "lowStockThreshold": 5, "createdAt": "2025-06-27T06:11:08.405Z", "updatedAt": "2025-06-27T06:11:08.405Z"}], "inventoryRequests": [], "mediaFiles": [], "notifications": [{"id": 1, "userId": 2, "senderId": 1, "title": "New update in Lagos Office Complex", "message": "Admin User posted: test", "type": "info", "isRead": false, "data": {"dailyLogId": 1, "projectId": "KM-2024-001"}, "createdAt": "2025-06-27T05:52:20.264Z"}, {"id": 2, "userId": 4, "senderId": 1, "title": "New update in Lagos Office Complex", "message": "Admin User posted: test", "type": "info", "isRead": false, "data": {"dailyLogId": 1, "projectId": "KM-2024-001"}, "createdAt": "2025-06-27T05:52:20.264Z"}, {"id": 3, "userId": 3, "senderId": 1, "title": "New update in Lagos Office Complex", "message": "Admin User posted: test", "type": "info", "isRead": false, "data": {"dailyLogId": 1, "projectId": "KM-2024-001"}, "createdAt": "2025-06-27T05:52:20.264Z"}, {"id": 4, "userId": 2, "senderId": 1, "title": "Reply to daily log in Lagos Office Complex", "message": "Ad<PERSON> User replied: Hello", "type": "info", "isRead": false, "data": {"projectId": "KM-2024-001", "dailyLogId": 1, "replyId": 1}, "createdAt": "2025-06-27T05:52:45.539Z"}, {"id": 5, "userId": 4, "senderId": 1, "title": "Reply to daily log in Lagos Office Complex", "message": "Ad<PERSON> User replied: Hello", "type": "info", "isRead": false, "data": {"projectId": "KM-2024-001", "dailyLogId": 1, "replyId": 1}, "createdAt": "2025-06-27T05:52:45.539Z"}, {"id": 6, "userId": 3, "senderId": 1, "title": "Reply to daily log in Lagos Office Complex", "message": "Ad<PERSON> User replied: Hello", "type": "info", "isRead": false, "data": {"projectId": "KM-2024-001", "dailyLogId": 1, "replyId": 1}, "createdAt": "2025-06-27T05:52:45.539Z"}], "notificationSettings": [], "systemSettings": [], "exportedAt": "2025-06-27T09:29:33.924Z", "totalRecords": 23}