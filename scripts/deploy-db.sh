#!/bin/bash

# Database Deployment Script
# This script handles database migrations and seeding for production deployment

set -e  # Exit on any error

echo "🗄️  Starting database deployment..."

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is not set"
    exit 1
fi

# Check if we're in production
if [ "$NODE_ENV" = "production" ]; then
    echo "🔒 Production environment detected"
    
    # Backup database before migration (if backup tools are available)
    if command -v pg_dump &> /dev/null; then
        echo "📦 Creating database backup..."
        BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
        pg_dump "$DATABASE_URL" > "backups/$BACKUP_FILE" || echo "⚠️  Backup failed, continuing..."
    fi
    
    # Run production migrations
    echo "🚀 Running production migrations..."
    npx prisma migrate deploy
    
else
    echo "🔧 Development environment detected"
    
    # Run development migrations
    echo "🚀 Running development migrations..."
    npx prisma migrate dev --name "deployment_$(date +%Y%m%d_%H%M%S)"
fi

# Generate Prisma client
echo "⚙️  Generating Prisma client..."
npx prisma generate

# Seed database if requested
if [ "$SEED_DATABASE" = "true" ]; then
    echo "🌱 Seeding database..."
    npx prisma db seed
fi

# Verify database connection
echo "🔍 Verifying database connection..."
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.\$connect()
  .then(() => {
    console.log('✅ Database connection successful');
    return prisma.\$disconnect();
  })
  .catch((error) => {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  });
"

echo "✅ Database deployment completed successfully!"
