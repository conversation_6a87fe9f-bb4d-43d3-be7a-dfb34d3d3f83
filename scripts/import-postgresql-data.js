/**
 * Import SQLite data to PostgreSQL
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

// Create Prisma client for PostgreSQL
const prisma = new PrismaClient();

async function importData() {
  try {
    console.log('🔄 Starting PostgreSQL data import...');

    // Read exported data
    const exportPath = path.join(__dirname, 'migration-data.json');
    if (!fs.existsSync(exportPath)) {
      console.error('❌ Migration data file not found. Please run export-sqlite-data.js first.');
      process.exit(1);
    }

    const data = JSON.parse(fs.readFileSync(exportPath, 'utf8'));
    console.log(`📊 Found ${data.totalRecords} total records to import`);

    // Clear existing data (in correct order to handle foreign keys)
    console.log('🧹 Clearing existing data...');
    await prisma.systemSettings.deleteMany();
    await prisma.notificationSettings.deleteMany();
    await prisma.notification.deleteMany();
    await prisma.mediaFile.deleteMany();
    await prisma.inventoryRequest.deleteMany();
    await prisma.inventoryItem.deleteMany();
    await prisma.materialRequest.deleteMany();
    await prisma.budgetItem.deleteMany();
    await prisma.budget.deleteMany();
    await prisma.attachment.deleteMany();
    await prisma.dailyLogReply.deleteMany();
    await prisma.dailyLog.deleteMany();
    await prisma.projectFinancial.deleteMany();
    await prisma.phaseTask.deleteMany();
    await prisma.projectPhase.deleteMany();
    await prisma.projectTeamMember.deleteMany();
    await prisma.project.deleteMany();
    await prisma.user.deleteMany();

    // Import users first (they're referenced by other tables)
    console.log('👥 Importing users...');
    for (const user of data.users) {
      await prisma.user.create({
        data: {
          id: user.id,
          email: user.email,
          password: user.password,
          role: user.role,
          name: user.name,
          phone: user.phone,
          location: user.location,
          company: user.company,
          bio: user.bio,
          avatar: user.avatar,
          createdAt: user.createdAt ? new Date(user.createdAt) : new Date(),
          updatedAt: user.updatedAt ? new Date(user.updatedAt) : new Date()
        }
      });
    }
    console.log(`✅ Imported ${data.users.length} users`);

    // Import projects with their relationships
    console.log('🏗️ Importing projects...');
    for (const project of data.projects) {
      const { phases, teamMembers, projectFinancials, ...projectData } = project;
      
      const createdProject = await prisma.project.create({
        data: {
          ...projectData,
          createdAt: new Date(projectData.createdAt),
          updatedAt: new Date(projectData.updatedAt),
          startDate: projectData.startDate ? new Date(projectData.startDate) : null,
          endDate: projectData.endDate ? new Date(projectData.endDate) : null
        }
      });

      // Import project phases
      if (phases && phases.length > 0) {
        for (const phase of phases) {
          const { tasks, ...phaseData } = phase;
          
          const createdPhase = await prisma.projectPhase.create({
            data: {
              ...phaseData,
              createdAt: new Date(phaseData.createdAt),
              updatedAt: new Date(phaseData.updatedAt),
              startDate: phaseData.startDate ? new Date(phaseData.startDate) : null,
              endDate: phaseData.endDate ? new Date(phaseData.endDate) : null
            }
          });

          // Import phase tasks
          if (tasks && tasks.length > 0) {
            for (const task of tasks) {
              await prisma.phaseTask.create({
                data: {
                  ...task,
                  phaseId: createdPhase.id,
                  createdAt: new Date(task.createdAt),
                  updatedAt: new Date(task.updatedAt)
                }
              });
            }
          }
        }
      }

      // Import team members
      if (teamMembers && teamMembers.length > 0) {
        for (const member of teamMembers) {
          await prisma.projectTeamMember.create({
            data: {
              ...member,
              createdAt: new Date(member.createdAt)
            }
          });
        }
      }

      // Import project financials
      if (projectFinancials) {
        let installmentsData = null;
        if (projectFinancials.installments) {
          try {
            // If it's already an object, use it directly; if it's a string, parse it
            installmentsData = typeof projectFinancials.installments === 'string'
              ? JSON.parse(projectFinancials.installments)
              : projectFinancials.installments;
          } catch (e) {
            console.warn('Failed to parse installments, setting to null:', e.message);
            installmentsData = null;
          }
        }

        await prisma.projectFinancial.create({
          data: {
            ...projectFinancials,
            installments: installmentsData,
            createdAt: new Date(projectFinancials.createdAt),
            updatedAt: new Date(projectFinancials.updatedAt)
          }
        });
      }
    }
    console.log(`✅ Imported ${data.projects.length} projects`);

    // Import daily logs
    console.log('📝 Importing daily logs...');
    for (const log of data.dailyLogs) {
      const { replies, attachments, ...logData } = log;
      
      const createdLog = await prisma.dailyLog.create({
        data: {
          ...logData,
          createdAt: new Date(logData.createdAt),
          updatedAt: new Date(logData.updatedAt)
        }
      });

      // Import replies
      if (replies && replies.length > 0) {
        for (const reply of replies) {
          const { logId, dailyLogId, ...replyData } = reply; // Remove old ID fields
          await prisma.dailyLogReply.create({
            data: {
              ...replyData,
              dailyLogId: createdLog.id, // Use the new log ID
              createdAt: reply.createdAt ? new Date(reply.createdAt) : new Date(),
              updatedAt: reply.updatedAt ? new Date(reply.updatedAt) : new Date()
            }
          });
        }
      }

      // Import attachments
      if (attachments && attachments.length > 0) {
        for (const attachment of attachments) {
          await prisma.attachment.create({
            data: {
              ...attachment,
              dailyLogId: createdLog.id,
              createdAt: new Date(attachment.createdAt)
            }
          });
        }
      }
    }
    console.log(`✅ Imported ${data.dailyLogs.length} daily logs`);

    // Import other data
    console.log('📦 Importing inventory items...');
    for (const item of data.inventoryItems) {
      await prisma.inventoryItem.create({
        data: {
          ...item,
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt)
        }
      });
    }
    console.log(`✅ Imported ${data.inventoryItems.length} inventory items`);

    console.log('🔔 Importing notifications...');
    for (const notification of data.notifications) {
      let notificationData = null;
      if (notification.data) {
        try {
          notificationData = typeof notification.data === 'string'
            ? JSON.parse(notification.data)
            : notification.data;
        } catch (e) {
          console.warn('Failed to parse notification data, setting to null:', e.message);
          notificationData = null;
        }
      }

      await prisma.notification.create({
        data: {
          ...notification,
          data: notificationData,
          createdAt: new Date(notification.createdAt)
        }
      });
    }
    console.log(`✅ Imported ${data.notifications.length} notifications`);

    console.log('🎉 PostgreSQL data import completed successfully!');
    console.log('📊 Migration summary:');
    console.log(`   Users: ${data.users.length}`);
    console.log(`   Projects: ${data.projects.length}`);
    console.log(`   Daily Logs: ${data.dailyLogs.length}`);
    console.log(`   Inventory Items: ${data.inventoryItems.length}`);
    console.log(`   Notifications: ${data.notifications.length}`);

  } catch (error) {
    console.error('❌ Error during data import:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run import
importData();
