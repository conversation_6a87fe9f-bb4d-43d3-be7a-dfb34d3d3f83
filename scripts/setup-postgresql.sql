-- PostgreSQL Database Setup Script
-- Run this script as a PostgreSQL superuser (e.g., postgres)

-- Create database
CREATE DATABASE karmod_project_hub;

-- Create user
CREATE USER karmod_user WITH PASSWORD 'karmod_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE karmod_project_hub TO karmod_user;

-- Connect to the database and grant schema privileges
\c karmod_project_hub;
GRANT ALL ON SCHEMA public TO karmod_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO karmod_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO karmod_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO karmod_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO karmod_user;
