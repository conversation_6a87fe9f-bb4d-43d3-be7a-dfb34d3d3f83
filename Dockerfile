# Karmod Project Hub - Unified Full-Stack Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Install system dependencies for building
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package*.json ./

# Install pnpm
RUN npm install -g pnpm

# Copy lockfile if it exists, otherwise pnpm will generate one
COPY pnpm-lock.yaml* ./

# Install dependencies
RUN pnpm install

# Copy source code
COPY . .

# Set build environment variables
ENV NODE_ENV=production
ENV VITE_API_URL=https://project.karmod.com.ng
ENV VITE_APP_NAME="Karmod Project Hub"

# Build frontend with explicit config
RUN NODE_ENV=production VITE_API_URL=https://project.karmod.com.ng pnpm build

# Generate Prisma client
RUN pnpm db:generate

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    curl \
    dumb-init

# Install pnpm
RUN npm install -g pnpm

# Copy package files
COPY package*.json ./

# Copy lockfile if it exists
COPY pnpm-lock.yaml* ./

# Install production dependencies only
RUN pnpm install --prod

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/server ./server
COPY --from=builder /app/prisma ./prisma

# Generate Prisma client in production stage
RUN npx prisma generate

# Create necessary directories
RUN mkdir -p uploads logs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S karmod -u 1001

# Change ownership of app directory
RUN chown -R karmod:nodejs /app

# Switch to non-root user
USER karmod

# Set production environment
ENV NODE_ENV=production
ENV PORT=3001

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/api/health || exit 1

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Start application
CMD ["node", "server/src/server.js"]
