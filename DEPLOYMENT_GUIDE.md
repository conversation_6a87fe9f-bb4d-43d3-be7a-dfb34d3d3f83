# Karmod Project Hub - Unified Full-Stack Deployment Guide

This guide covers deployment for the **unified full-stack architecture** where frontend and backend are served from a single Express.js application.

## 🏗️ Architecture Overview

```
Karmod Project Hub (Unified Full-Stack)
├── Frontend: React SPA (built to static files)
├── Backend: Express.js API + Static File Serving
├── Database: PostgreSQL (primary) / SQLite (development)
└── Uploads: Local file storage with attachment API
```

**Key Benefits:**
- ✅ Single deployment unit
- ✅ One domain/port for everything
- ✅ Simplified CORS and routing
- ✅ Unified environment configuration

---

## 📋 Prerequisites

### Required Software
- **Node.js** v18+ 
- **pnpm** v8+ (package manager)
- **PostgreSQL** v15+ (production database)

### For Docker Deployment
- **Docker** v20+
- **Docker Compose** v2+

---

## 🚀 Method 1: Docker Deployment (Recommended)

### Step 1: Clone and Configure
```bash
# Clone repository
git clone <your-repo-url>
cd karmod

# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### Step 2: Configure Environment
Update `.env` with your production values:
```bash
# Application
NODE_ENV=production
PORT=3001
APP_NAME="Karmod Project Hub"

# Database (PostgreSQL)
DATABASE_URL="**************************************/karmod_db"

# Security (CHANGE THESE!)
JWT_SECRET="your-super-secure-jwt-secret-here"
JWT_REFRESH_SECRET="your-super-secure-refresh-secret-here"

# CORS
CORS_ORIGIN=https://yourdomain.com
FRONTEND_URL="https://yourdomain.com"
```

### Step 3: Deploy with Docker
```bash
# Build and start services
docker-compose up -d

# Check logs
docker-compose logs -f

# Run database migrations
docker-compose exec app pnpm db:migrate

# Seed initial data
docker-compose exec app pnpm db:seed
```

### Step 4: Access Application
- **Application**: http://localhost:3001
- **API**: http://localhost:3001/api
- **Database Admin**: http://localhost:8080 (pgAdmin)

---

## 🖥️ Method 2: Manual Deployment (VPS/Cloud)

### Step 1: Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install pnpm
npm install -g pnpm

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib
```

### Step 2: Database Setup
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE karmod_db;
CREATE USER karmod_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE karmod_db TO karmod_user;
\q
```

### Step 3: Application Setup
```bash
# Clone repository
git clone <your-repo-url>
cd karmod

# Install dependencies (ONCE!)
pnpm install

# Copy and configure environment
cp .env.example .env
nano .env
```

### Step 4: Configure Environment
```bash
# Production environment
NODE_ENV=production
PORT=3001
DATABASE_URL="postgresql://karmod_user:your_secure_password@localhost:5432/karmod_db"

# Security secrets
JWT_SECRET="your-super-secure-jwt-secret"
JWT_REFRESH_SECRET="your-super-secure-refresh-secret"

# CORS for your domain
CORS_ORIGIN=https://yourdomain.com
FRONTEND_URL="https://yourdomain.com"
```

### Step 5: Build and Deploy
```bash
# Build frontend
pnpm build

# Run database migrations
pnpm db:migrate

# Seed initial data
pnpm db:seed

# Start application
pnpm server:start
```

### Step 6: Process Management (PM2)
```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start server/src/server.js --name "karmod-hub"

# Save PM2 configuration
pm2 save
pm2 startup

# Monitor
pm2 status
pm2 logs karmod-hub
```

---

## 🔧 Key Deployment Commands

### Single Installation
```bash
# Only ONE command needed for dependencies
pnpm install
```

### Build Process
```bash
# Build frontend static files
pnpm build

# Run database setup
pnpm db:migrate
pnpm db:seed
```

### Start Application
```bash
# Development
pnpm server:dev

# Production
pnpm server:start
```

---

## 📁 Unified File Structure

```
karmod/
├── node_modules/           # Single dependency folder
├── dist/                   # Built frontend files
├── server/                 # Backend API
├── src/                    # Frontend source
├── prisma/                 # Database schema
├── uploads/                # File attachments
├── .env                    # Single environment config
└── package.json           # All dependencies
```

**Important Notes:**
- ✅ Only ONE `node_modules` folder
- ✅ Only ONE `package.json` with dependencies
- ✅ Only ONE `.env` file
- ✅ Only ONE `pnpm install` command needed

---

## 🐳 Docker Configuration Files

### Dockerfile
```dockerfile
# Multi-stage build for unified full-stack app
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json pnpm-lock.yaml ./

# Install pnpm and dependencies
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build frontend
RUN pnpm build

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy package files
COPY package*.json pnpm-lock.yaml ./

# Install production dependencies only
RUN pnpm install --frozen-lockfile --prod

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/server ./server
COPY --from=builder /app/prisma ./prisma

# Create uploads directory
RUN mkdir -p uploads

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3001/api/health || exit 1

# Start application
CMD ["node", "server/src/server.js"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=************************************************/karmod_db
    depends_on:
      - db
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: karmod_db
      POSTGRES_USER: karmod_user
      POSTGRES_PASSWORD: karmod_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8080:80"
    depends_on:
      - db
    restart: unless-stopped

volumes:
  postgres_data:
```

---

## 🔧 Environment Variables Reference

### Required Variables
```bash
# Application
NODE_ENV=production
PORT=3001
APP_NAME="Karmod Project Hub"

# Database
DATABASE_URL="********************************/db"

# Security (MUST CHANGE!)
JWT_SECRET="your-jwt-secret"
JWT_REFRESH_SECRET="your-refresh-secret"
```

### Optional Variables
```bash
# CORS
CORS_ORIGIN=https://yourdomain.com
CORS_CREDENTIALS=true

# File Uploads
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
```

---

## 🔒 Security Checklist

### Before Production
- [ ] Change all default passwords
- [ ] Generate secure JWT secrets
- [ ] Configure CORS for your domain
- [ ] Set up SSL/TLS certificates
- [ ] Configure firewall rules
- [ ] Set up database backups
- [ ] Configure log rotation
- [ ] Set up monitoring

### Recommended Security Headers
The application automatically includes:
- Helmet.js security headers
- CORS protection
- Rate limiting
- Input validation
- File upload restrictions

---

## 📊 Monitoring & Maintenance

### Health Checks
- **Application**: `GET /api/health`
- **Database**: `GET /api/health/db`

### Log Files
- **Application**: `logs/app.log`
- **Access**: `logs/access.log`
- **Error**: `logs/error.log`

### Database Maintenance
```bash
# Backup database
pg_dump karmod_db > backup.sql

# Restore database
psql karmod_db < backup.sql

# Run migrations
pnpm db:migrate
```

---

## 🚨 Troubleshooting

### Common Issues

**1. Port Already in Use**
```bash
# Find process using port 3001
lsof -i :3001
# Kill process
kill -9 <PID>
```

**2. Database Connection Failed**
- Check PostgreSQL is running
- Verify DATABASE_URL format
- Check firewall settings

**3. File Upload Issues**
- Ensure uploads directory exists
- Check file permissions
- Verify MAX_FILE_SIZE setting

**4. Build Failures**
```bash
# Clear cache and reinstall
rm -rf node_modules
rm pnpm-lock.yaml
pnpm install
```

### Getting Help
- Check application logs: `pm2 logs karmod-hub`
- Check database logs: `sudo journalctl -u postgresql`
- Verify environment: `printenv | grep -E "(NODE_ENV|DATABASE_URL|JWT)"`

---

**🎉 Your Karmod Project Hub is now deployed with unified full-stack architecture!**
