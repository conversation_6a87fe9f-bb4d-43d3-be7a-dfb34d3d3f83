# Karmod Project Hub - Unified Full-Stack Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
NODE_ENV=development
PORT=3001
APP_NAME="Karmod Project Hub"
APP_VERSION=1.0.0

# Database Configuration
# SQLite (backup/development)
SQLITE_DATABASE_URL="file:./prisma/dev.db"
# PostgreSQL (primary/production)
DATABASE_URL="postgresql://username:password@localhost:5432/karmod_db"

# JWT Configuration (CHANGE THESE IN PRODUCTION!)
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-this-in-production"
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
# For development
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true

# For production (project.karmod.com.ng)
# CORS_ORIGIN=https://project.karmod.com.ng
# CORS_CREDENTIALS=true

# Frontend Configuration
# For development
FRONTEND_URL="http://localhost:5173"
VITE_API_URL="http://localhost:3001"

# For production (project.karmod.com.ng)
# FRONTEND_URL="https://project.karmod.com.ng"
# VITE_API_URL="https://project.karmod.com.ng"

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Security
BCRYPT_ROUNDS=10
SESSION_SECRET=your-session-secret-for-development

# ZeptoMail Configuration (Optional - can also be configured via admin settings)
ZEPTOMAIL_API_KEY=your-zeptomail-api-key
ZEPTOMAIL_FROM_EMAIL=<EMAIL>
ZEPTOMAIL_FROM_NAME="Karmod Project Hub"
ZEPTOMAIL_TEST_EMAIL=<EMAIL>

# Alternative naming convention (for compatibility)
ZEPTO_API_KEY=your-zeptomail-api-key
ZEPTO_FROM_EMAIL=<EMAIL>
ZEPTO_BOUNCE_ADDRESS=<EMAIL>
