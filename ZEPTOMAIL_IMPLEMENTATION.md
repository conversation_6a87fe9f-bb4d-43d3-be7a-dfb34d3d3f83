# ZeptoMail Implementation Guide

## Overview

This document outlines the ZeptoMail email service implementation in the Karmod Project Hub. The implementation has been fixed and enhanced to support both database configuration and environment variables with multiple naming conventions.

## Issues Fixed

### 1. Incorrect API URL
- **Problem**: The original implementation used `https://api.zeptomail.in/v1.1/email`
- **Fix**: Updated to the correct URL `https://api.zeptomail.com/v1.1/email`
- **Location**: `server/src/services/emailService.js` line 80

### 2. Environment Variable Support
- **Problem**: Only supported `ZEPTOMAIL_*` naming convention
- **Fix**: Added support for both `ZEPTOMAIL_*` and `ZEPTO_*` naming conventions
- **Locations**: 
  - `server/src/services/emailService.js` (fallback logic)
  - `.env.example` (documentation)

### 3. Missing Environment Variables
- **Problem**: ZeptoMail environment variables were not documented
- **Fix**: Added comprehensive environment variable examples to `.env.example`

## Configuration Options

### Option 1: Environment Variables (Recommended for Development)

Add these variables to your `.env` file:

```bash
# Primary naming convention
ZEPTOMAIL_API_KEY=your-zeptomail-api-key
ZEPTOMAIL_FROM_EMAIL=<EMAIL>
ZEPTOMAIL_FROM_NAME="Karmod Project Hub"
ZEPTOMAIL_TEST_EMAIL=<EMAIL>

# Alternative naming convention (for compatibility)
ZEPTO_API_KEY=your-zeptomail-api-key
ZEPTO_FROM_EMAIL=<EMAIL>
ZEPTO_BOUNCE_ADDRESS=<EMAIL>
```

### Option 2: Admin Settings (Recommended for Production)

1. Log in as an admin user
2. Navigate to Settings page
3. Scroll to "ZeptoMail Integration" section
4. Configure:
   - **API Key**: Your ZeptoMail API key (encrypted in database)
   - **From Email**: Default sender email address
   - **From Name**: Default sender name
   - **Test Email**: Email address for testing

## Usage Examples

### Using the Main Email Service

```javascript
const { sendEmail } = require('../services/emailService');

// Send email using template
await sendEmail({
  to: '<EMAIL>',
  subject: 'Welcome to Karmod',
  template: 'welcome',
  data: {
    userName: 'John Doe',
    projectName: 'My Project'
  }
});

// Send email with custom from address
await sendEmail({
  to: '<EMAIL>',
  subject: 'Custom Email',
  template: 'notification',
  data: { message: 'Hello World' },
  from: {
    email: '<EMAIL>',
    name: 'Custom Sender'
  }
});
```

### Using the Simplified ZeptoMail Service

```javascript
const { zeptoMailService } = require('../utils/zeptoMailService');

// Send simple email
await zeptoMailService.sendEmail({
  to: '<EMAIL>',
  subject: 'Test Email',
  htmlBody: '<h1>Hello World</h1><p>This is a test email.</p>'
});

// Send to multiple recipients
await zeptoMailService.sendEmail({
  to: ['<EMAIL>', '<EMAIL>'],
  subject: 'Bulk Email',
  htmlBody: '<p>This email goes to multiple recipients.</p>',
  trackClicks: true,
  trackOpens: true
});

// Test configuration
const testResult = await zeptoMailService.testConfiguration('<EMAIL>');
console.log(testResult);

// Validate configuration
const validation = zeptoMailService.validateConfiguration();
console.log(validation);
```

## API Endpoints

### Test Email Functionality

**POST** `/api/notifications/test-email`

Send a test email to verify configuration:

```javascript
// Frontend usage
const response = await api.testEmailSettings('<EMAIL>');
```

### ZeptoMail Settings Management (Admin Only)

**GET** `/api/admin/settings/email/zeptomail`
- Get current ZeptoMail settings

**PUT** `/api/admin/settings/email/zeptomail`
- Update ZeptoMail settings

```javascript
// Frontend usage
const settings = await api.getZeptomailSettings();
await api.updateZeptomailSettings({
  apiKey: 'new-api-key',
  fromEmail: '<EMAIL>',
  fromName: 'Karmod Hub',
  testEmail: '<EMAIL>'
});
```

## File Structure

```
server/src/
├── services/
│   └── emailService.js          # Main email service (enhanced)
├── utils/
│   └── zeptoMailService.js      # Simplified ZeptoMail service (new)
├── controllers/
│   ├── systemSettingsController.js  # Admin settings management
│   └── notificationController.js    # Email testing endpoint
└── routes/
    ├── systemSettings.js        # Admin settings routes
    └── notifications.js         # Email testing routes

src/
├── pages/
│   └── Settings.jsx            # Admin settings UI
└── services/
    ├── api.js                  # API client methods
    └── settingsApi.js          # Settings-specific API methods
```

## Environment Variable Priority

The system uses the following priority order for configuration:

1. **Database settings** (configured via admin panel)
2. **ZEPTOMAIL_* environment variables**
3. **ZEPTO_* environment variables** (fallback)
4. **Default values**

## Testing

### Via Admin Interface
1. Go to Settings → ZeptoMail Integration
2. Enter test email address
3. Click "Test Email Settings"

### Via API
```bash
curl -X POST http://localhost:3001/api/notifications/test-email \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"testEmail": "<EMAIL>"}'
```

### Via Simplified Service
```javascript
const { zeptoMailService } = require('./server/src/utils/zeptoMailService');

// Test configuration
const result = await zeptoMailService.testConfiguration('<EMAIL>');
console.log(result);
```

## Troubleshooting

### Common Issues

1. **"API key not configured"**
   - Ensure API key is set in environment variables or admin settings
   - Check that the API key is valid and not expired

2. **"Failed to send email: 401"**
   - Invalid API key
   - API key may be expired or revoked

3. **"Failed to send email: 400"**
   - Check email format
   - Verify sender email is authorized in ZeptoMail

4. **Network timeouts**
   - Check internet connectivity
   - Verify firewall settings allow outbound HTTPS connections

### Debug Logging

The email service includes comprehensive logging. Check server logs for detailed information about email sending attempts.

## Security Notes

- API keys are encrypted when stored in the database
- Environment variables should be secured in production
- Use HTTPS in production to protect API communications
- Regularly rotate API keys for security

## Next Steps

1. Set up your ZeptoMail account and obtain API key
2. Configure environment variables or use admin settings
3. Test email functionality using the test endpoint
4. Integrate email sending into your application workflows
