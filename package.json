{"name": "karmod-projecthub", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "build:prod": "NODE_ENV=production vite build", "build:analyze": "vite build --mode analyze", "preview": "vite preview", "preview:prod": "vite preview --port 4173", "server:dev": "nodemon server/src/server.js", "server:start": "node server/src/server.js", "server:prod": "cd server && NODE_ENV=production node src/server.js", "dev:full": "concurrently \"pnpm server:dev\" \"pnpm dev\"", "build:full": "pnpm build && pnpm server:start", "start": "pnpm build && pnpm preview", "db:migrate": "npx prisma migrate dev", "db:migrate:prod": "npx prisma migrate deploy", "db:generate": "npx prisma generate", "db:seed": "node prisma/seed.js", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "clean": "rm -rf dist"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@emotion/is-prop-valid": "^1.2.1", "@prisma/client": "^6.10.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "compression": "^1.8.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "framer-motion": "^10.16.4", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.285.0", "morgan": "^1.10.0", "multer": "^2.0.1", "prisma": "^6.10.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-pull-to-refresh": "^2.0.1", "react-router-dom": "^6.16.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "zeptomail": "^1.0.1"}, "devDependencies": {"@babel/generator": "^7.27.0", "@babel/parser": "^7.27.0", "@babel/traverse": "^7.27.0", "@babel/types": "^7.27.0", "@types/bcryptjs": "^3.0.0", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^20.8.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "concurrently": "^9.2.0", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "jest": "^30.0.3", "nodemon": "^3.1.10", "postcss": "^8.4.31", "supertest": "^7.1.1", "tailwindcss": "^3.3.3", "terser": "^5.39.0", "vite": "^4.4.5"}}