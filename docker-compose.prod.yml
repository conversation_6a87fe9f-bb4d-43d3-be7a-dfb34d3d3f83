version: '3.8'

services:
  # Main application (production)
  app:
    build: .
    container_name: karmod-hub-prod
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - CORS_ORIGIN=${CORS_ORIGIN}
      - FRONTEND_URL=${FRONTEND_URL}
      - LOG_LEVEL=info
    # No database dependency - using external PostgreSQL
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - karmod-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Note: Using external PostgreSQL database via DATABASE_URL
  # No local database services needed

  # Nginx reverse proxy (production)
  nginx:
    image: nginx:alpine
    container_name: karmod-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - karmod-network

  # Redis for caching (production)
  redis:
    image: redis:7-alpine
    container_name: karmod-redis-prod
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - karmod-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

volumes:
  redis_data:
    driver: local

networks:
  karmod-network:
    driver: bridge
