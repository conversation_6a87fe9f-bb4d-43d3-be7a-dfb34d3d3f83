# Karmod Project Hub - Unified Full-Stack Application

A comprehensive construction project management system built with **unified full-stack architecture** using React frontend and Express.js backend served from a single application.

## 🏗️ Architecture

```
Karmod Project Hub (Unified Full-Stack)
├── Frontend: React SPA (built to static files)
├── Backend: Express.js API + Static File Serving  
├── Database: PostgreSQL (primary) / SQLite (development)
└── Storage: Local file uploads with attachment API
```

## ✨ Key Features

- **Project Management**: Create, track, and manage construction projects
- **Phase & Task Tracking**: Organize work into phases with detailed tasks
- **Financial Management**: Track budgets, expenses, and installments
- **Inventory Management**: Manage materials and equipment
- **File Attachments**: Upload and manage project documents
- **User Management**: Role-based access control
- **Real-time Updates**: Live project status updates

## 🚀 Quick Start

### Prerequisites
- **Node.js** v18+
- **pnpm** v8+
- **PostgreSQL** v15+ (for production)

### Installation

```bash
# Clone repository
git clone <your-repo-url>
cd karmod

# Install dependencies (ONCE!)
pnpm install

# Setup environment
cp .env.example .env
# Edit .env with your database credentials

# Run database migrations
pnpm db:migrate

# Seed initial data
pnpm db:seed

# Build frontend
pnpm build

# Start application
pnpm server:start
```

### Development

```bash
# Start development server
pnpm server:dev

# Start frontend development (separate terminal)
pnpm dev

# Or run both together
pnpm dev:full
```

## 🐳 Docker Deployment

```bash
# Quick Docker deployment
docker-compose up -d

# Run migrations
docker-compose exec app pnpm db:migrate

# Seed database
docker-compose exec app pnpm db:seed
```

Access:
- **Application**: http://localhost:3001
- **API**: http://localhost:3001/api
- **pgAdmin**: http://localhost:8080

## 📁 Project Structure

```
karmod/
├── node_modules/           # Single dependency folder
├── dist/                   # Built frontend files
├── server/                 # Backend API
│   ├── src/
│   │   ├── routes/        # API routes
│   │   ├── middleware/    # Express middleware
│   │   ├── lib/          # Utilities
│   │   └── config/       # Configuration
├── src/                   # Frontend React source
├── prisma/                # Database schema & migrations
├── uploads/               # File attachments
├── .env                   # Environment configuration
└── package.json          # All dependencies
```

## 🔧 Available Scripts

### Development
- `pnpm dev` - Start frontend development server
- `pnpm server:dev` - Start backend development server
- `pnpm dev:full` - Start both frontend and backend

### Production
- `pnpm build` - Build frontend for production
- `pnpm server:start` - Start production server
- `pnpm server:prod` - Start with production environment

### Database
- `pnpm db:migrate` - Run database migrations
- `pnpm db:seed` - Seed database with initial data
- `pnpm db:studio` - Open Prisma Studio
- `pnpm db:reset` - Reset database

## 🔒 Environment Variables

Copy `.env.example` to `.env` and update:

```bash
# Application
NODE_ENV=development
PORT=3001
APP_NAME="Karmod Project Hub"

# Database
DATABASE_URL="********************************/db"

# Security (CHANGE IN PRODUCTION!)
JWT_SECRET="your-jwt-secret"
JWT_REFRESH_SECRET="your-refresh-secret"

# CORS
CORS_ORIGIN=http://localhost:5173
FRONTEND_URL="http://localhost:5173"
```

## 📊 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - User logout

### Projects
- `GET /api/projects` - List projects
- `POST /api/projects` - Create project
- `GET /api/projects/:id` - Get project details
- `PUT /api/projects/:id` - Update project
- `DELETE /api/projects/:id` - Delete project

### Attachments
- `POST /api/attachments` - Upload file
- `GET /api/attachments/:id` - Download file
- `DELETE /api/attachments/:id` - Delete file

## 🚨 Troubleshooting

### Common Issues

**Port already in use:**
```bash
lsof -i :3001
kill -9 <PID>
```

**Database connection failed:**
- Check PostgreSQL is running
- Verify DATABASE_URL in .env
- Check firewall settings

**Build failures:**
```bash
rm -rf node_modules
rm pnpm-lock.yaml
pnpm install
```

## 📈 Deployment

See [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) for detailed deployment instructions including:
- Docker deployment
- Manual VPS deployment
- Production configuration
- Security checklist
- Monitoring setup

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the deployment guide

---

**🎉 Built with unified full-stack architecture for simplified deployment and maintenance!**
