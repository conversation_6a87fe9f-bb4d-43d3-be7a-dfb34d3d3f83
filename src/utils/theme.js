/**
 * Theme utility functions for managing application appearance
 */

import { storage } from './storage';

export const themeUtils = {
  // Apply dark mode
  applyDarkMode: (isDark) => {
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    storage.set('dark_mode', isDark);
  },

  // Apply theme color
  applyThemeColor: (color) => {
    document.documentElement.style.setProperty('--primary-color', color);
    storage.set('theme_color', color);
  },

  // Apply compact mode
  applyCompactMode: (isCompact) => {
    if (isCompact) {
      document.documentElement.classList.add('compact');
    } else {
      document.documentElement.classList.remove('compact');
    }
    storage.set('compact_mode', isCompact);
  },

  // Initialize theme from storage
  initializeTheme: () => {
    const darkMode = storage.get('dark_mode') ?? false;
    const themeColor = storage.get('theme_color') ?? '#3b82f6';
    const compactMode = storage.get('compact_mode') ?? false;

    themeUtils.applyDarkMode(darkMode);
    themeUtils.applyThemeColor(themeColor);
    themeUtils.applyCompactMode(compactMode);

    return {
      darkMode,
      themeColor,
      compactMode
    };
  },

  // Get current theme settings
  getCurrentTheme: () => {
    return {
      darkMode: storage.get('dark_mode') ?? false,
      themeColor: storage.get('theme_color') ?? '#3b82f6',
      compactMode: storage.get('compact_mode') ?? false,
      language: storage.get('language') ?? 'en',
      dateFormat: storage.get('date_format') ?? 'MM/DD/YYYY',
      timeFormat: storage.get('time_format') ?? '12h'
    };
  },

  // Format date according to user preference
  formatDate: (date, format = null) => {
    const dateFormat = format || storage.get('date_format') || 'MM/DD/YYYY';
    const dateObj = new Date(date);

    switch (dateFormat) {
      case 'DD/MM/YYYY':
        return dateObj.toLocaleDateString('en-GB');
      case 'YYYY-MM-DD':
        return dateObj.toISOString().split('T')[0];
      case 'DD MMM YYYY':
        return dateObj.toLocaleDateString('en-US', {
          day: '2-digit',
          month: 'short',
          year: 'numeric'
        });
      case 'MM/DD/YYYY':
      default:
        return dateObj.toLocaleDateString('en-US');
    }
  },

  // Format time according to user preference
  formatTime: (date, format = null) => {
    const timeFormat = format || storage.get('time_format') || '12h';
    const dateObj = new Date(date);

    if (timeFormat === '24h') {
      return dateObj.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return dateObj.toLocaleTimeString('en-US', {
        hour12: true,
        hour: 'numeric',
        minute: '2-digit'
      });
    }
  },

  // Format datetime according to user preferences
  formatDateTime: (date, dateFormat = null, timeFormat = null) => {
    return `${themeUtils.formatDate(date, dateFormat)} ${themeUtils.formatTime(date, timeFormat)}`;
  }
};

// Initialize theme on module load
if (typeof window !== 'undefined') {
  themeUtils.initializeTheme();
}
