import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { motion } from 'framer-motion';
import { BellRing, CheckCheck, Settings, ArrowLeft, AlertTriangle, Info, CheckCircle, RefreshCw } from 'lucide-react';
import { useNotifications } from '@/contexts/NotificationContext';

const Notifications = () => {
  const { notifications, markAsRead, markAllAsRead, unreadCount, loading, error, loadNotifications } = useNotifications();
  const navigate = useNavigate();

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'project': return <Info className="h-5 w-5 text-blue-500" />;
      case 'log': return <MessageSquare className="h-5 w-5 text-green-500" />;
      case 'budget': return <DollarSign className="h-5 w-5 text-yellow-500" />;
      case 'task': return <ListChecks className="h-5 w-5 text-purple-500" />;
      case 'material': return <Truck className="h-5 w-5 text-orange-500" />;
      case 'testimonial': return <Star className="h-5 w-5 text-pink-500" />;
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default: return <BellRing className="h-5 w-5 text-gray-500" />;
    }
  };


  return (
    <div className="p-4 max-w-3xl mx-auto space-y-6">
      <motion.div 
        initial={{ opacity: 0, y: 20 }} 
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center space-x-3">
            <Button variant="ghost" size="icon" onClick={() => navigate(-1)}><ArrowLeft className="h-5 w-5" /></Button>
            <div>
                <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
                <p className="text-gray-600">You have {unreadCount} unread notifications.</p>
            </div>
        </div>
        <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={loadNotifications}
              disabled={loading}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            {notifications.length > 0 && unreadCount > 0 && (
              <Button variant="outline" size="sm" onClick={markAllAsRead}><CheckCheck className="mr-2 h-4 w-4" /> Mark all as read</Button>
            )}
            <Link to="/settings">
                <Button variant="ghost" size="icon"><Settings className="h-5 w-5" /></Button>
            </Link>
        </div>
      </motion.div>

      {error && (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-center py-12"
        >
          <AlertTriangle className="mx-auto h-16 w-16 text-red-400 mb-4" />
          <h2 className="text-xl font-semibold text-gray-700">Error Loading Notifications</h2>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={loadNotifications} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </motion.div>
      )}

      {!error && notifications.length === 0 ? (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-center py-12"
        >
          <BellRing className="mx-auto h-16 w-16 text-gray-300 mb-4" />
          <h2 className="text-xl font-semibold text-gray-700">
            {loading ? 'Loading Notifications...' : 'No New Notifications'}
          </h2>
          <p className="text-gray-500">
            {loading ? 'Please wait while we fetch your notifications.' : "You're all caught up!"}
          </p>
        </motion.div>
      ) : (
        <div className="space-y-4">
          {notifications.map((notification, index) => (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <Card
                className={`glass border-white/20 cursor-pointer hover:shadow-md ${!(notification.read || notification.isRead) ? 'border-blue-500 border-2' : 'border-white/20'}`}
                onClick={() => {
                  markAsRead(notification.id);
                  if (notification.link) navigate(notification.link);
                }}
              >
                <CardContent className="p-4 flex items-start space-x-4">
                  <div className="mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-center">
                        <h3 className={`font-semibold ${!(notification.read || notification.isRead) ? 'text-gray-800' : 'text-gray-700'}`}>{notification.title}</h3>
                        {!(notification.read || notification.isRead) && <span className="w-2.5 h-2.5 bg-blue-500 rounded-full flex-shrink-0"></span>}
                    </div>
                    <p className="text-sm text-gray-600 mt-0.5">{notification.message}</p>
                    <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-500">{new Date(notification.timestamp || notification.createdAt).toLocaleString()}</span>
                        {notification.link && (
                            <Button variant="link" size="sm" className="p-0 h-auto text-xs">View Details</Button>
                        )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Notifications;

// Helper icons (ensure these are imported if not already available globally)
const MessageSquare = ({ className }) => <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>;
const DollarSign = ({ className }) => <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg>;
const ListChecks = ({ className }) => <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="10" x2="21" y1="6" y2="6"></line><line x1="10" x2="21" y1="12" y2="12"></line><line x1="10" x2="21" y1="18" y2="18"></line><polyline points="3 6 4 7 6 5"></polyline><polyline points="3 12 4 13 6 11"></polyline><polyline points="3 18 4 19 6 17"></polyline></svg>;
const Truck = ({ className }) => <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="1" y="3" width="15" height="13"></rect><polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon><circle cx="5.5" cy="18.5" r="2.5"></circle><circle cx="18.5" cy="18.5" r="2.5"></circle></svg>;
const Star = ({ className }) => <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>;