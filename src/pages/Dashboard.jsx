import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { motion } from 'framer-motion';
import { FolderOpen, Plus, TrendingUp, Clock, DollarSign, Users, AlertTriangle, CheckCircle, BookOpen, Banknote, FileText } from 'lucide-react';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ErrorDisplay from '@/components/ui/ErrorDisplay';

const Dashboard = () => {
  const { user } = useAuth();
  const { projects, loading, error, loadInitialData } = useProjects();

  // Handle loading state
  if (loading) {
    return (
      <div className="p-4">
        <LoadingSpinner
          size="lg"
          text="Loading dashboard data..."
          className="mx-auto"
        />
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="p-4">
        <ErrorDisplay
          error={error}
          title="Failed to Load Dashboard"
          onRetry={loadInitialData}
        />
      </div>
    );
  }

  const activeProjects = projects.filter(p => p.status === 'in_progress');
  const completedProjects = projects.filter(p => p.status === 'completed');

  let displayStats;

  if (user?.role === 'client') {
    const totalProjectValue = projects.reduce((sum, p) => sum + (Number(p.projectFinancials?.totalProjectValue) || Number(p.budget) || 0), 0);
    const totalAmountPaid = projects.reduce((sum, p) => sum + (Number(p.projectFinancials?.initialPayment) || 0), 0);
    displayStats = [
      { title: 'Active Projects', value: activeProjects.length, icon: FolderOpen, gradient: 'gradient-primary' },
      { title: 'Completed Projects', value: completedProjects.length, icon: CheckCircle, gradient: 'gradient-success' },
      { title: 'Total Project Value', value: `₦${(totalProjectValue / 1000000).toFixed(1)}M`, icon: Banknote, gradient: 'gradient-bg' },
      { title: 'Total Amount Paid', value: `₦${(totalAmountPaid / 1000000).toFixed(1)}M`, icon: DollarSign, gradient: 'gradient-warning' }
    ];
  } else {
    const totalBudget = projects.reduce((sum, p) => sum + (Number(p.budget) || 0), 0);
    const totalSpent = projects.reduce((sum, p) => sum + (Number(p.spent) || 0), 0);
    let budgetUsedPercentage = 0;
    if (totalBudget > 0) {
      budgetUsedPercentage = ((totalSpent / totalBudget) * 100);
    }
    const displayBudgetUsed = budgetUsedPercentage > 0 ? budgetUsedPercentage.toFixed(0) : "0";
    displayStats = [
      { title: 'Active Projects', value: activeProjects.length, icon: FolderOpen, gradient: 'gradient-primary' },
      { title: 'Completed Projects', value: completedProjects.length, icon: CheckCircle, gradient: 'gradient-success' },
      { title: 'Total Budget', value: `₦${(totalBudget / 1000000).toFixed(1)}M`, icon: DollarSign, gradient: 'gradient-bg' },
      { title: 'Budget Used', value: `${displayBudgetUsed}%`, icon: TrendingUp, gradient: 'gradient-warning' }
    ];
  }


  return (
    <div className="p-4 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center md:text-left"
      >
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Here's what's happening with your projects today.
        </p>
      </motion.div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {displayStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="card-hover glass border-white/20">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${stat.gradient}`}>
                      <Icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stat.value}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{stat.title}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {(user?.role === 'admin' || user?.role === 'project_manager' || user?.role === 'project_lead') && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Plus className="h-5 w-5" />
                <span>Quick Actions</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Link to="/projects/new">
                  <Button className="w-full gradient-primary text-white">
                    <Plus className="mr-2 h-4 w-4" />
                    New Project
                  </Button>
                </Link>
                <Link to="/daily-log">
                  <Button variant="outline" className="w-full">
                    <BookOpen className="mr-2 h-4 w-4" />
                    Add Daily Log
                  </Button>
                </Link>
                <Link to="/reports">
                  <Button variant="outline" className="w-full">
                    <FileText className="mr-2 h-4 w-4" />
                    View Reports
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card className="glass border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Projects</CardTitle>
              <Link to="/projects">
                <Button variant="ghost" size="sm">View All</Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {projects.slice(0, 3).map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                >
                  <Link to={`/projects/${project.id}`}>
                    <div className="p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-colors cursor-pointer bg-white dark:bg-gray-800">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-gray-900 dark:text-gray-100">{project.title}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          project.status === 'completed' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
                          project.status === 'in_progress' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :
                          project.status === 'on_hold' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :
                          'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                        }`}>
                          {project.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{project.location}</p>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm text-gray-700 dark:text-gray-300">
                          <span>Progress</span>
                          <span className="font-medium">{project.progress}%</span>
                        </div>
                        <Progress value={project.progress} className="h-2" />
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
              {projects.length === 0 && (
                <p className="text-center text-gray-500 dark:text-gray-400 py-4">No projects found. Start by creating one!</p>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {activeProjects.some(p => p.progress < 30) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card className="border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-orange-800">
                <AlertTriangle className="h-5 w-5" />
                <span>Attention Required</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-orange-700">
                Some projects are behind schedule. Consider reviewing timelines and resource allocation.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default Dashboard;