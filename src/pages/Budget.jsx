
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { DollarSign, Plus, Edit, Trash2, CheckCircle, Clock, AlertCircle, Send, CornerDownLeft, Save } from 'lucide-react';
import apiService from '../services/api';

const Budget = () => {
  const { id: projectIdFromParams } = useParams();
  const { user } = useAuth();
  const { projects, getProjectBudgets, addBudget, updateBudget, getProjectByIdSync } = useProjects();

  const [projectBudgets, setProjectBudgets] = useState([]);
  const [showAddBudgetForm, setShowAddBudgetForm] = useState(false);
  const [editingBudget, setEditingBudget] = useState(null);
  const [loadingActions, setLoadingActions] = useState({}); // Track loading state for each budget action

  const currentProject = getProjectByIdSync(projectIdFromParams);

  const initialBudgetData = {
    projectId: projectIdFromParams || '',
    items: [{ id: `BI${Date.now()}`, description: '', quantity: '', unitPrice: '', notes: '', total: 0 }],
    totalAmount: 0,
    remarks: ''
  };
  const [newBudgetData, setNewBudgetData] = useState(initialBudgetData);

  useEffect(() => {
    if (projectIdFromParams) {
      setProjectBudgets(getProjectBudgets(projectIdFromParams));
      setNewBudgetData(prev => ({ ...prev, projectId: projectIdFromParams }));
    } else {
      const allAccessibleBudgets = projects.reduce((acc, proj) => {
        return [...acc, ...getProjectBudgets(proj.id)];
      }, []);
      setProjectBudgets(allAccessibleBudgets);
    }
  }, [projectIdFromParams, getProjectBudgets, projects]);


  const calculateItemTotal = (item) => {
    const quantity = parseFloat(item.quantity) || 0;
    const unitPrice = parseFloat(item.unitPrice) || 0;
    return quantity * unitPrice;
  };

  const calculateBudgetTotal = (items) => {
    return items.reduce((sum, item) => sum + calculateItemTotal(item), 0);
  };

  const handleBudgetItemChange = (index, field, value) => {
    const budgetToUpdate = editingBudget ? editingBudget : newBudgetData;
    const setBudgetToUpdate = editingBudget ? setEditingBudget : setNewBudgetData;

    const updatedItems = budgetToUpdate.items.map((item, i) => {
      if (i === index) {
        const newItem = { ...item, [field]: value };
        newItem.total = calculateItemTotal(newItem);
        return newItem;
      }
      return item;
    });
    const newTotalAmount = calculateBudgetTotal(updatedItems);
    setBudgetToUpdate(prev => ({ ...prev, items: updatedItems, totalAmount: newTotalAmount }));
  };

  const addBudgetItemLine = () => {
    const budgetToUpdate = editingBudget ? editingBudget : newBudgetData;
    const setBudgetToUpdate = editingBudget ? setEditingBudget : setNewBudgetData;

    setBudgetToUpdate(prev => ({
      ...prev,
      items: [...prev.items, { id: `BI${Date.now()}`, description: '', quantity: '', unitPrice: '', notes: '', total: 0 }]
    }));
  };

  const removeBudgetItemLine = (index) => {
    const budgetToUpdate = editingBudget ? editingBudget : newBudgetData;
    const setBudgetToUpdate = editingBudget ? setEditingBudget : setNewBudgetData;
    
    const updatedItems = budgetToUpdate.items.filter((_, i) => i !== index);
    const newTotalAmount = calculateBudgetTotal(updatedItems);
    setBudgetToUpdate(prev => ({ ...prev, items: updatedItems, totalAmount: newTotalAmount }));
  };


  const handleSubmitBudget = (e) => {
    e.preventDefault();
    if (!newBudgetData.projectId) {
      toast({ title: "Project Required", description: "Please select a project.", variant: "destructive" });
      return;
    }
    if (newBudgetData.items.some(item => !item.description || !item.quantity || !item.unitPrice)) {
       toast({ title: "Incomplete Line Items", description: "Please fill all required fields in budget lines.", variant: "destructive" });
       return;
    }

    addBudget(newBudgetData);
    toast({ title: "Budget Submitted", description: "Your budget has been submitted for review." });
    setNewBudgetData(initialBudgetData);
    setShowAddBudgetForm(false);
    setProjectBudgets(getProjectBudgets(newBudgetData.projectId));
  };

  const handleSaveEditedBudget = () => {
    if (!editingBudget) return;
     if (editingBudget.items.some(item => !item.description || !item.quantity || !item.unitPrice)) {
       toast({ title: "Incomplete Line Items", description: "Please fill all required fields in budget lines.", variant: "destructive" });
       return;
    }
    const updatedBudgetData = { ...editingBudget };
    if (user?.role === 'project_lead' && editingBudget.status === 'revision_needed') {
      updatedBudgetData.status = 'pending_accountant_review';
      updatedBudgetData.pushedByAccountant = false; 
    }

    updateBudget(editingBudget.id, updatedBudgetData);
    toast({ title: "Budget Updated", description: "Changes have been saved." });
    setEditingBudget(null);
    setProjectBudgets(getProjectBudgets(editingBudget.projectId));
  };

  const handleAccountantAction = async (budgetId, action) => {
    const actionKey = `${budgetId}-${action}`;

    // Prevent double clicks
    if (loadingActions[actionKey]) return;

    setLoadingActions(prev => ({ ...prev, [actionKey]: true }));

    try {
      let response;
      let toastMessage = "";

      if (action === 'push_to_admin') {
        response = await apiService.pushBudgetToAdmin(budgetId);
        toastMessage = "Budget pushed to Admin for approval.";
      } else if (action === 'return_to_lead') {
        response = await apiService.returnBudgetToLead(budgetId);
        toastMessage = "Budget returned to Project Lead for revisions.";
      } else if (action === 'reject') {
        response = await apiService.updateBudget(budgetId, { status: 'rejected' });
        toastMessage = "Budget rejected by Accountant.";
      }

      if (response && response.budget) {
        // Update the local state with the updated budget
        setProjectBudgets(prev =>
          prev.map(budget =>
            budget.id === budgetId ? response.budget : budget
          )
        );
        toast({ title: "Budget Status Updated", description: toastMessage });
      }
    } catch (error) {
      console.error('Accountant action error:', error);
      toast({
        title: "Action Failed",
        description: "Failed to update budget status. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingActions(prev => ({ ...prev, [actionKey]: false }));
    }
  };
  
  const handleAdminAction = async (budgetId, action) => {
    const actionKey = `${budgetId}-${action}`;

    // Prevent double clicks
    if (loadingActions[actionKey]) return;

    setLoadingActions(prev => ({ ...prev, [actionKey]: true }));

    try {
      let response;
      let toastMessage = "";

      if (action === 'approve') {
        response = await apiService.approveBudget(budgetId);
        toastMessage = "Budget approved by Admin.";
      } else if (action === 'reject') {
        // For reject, we'll use the general update endpoint
        response = await apiService.updateBudget(budgetId, { status: 'rejected' });
        toastMessage = "Budget rejected by Admin.";
      }

      if (response && response.budget) {
        // Update the local state with the updated budget
        setProjectBudgets(prev =>
          prev.map(budget =>
            budget.id === budgetId ? response.budget : budget
          )
        );
        toast({ title: "Budget Status Updated", description: toastMessage });
      }
    } catch (error) {
      console.error('Admin action error:', error);
      toast({
        title: "Action Failed",
        description: "Failed to update budget status. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingActions(prev => ({ ...prev, [actionKey]: false }));
    }
  };

  const getStatusChip = (status) => {
    let Icon, color, text;
    switch (status) {
      case 'draft': Icon = Clock; color = 'bg-gray-100 text-gray-800'; text = 'Draft'; break;
      case 'pending_accountant': Icon = Clock; color = 'bg-yellow-100 text-yellow-800'; text = 'Pending Accountant'; break;
      case 'pending_admin': Icon = Clock; color = 'bg-blue-100 text-blue-800'; text = 'Pending Admin'; break;
      case 'approved': Icon = CheckCircle; color = 'bg-green-100 text-green-800'; text = 'Approved'; break;
      case 'revision_needed': Icon = Edit; color = 'bg-orange-100 text-orange-800'; text = 'Revision Needed'; break;
      case 'rejected': Icon = AlertCircle; color = 'bg-red-100 text-red-800'; text = 'Rejected'; break;
      default: Icon = Clock; color = 'bg-gray-100 text-gray-800'; text = status.replace(/_/g, ' '); break;
    }
    return <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}><Icon className="mr-1.5 h-3 w-3" />{text}</span>;
  };
  
  const budgetFormContent = (budgetState, setBudgetStateFunction) => (
    <form onSubmit={editingBudget ? handleSaveEditedBudget : handleSubmitBudget} className="space-y-6">
      {!editingBudget && projectIdFromParams && (
         <Input type="hidden" value={budgetState.projectId} />
      )}
       {!editingBudget && !projectIdFromParams && (
        <div className="space-y-2">
          <Label htmlFor="project">Project *</Label>
            <Select 
              value={budgetState.projectId} 
              onValueChange={(value) => setBudgetStateFunction(prev => ({ ...prev, projectId: value }))}
              required
            >
            <SelectTrigger>
              <SelectValue placeholder="Select project" />
            </SelectTrigger>
            <SelectContent>
              {projects.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <Card>
        <CardHeader><CardTitle>Budget Line Items</CardTitle></CardHeader>
        <CardContent className="space-y-4">
          {budgetState.items.map((item, index) => (
            <motion.div 
              key={item.id || index} 
              className="grid grid-cols-12 gap-x-3 gap-y-2 p-3 border rounded-md items-end"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <div className="col-span-12 md:col-span-4 space-y-1">
                {index === 0 && <Label htmlFor={`description-${index}`}>Description *</Label>}
                <Input id={`description-${index}`} placeholder="Item description" value={item.description} onChange={(e) => handleBudgetItemChange(index, 'description', e.target.value)} required />
              </div>
              <div className="col-span-6 md:col-span-2 space-y-1">
                 {index === 0 && <Label htmlFor={`quantity-${index}`}>Quantity *</Label>}
                <Input id={`quantity-${index}`} type="number" placeholder="Qty" value={item.quantity} onChange={(e) => handleBudgetItemChange(index, 'quantity', e.target.value)} required />
              </div>
              <div className="col-span-6 md:col-span-2 space-y-1">
                {index === 0 && <Label htmlFor={`unitPrice-${index}`}>Unit Price (₦) *</Label>}
                <Input id={`unitPrice-${index}`} type="number" placeholder="Unit Price" value={item.unitPrice} onChange={(e) => handleBudgetItemChange(index, 'unitPrice', e.target.value)} required />
              </div>
              <div className="col-span-12 md:col-span-3 space-y-1">
                {index === 0 && <Label htmlFor={`notes-${index}`}>Notes</Label>}
                <Input id={`notes-${index}`} placeholder="Internal notes" value={item.notes} onChange={(e) => handleBudgetItemChange(index, 'notes', e.target.value)} />
              </div>
              <div className="col-span-12 md:col-span-1 flex items-center">
                { budgetState.items.length > 1 && (
                  <Button type="button" variant="ghost" size="icon" onClick={() => removeBudgetItemLine(index)} className="text-red-500 hover:text-red-700 mt-1 md:mt-0">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <div className="col-span-12 text-right font-medium pr-4">Line Total: ₦{calculateItemTotal(item).toLocaleString()}</div>
            </motion.div>
          ))}
          <Button type="button" variant="outline" onClick={addBudgetItemLine} className="mt-2">
            <Plus className="mr-2 h-4 w-4" /> Add Line Item
          </Button>
        </CardContent>
        <CardFooter className="flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0">
            <div className="w-full md:w-2/3 space-y-1">
              <Label htmlFor="overallRemarks">Overall Remarks</Label>
              <Textarea id="overallRemarks" placeholder="Add any overall remarks for this budget" value={budgetState.remarks} onChange={(e) => setBudgetStateFunction(prev => ({ ...prev, remarks: e.target.value }))} className="w-full"/>
            </div>
            <div className="text-xl font-bold w-full md:w-auto text-right md:text-left mt-4 md:mt-0">Total: ₦{budgetState.totalAmount.toLocaleString()}</div>
        </CardFooter>
      </Card>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={() => { setShowAddBudgetForm(false); setEditingBudget(null); setNewBudgetData(initialBudgetData); }}>Cancel</Button>
        <Button type="submit" className="gradient-primary text-white">
          <Save className="mr-2 h-4 w-4" /> {editingBudget ? 'Save Changes' : 'Submit Budget'}
        </Button>
      </div>
    </form>
  );


  return (
    <div className="p-4 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Budget Management {currentProject ? `- ${currentProject.title}` : ''}
          </h1>
          <p className="text-gray-600">Track project costs and manage budget approvals.</p>
        </div>
        {(user?.role === 'project_lead' || user?.role === 'accountant' || user?.role === 'quantity_surveyor') && !showAddBudgetForm && !editingBudget && (
          <Button onClick={() => { setShowAddBudgetForm(true); setEditingBudget(null); setNewBudgetData(initialBudgetData); }} className="gradient-primary text-white">
            <Plus className="mr-2 h-4 w-4" /> Create New Budget
          </Button>
        )}
      </motion.div>

      {(showAddBudgetForm || editingBudget) && (
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle>{editingBudget ? 'Edit Budget' : 'Create New Budget'}</CardTitle>
              <CardDescription>{editingBudget ? 'Modify the budget details below.' : 'Fill in the details to create a new budget.'}</CardDescription>
            </CardHeader>
            <CardContent>
              {editingBudget ? budgetFormContent(editingBudget, setEditingBudget) : budgetFormContent(newBudgetData, setNewBudgetData)}
            </CardContent>
          </Card>
        </motion.div>
      )}
      
      {!showAddBudgetForm && !editingBudget && (
        <motion.div className="space-y-4" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.2 }}>
          {projectBudgets.length === 0 && <p className="text-center text-gray-500 py-8">No budgets submitted for this project yet.</p>}
          {projectBudgets.map((budget, index) => {
            const canEdit = (user?.role === 'project_lead' && budget.status === 'revision_needed') ||
                            (user?.role === 'accountant' && budget.status === 'pending_accountant') ||
                            (user?.role === 'quantity_surveyor' && (budget.status === 'pending_accountant' || budget.status === 'revision_needed' ));
            const projectForBudget = projects.find(p => p.id === budget.projectId);
            
            return(
            <motion.div 
              key={budget.id} 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="glass border-white/20">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>Budget ID: {budget.id} {projectForBudget && `(${projectForBudget.title})`}</CardTitle>
                      <CardDescription>Submitted by: {budget.submittedBy} on {new Date(budget.timestamp).toLocaleDateString()}</CardDescription>
                    </div>
                    {getStatusChip(budget.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 mb-4">
                    {budget.items.map(item => (
                      <div key={item.id} className="flex justify-between items-center text-sm p-2 border-b">
                        <span>{item.description} ({item.quantity} x ₦{parseFloat(item.unitPrice).toLocaleString()})</span>
                        <span className="font-medium">₦{item.total.toLocaleString()}</span>
                      </div>
                    ))}
                  </div>
                   {budget.remarks && <p className="text-sm text-gray-600 italic mt-2 mb-3">Remarks: {budget.remarks}</p>}
                </CardContent>
                <CardFooter className="flex flex-col sm:flex-row justify-between items-center">
                  <p className="text-lg font-bold">Total: ₦{budget.totalAmount.toLocaleString()}</p>
                  <div className="flex space-x-2 mt-2 sm:mt-0">
                    {canEdit && (
                       <Button variant="outline" size="sm" onClick={() => { setEditingBudget({...budget}); setShowAddBudgetForm(false);}}> <Edit className="mr-2 h-4 w-4" /> Edit </Button>
                    )}
                    {user?.role === 'accountant' && budget.status === 'pending_accountant' && (
                      <>
                        <Button
                          size="sm"
                          className="bg-green-600 hover:bg-green-700 text-white"
                          onClick={() => handleAccountantAction(budget.id, 'push_to_admin')}
                          disabled={loadingActions[`${budget.id}-push_to_admin`]}
                        >
                          <Send className="mr-2 h-4 w-4" />
                          {loadingActions[`${budget.id}-push_to_admin`] ? 'Pushing...' : 'Push to Admin'}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleAccountantAction(budget.id, 'return_to_lead')}
                          disabled={loadingActions[`${budget.id}-return_to_lead`]}
                        >
                          <CornerDownLeft className="mr-2 h-4 w-4" />
                          {loadingActions[`${budget.id}-return_to_lead`] ? 'Returning...' : 'Return to Lead'}
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleAccountantAction(budget.id, 'reject')}
                          disabled={loadingActions[`${budget.id}-reject`]}
                        >
                          <AlertCircle className="mr-2 h-4 w-4" />
                          {loadingActions[`${budget.id}-reject`] ? 'Rejecting...' : 'Reject'}
                        </Button>
                      </>
                    )}
                    {user?.role === 'admin' && budget.status === 'pending_admin' && (
                      <>
                        <Button
                          size="sm"
                          className="bg-green-600 hover:bg-green-700 text-white"
                          onClick={() => handleAdminAction(budget.id, 'approve')}
                          disabled={loadingActions[`${budget.id}-approve`]}
                        >
                          {loadingActions[`${budget.id}-approve`] ? 'Approving...' : 'Approve'}
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleAdminAction(budget.id, 'reject')}
                          disabled={loadingActions[`${budget.id}-reject`]}
                        >
                          {loadingActions[`${budget.id}-reject`] ? 'Rejecting...' : 'Reject'}
                        </Button>
                      </>
                    )}
                  </div>
                </CardFooter>
              </Card>
            </motion.div>
          )})}
        </motion.div>
      )}
    </div>
  );
};

export default Budget;
