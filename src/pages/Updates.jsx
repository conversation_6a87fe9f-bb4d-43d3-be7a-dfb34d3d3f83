import React, { useState, useEffect } from 'react'; // Added useEffect
import { useLocation } from 'react-router-dom'; // Added useLocation
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Plus, MessageSquare, Clock, User, Send, CornerDownLeft, Filter, FileText, Trash2 } from 'lucide-react'; // Added Filter, FileText, Trash2
import { Checkbox } from "@/components/ui/checkbox"; // Added Checkbox

const ReplyForm = ({ dailyLogId, parentReplyId, onReplySubmitted, onCancel }) => { // Changed updateId to dailyLogId
  const [replyText, setReplyText] = useState('');
  const [replyAttachment, setReplyAttachment] = useState(null);
  const { addReplyToDailyLog } = useProjects(); // Changed addReplyToUpdate to addReplyToDailyLog
  const { user } = useAuth();

  const handleReplySubmit = (e) => {
    e.preventDefault();
    if (!replyText.trim()) {
      toast({ title: "Reply cannot be empty", variant: "destructive" });
      return;
    }
    const attachments = replyAttachment ? [{ name: replyAttachment.name, type: replyAttachment.type, size: replyAttachment.size, url: URL.createObjectURL(replyAttachment) }] : [];
    addReplyToDailyLog(dailyLogId, replyText, parentReplyId, attachments);
    setReplyText('');
    setReplyAttachment(null);
    if (onReplySubmitted) onReplySubmitted();
  };

  return (
    <motion.form 
      onSubmit={handleReplySubmit} 
      className="mt-2 ml-4 pl-4 border-l-2 border-gray-200 space-y-2"
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
    >
      <Textarea
        placeholder={`Reply as ${user.name}...`}
        value={replyText}
        onChange={(e) => setReplyText(e.target.value)}
        rows={2}
        required
      />
      <div className="space-y-1">
        <Label htmlFor={`reply-attachment-${parentReplyId || dailyLogId}`} className="text-xs">Attach File (Optional)</Label>
        <Input id={`reply-attachment-${parentReplyId || dailyLogId}`} type="file" onChange={(e) => setReplyAttachment(e.target.files[0])} className="text-xs h-8" />
      </div>
      <div className="flex justify-end space-x-2">
        {onCancel && <Button type="button" variant="ghost" size="sm" onClick={onCancel}>Cancel</Button>}
        <Button type="submit" size="sm" className="gradient-primary text-white"><Send className="mr-1 h-3 w-3" />Post Reply</Button>
      </div>
    </motion.form>
  );
};

const ReplyThread = ({ replies, dailyLogId, level = 0, onDeleteReply }) => { // Changed updateId to dailyLogId, added onDeleteReply
  const [replyingTo, setReplyingTo] = useState(null);
  const { user } = useAuth();

  if (!replies || replies.length === 0) return null;

  return (
    <div className={`mt-3 ${level > 0 ? 'ml-6 pl-6 border-l-2 border-gray-200' : ''}`}>
      {replies.map(reply => (
        <motion.div 
          key={reply.id} 
          className="py-2"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: level * 0.1 }}
        >
          <div className="flex items-start space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${reply.author}`} />
              <AvatarFallback>{reply.author.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-2 justify-between">
                <div>
                    <span className="font-medium text-sm">{reply.author}</span>
                    <span className="text-xs text-gray-500 ml-2">{new Date(reply.timestamp).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })} at {new Date(reply.timestamp).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}</span>
                </div>
                {user.name === reply.author && reply.text !== "[Message deleted by user]" && (
                    <Button variant="ghost" size="icon" className="h-6 w-6 text-red-500 hover:bg-red-100" onClick={() => onDeleteReply(dailyLogId, reply.id, true)}>
                        <Trash2 className="h-3 w-3" />
                    </Button>
                )}
              </div>
              <p className="text-sm text-gray-700 mt-1">{reply.text}</p>
              {reply.attachments && reply.attachments.map((att, idx) => (
                 <a key={idx} href={att.url} target="_blank" rel="noopener noreferrer" className="text-xs text-blue-600 hover:underline block mt-1">{att.name} ({Math.round(att.size / 1024)}KB)</a>
              ))}
              {reply.text !== "[Message deleted by user]" && (
                <Button variant="link" size="sm" className="p-0 h-auto text-xs" onClick={() => setReplyingTo(replyingTo === reply.id ? null : reply.id)}>
                    <CornerDownLeft className="mr-1 h-3 w-3" /> Reply
                </Button>
              )}
            </div>
          </div>
          {replyingTo === reply.id && (
            <ReplyForm 
              dailyLogId={dailyLogId} 
              parentReplyId={reply.id} 
              onReplySubmitted={() => setReplyingTo(null)}
              onCancel={() => setReplyingTo(null)}
            />
          )}
          {reply.replies && reply.replies.length > 0 && (
            <ReplyThread replies={reply.replies} dailyLogId={dailyLogId} level={level + 1} onDeleteReply={onDeleteReply} />
          )}
        </motion.div>
      ))}
    </div>
  );
};


const DailyLogPage = () => { // Renamed from Updates
  const { user } = useAuth();
  const { projects, dailyLogs, addDailyLog, addReplyToDailyLog, deleteDailyLogMessage } = useProjects(); // Changed updates to dailyLogs, addUpdate to addDailyLog
  const [showForm, setShowForm] = useState(false);
  const [replyingToDailyLogId, setReplyingToDailyLogId] = useState(null); // Changed replyingToUpdateId
  
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const initialProjectIdFilter = queryParams.get('projectId') || 'all';
  const [projectIdFilter, setProjectIdFilter] = useState(initialProjectIdFilter);
  
  const [formData, setFormData] = useState({
    projectId: initialProjectIdFilter !== 'all' ? initialProjectIdFilter : '',
    title: '',
    description: '',
    type: 'update',
    isClientVisible: false,
    attachment: null
  });

  useEffect(() => {
    setFormData(prev => ({...prev, projectId: initialProjectIdFilter !== 'all' ? initialProjectIdFilter : ''}));
  }, [initialProjectIdFilter]);


  const handleSubmit = (e) => {
    e.preventDefault();
    
    const attachments = formData.attachment ? [{ name: formData.attachment.name, type: formData.attachment.type, size: formData.attachment.size, url: URL.createObjectURL(formData.attachment) }] : [];
    
    const logData = { // Changed updateData to logData
      ...formData,
      author: user.name,
      authorRole: user.role,
      attachments
    };

    addDailyLog(logData); // Changed addUpdate to addDailyLog
    
    toast({
      title: "Daily Log added successfully!", // Changed "Update" to "Daily Log"
      description: "Your project daily log has been recorded.",
    });

    setFormData({
      projectId: projectIdFilter !== 'all' ? projectIdFilter : '',
      title: '',
      description: '',
      type: 'update',
      isClientVisible: false,
      attachment: null
    });
    setShowForm(false);
  };

  const getDailyLogTypeColor = (type) => { // Changed getUpdateTypeColor
    switch (type) {
      case 'progress': return 'bg-blue-100 text-blue-800';
      case 'milestone': return 'bg-green-100 text-green-800';
      case 'issue': return 'bg-red-100 text-red-800';
      case 'note': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredDailyLogs = dailyLogs.filter(log => { // Changed filteredUpdates
    const projectVisibility = projectIdFilter === 'all' || log.projectId === projectIdFilter;
    if (user?.role === 'client') {
      const project = projects.find(p => p.id === log.projectId);
      return projectVisibility && log.isClientVisible && project?.client === user.name;
    }
    return projectVisibility;
  }).sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp));

  const handleDeleteMessage = (logId, messageId, isReply = false) => {
    if(window.confirm("Are you sure you want to delete this message? This action cannot be undone.")) {
        deleteDailyLogMessage(logId, messageId, isReply);
        toast({ title: "Message Deleted", description: "The message has been removed." });
    }
  };

  return (
    <div className="p-4 space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Daily Logs</h1> {/* Changed Project Updates to Daily Logs */}
          <p className="text-gray-600">Track progress and communicate with your team</p>
        </div>
        <div className="flex items-center space-x-2">
            <Select value={projectIdFilter} onValueChange={setProjectIdFilter}>
                <SelectTrigger className="w-[180px]"><Filter className="h-4 w-4 mr-2"/><SelectValue placeholder="Filter by project" /></SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">All Projects</SelectItem>
                    {projects.map(p => <SelectItem key={p.id} value={p.id}>{p.title}</SelectItem>)}
                </SelectContent>
            </Select>
            {user?.role !== 'client' && (
              <Button onClick={() => setShowForm(!showForm)} className="gradient-primary text-white">
                <Plus className="mr-2 h-4 w-4" /> Add Log {/* Changed "Update" to "Log" */}
              </Button>
            )}
        </div>
      </motion.div>

      {showForm && (
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle>Add New Daily Log</CardTitle> {/* Changed "Update" to "Daily Log" */}
              <CardDescription>Share progress, milestones, or important notes about your project</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="project">Project *</Label>
                    <Select value={formData.projectId} onValueChange={(value) => setFormData(prev => ({ ...prev, projectId: value }))} required>
                      <SelectTrigger><SelectValue placeholder="Select project" /></SelectTrigger>
                      <SelectContent>
                        {projects.map((project) => (
                          <SelectItem key={project.id} value={project.id}>{project.title}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type">Log Type *</Label> {/* Changed "Update Type" to "Log Type" */}
                    <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
                      <SelectTrigger><SelectValue placeholder="Select type" /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="update">Progress Update</SelectItem>
                        <SelectItem value="milestone">Milestone</SelectItem>
                        <SelectItem value="issue">Issue/Problem</SelectItem>
                        <SelectItem value="note">General Note</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="title">Title *</Label>
                  <Input id="title" placeholder="Enter log title" value={formData.title} onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea id="description" placeholder="Describe the log in detail" value={formData.description} onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))} rows={4} required />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="attachment">Attach File (Optional)</Label>
                    <Input id="attachment" type="file" onChange={(e) => setFormData(prev => ({...prev, attachment: e.target.files[0]}))} />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="clientVisible" checked={formData.isClientVisible} onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isClientVisible: checked }))} />
                  <Label htmlFor="clientVisible">Make visible to client</Label>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setShowForm(false)}>Cancel</Button>
                  <Button type="submit" className="gradient-primary text-white">Add Log</Button> {/* Changed "Update" to "Log" */}
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      )}

      <div className="space-y-4">
        {filteredDailyLogs.length > 0 ? (
          filteredDailyLogs.map((log, index) => { // Changed update to log
            const project = projects.find(p => p.id === log.projectId);
            return (
              <motion.div key={log.id} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 + index * 0.1 }}>
                <Card className="card-hover glass border-white/20">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{log.title}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDailyLogTypeColor(log.type)}`}>{log.type.toUpperCase()}</span>
                          {log.isClientVisible && (<span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">CLIENT VISIBLE</span>)}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">Project: <span className="font-medium">{project?.title}</span></p>
                      </div>
                      <div className="text-right text-sm text-gray-500">
                        <div className="flex items-center space-x-1 mb-1"><Clock className="h-3 w-3" /><span>{new Date(log.timestamp).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}</span></div>
                        <div className="flex items-center space-x-1"><User className="h-3 w-3" /><span>{log.author}</span></div>
                        {user.name === log.author && log.description !== "[Message deleted by user]" && (
                            <Button variant="ghost" size="icon" className="h-6 w-6 text-red-500 hover:bg-red-100 ml-auto" onClick={() => handleDeleteMessage(log.id, log.id, false)}>
                                <Trash2 className="h-3 w-3" />
                            </Button>
                        )}
                      </div>
                    </div>
                    <p className="text-gray-700">{log.description}</p>
                    {log.attachments && log.attachments.map((att, idx) => (
                        <a key={idx} href={att.url} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 hover:underline block mt-2">{att.name} ({Math.round(att.size / 1024)}KB)</a>
                    ))}
                    {log.description !== "[Message deleted by user]" && (user.role === 'client' ? log.isClientVisible : true) && (
                        <Button variant="link" size="sm" className="p-0 h-auto text-xs mt-2" onClick={() => setReplyingToDailyLogId(replyingToDailyLogId === log.id ? null : log.id)}>
                            <CornerDownLeft className="mr-1 h-3 w-3" /> Reply to Log
                        </Button>
                    )}
                    {replyingToDailyLogId === log.id && (
                      <ReplyForm 
                        dailyLogId={log.id} 
                        onReplySubmitted={() => setReplyingToDailyLogId(null)}
                        onCancel={() => setReplyingToDailyLogId(null)}
                      />
                    )}
                    <ReplyThread replies={log.replies} dailyLogId={log.id} onDeleteReply={handleDeleteMessage} />
                  </CardContent>
                </Card>
              </motion.div>
            );
          })
        ) : (
          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.3 }} className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center"><FileText className="h-12 w-12 text-gray-400" /></div> {/* Changed icon */}
            <h3 className="text-lg font-medium text-gray-900 mb-2">No daily logs yet</h3> {/* Changed "updates" to "daily logs" */}
            <p className="text-gray-600 mb-4">{user?.role === 'client' ? 'No daily logs have been shared with you yet.' : 'Start by adding your first project daily log.'}</p>
            {user?.role !== 'client' && (<Button onClick={() => setShowForm(true)} className="gradient-primary text-white"><Plus className="mr-2 h-4 w-4" />Add First Log</Button>)} {/* Changed "Update" to "Log" */}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default DailyLogPage; // Renamed from Updates