import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Plus, Send, Check, X, Edit, Package, PackageCheck, PackageX, History, CornerDownLeft, RotateCcw } from 'lucide-react';
import { generateId } from '@/utils/storage';

const MaterialRequests = () => {
  const { user } = useAuth();
  const { projects, materialRequests, addMaterialRequest, updateMaterialRequest } = useProjects();
  const [showRequestForm, setShowRequestForm] = useState(false);
  const [editingRequest, setEditingRequest] = useState(null); 
  const [leadReviewNotes, setLeadReviewNotes] = useState('');

  const initialRequestData = {
    projectId: '',
    items: [{ id: generateId(), name: '', quantity: '' }],
    notes: '',
    leadReviewNotes: ''
  };
  const [newRequestData, setNewRequestData] = useState(initialRequestData);

  const handleItemChange = (index, field, value, isNewForm = true) => {
    const currentData = isNewForm ? newRequestData : editingRequest;
    const setDataFunction = isNewForm ? setNewRequestData : setEditingRequest;

    const updatedItems = currentData.items.map((item, i) => 
      i === index ? { ...item, [field]: value } : item
    );
    setDataFunction(prev => ({ ...prev, items: updatedItems }));
  };

  const addItemLine = (isNewForm = true) => {
    const setDataFunction = isNewForm ? setNewRequestData : setEditingRequest;
    setDataFunction(prev => ({
      ...prev,
      items: [...prev.items, { id: generateId(), name: '', quantity: '' }]
    }));
  };

  const removeItemLine = (index, isNewForm = true) => {
    const currentData = isNewForm ? newRequestData : editingRequest;
    const setDataFunction = isNewForm ? setNewRequestData : setEditingRequest;
    const updatedItems = currentData.items.filter((_, i) => i !== index);
    setDataFunction(prev => ({ ...prev, items: updatedItems }));
  };

  const handleSubmitRequest = (e) => {
    e.preventDefault();
    if (!newRequestData.projectId || newRequestData.items.some(item => !item.name || !item.quantity)) {
      toast({ title: "Incomplete Request", description: "Please fill all required fields.", variant: "destructive" });
      return;
    }
    addMaterialRequest(newRequestData);
    toast({ title: "Request Submitted", description: "Your material request has been sent for approval." });
    setNewRequestData(initialRequestData);
    setShowRequestForm(false);
  };

  const handleLeadAction = (requestId, action, notes = '') => {
    let statusUpdate = { leadReviewNotes: notes };
    let toastMessage = "";

    switch(action) {
        case 'approve':
            statusUpdate.status = 'approved_by_lead';
            toastMessage = "Request Approved and sent to Store Keeper.";
            break;
        case 'reject':
            statusUpdate.status = 'rejected_by_lead';
            toastMessage = "Request Rejected.";
            break;
        case 'review':
            statusUpdate.status = 'needs_review_by_engineer';
            toastMessage = "Request sent back to Engineer for review.";
            break;
        default:
            return;
    }
    updateMaterialRequest(requestId, statusUpdate);
    toast({ title: `Request ${action}d`, description: toastMessage });
    setLeadReviewNotes(''); 
  };

  const handleStoreKeeperAction = (requestId, action, notes) => {
    updateMaterialRequest(requestId, { storeStatus: action, storeKeeperNotes: notes, storeTimestamp: new Date().toISOString() });
    toast({ title: "Store Status Updated", description: `Request marked as ${action.replace('_', ' ')}.` });
    setEditingRequest(null);
  };
  
  const getStatusChip = (status, storeStatus) => {
    let Icon, color, text;
    if (storeStatus) {
        switch (storeStatus) {
            case 'delivered': Icon = PackageCheck; color = 'bg-green-100 text-green-800'; text = 'Delivered'; break;
            case 'partially_delivered': Icon = History; color = 'bg-yellow-100 text-yellow-800'; text = 'Partially Delivered'; break;
            case 'unavailable': Icon = PackageX; color = 'bg-red-100 text-red-800'; text = 'Unavailable'; break;
            default: Icon = Package; color = 'bg-gray-100 text-gray-800'; text = storeStatus; break;
        }
    } else {
        switch (status) {
            case 'pending_lead_approval': Icon = Send; color = 'bg-blue-100 text-blue-800'; text = 'Pending Lead Approval'; break;
            case 'approved_by_lead': Icon = Check; color = 'bg-teal-100 text-teal-800'; text = 'Approved (Pending Store)'; break;
            case 'rejected_by_lead': Icon = X; color = 'bg-pink-100 text-pink-800'; text = 'Rejected by Lead'; break;
            case 'needs_review_by_engineer': Icon = RotateCcw; color = 'bg-orange-100 text-orange-800'; text = 'Needs Review (Engineer)'; break;
            default: Icon = Package; color = 'bg-gray-100 text-gray-800'; text = status; break;
        }
    }
    return <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}><Icon className="mr-1.5 h-3 w-3" />{text}</span>;
  };

  const requestFormContent = (data, setDataFunc, isNew) => (
    <form onSubmit={isNew ? handleSubmitRequest : (e) => e.preventDefault()} className="space-y-4">
      {isNew && (
        <div className="space-y-2">
          <Label htmlFor="project">Project *</Label>
          <Select value={data.projectId} onValueChange={(value) => setDataFunc(prev => ({ ...prev, projectId: value }))} required>
            <SelectTrigger><SelectValue placeholder="Select project" /></SelectTrigger>
            <SelectContent>{projects.map(p => (<SelectItem key={p.id} value={p.id}>{p.title}</SelectItem>))}</SelectContent>
          </Select>
        </div>
      )}
      <Card><CardHeader><CardTitle className="text-base">Requested Items</CardTitle></CardHeader><CardContent className="space-y-3">
        {data.items.map((item, index) => (
          <div key={item.id} className="grid grid-cols-12 gap-2 items-center">
            <div className="col-span-6"><Input placeholder="Item Name" value={item.name} onChange={(e) => handleItemChange(index, 'name', e.target.value, isNew)} required /></div>
            <div className="col-span-4"><Input type="number" placeholder="Quantity" value={item.quantity} onChange={(e) => handleItemChange(index, 'quantity', e.target.value, isNew)} required /></div>
            <div className="col-span-2">{data.items.length > 1 && <Button type="button" variant="ghost" size="icon" onClick={() => removeItemLine(index, isNew)} className="text-red-500"><X className="h-4 w-4" /></Button>}</div>
          </div>
        ))}
        <Button type="button" variant="outline" size="sm" onClick={() => addItemLine(isNew)}><Plus className="mr-1 h-3 w-3" />Add Item</Button>
      </CardContent></Card>
      <div className="space-y-2">
        <Label htmlFor="notes">{isNew ? 'Notes for Project Lead' : 'Notes for Store Keeper'}</Label>
        <Textarea id="notes" placeholder="Add any relevant notes..." value={data.notes} onChange={(e) => setDataFunc(prev => ({ ...prev, notes: e.target.value }))} />
      </div>
      {isNew && (
        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={() => { setShowRequestForm(false); setNewRequestData(initialRequestData); }}>Cancel</Button>
          <Button type="submit" className="gradient-primary text-white"><Send className="mr-2 h-4 w-4" />Submit Request</Button>
        </div>
      )}
    </form>
  );

  const storeKeeperFormContent = (request) => (
    <form onSubmit={(e) => { e.preventDefault(); handleStoreKeeperAction(request.id, editingRequest.storeStatus, editingRequest.storeKeeperNotes); }} className="space-y-3 mt-2">
        <Select value={editingRequest.storeStatus || ''} onValueChange={(value) => setEditingRequest(prev => ({...prev, storeStatus: value}))}>
            <SelectTrigger><SelectValue placeholder="Update Store Status" /></SelectTrigger>
            <SelectContent>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="partially_delivered">Partially Delivered</SelectItem>
                <SelectItem value="unavailable">Unavailable</SelectItem>
            </SelectContent>
        </Select>
        <Textarea placeholder="Store Keeper Notes (e.g., quantity delivered if partial)" value={editingRequest.storeKeeperNotes || ''} onChange={(e) => setEditingRequest(prev => ({...prev, storeKeeperNotes: e.target.value}))} />
        <div className="flex justify-end space-x-2">
            <Button type="button" variant="ghost" size="sm" onClick={() => setEditingRequest(null)}>Cancel</Button>
            <Button type="submit" size="sm" className="bg-green-600 hover:bg-green-700 text-white">Save Store Update</Button>
        </div>
    </form>
  );

  const visibleRequests = materialRequests.filter(req => {
    if (user.role === 'admin' || user.role === 'store_keeper') return true;
    if (user.role === 'project_lead') return projects.find(p => p.id === req.projectId && p.projectLead === user.name);
    if (user.role === 'team_member') return req.requestedBy === user.name;
    return false;
  }).sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp));

  return (
    <div className="p-4 space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div><h1 className="text-3xl font-bold text-gray-900">Material Requests</h1><p className="text-gray-600">Manage material requisitions for projects.</p></div>
        {(user.role === 'team_member' || user.role === 'project_lead') && !showRequestForm && (
          <Button onClick={() => { setShowRequestForm(true); setNewRequestData(initialRequestData); }} className="gradient-primary text-white"><Plus className="mr-2 h-4 w-4" />New Request</Button>
        )}
      </motion.div>

      {showRequestForm && requestFormContent(newRequestData, setNewRequestData, true)}

      <div className="space-y-4">
        {visibleRequests.length === 0 && !showRequestForm && <p className="text-center text-gray-500 py-8">No material requests found.</p>}
        {visibleRequests.map((req, index) => {
          const project = projects.find(p => p.id === req.projectId);
          return (
            <motion.div key={req.id} initial={{ opacity: 0, y: 15 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: index * 0.05 }}>
              <Card className="glass border-white/20">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>Request ID: {req.id.substring(0,8)}</CardTitle>
                      <CardDescription>For: {project?.title || 'N/A'} | By: {req.requestedBy} on {new Date(req.timestamp).toLocaleDateString()}</CardDescription>
                    </div>
                    {getStatusChip(req.status, req.storeStatus)}
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="list-disc list-inside space-y-1 text-sm mb-2">
                    {req.items.map(item => <li key={item.id}>{item.name} - Quantity: {item.quantity}</li>)}
                  </ul>
                  {req.notes && <p className="text-sm text-gray-600 italic">Engineer Notes: {req.notes}</p>}
                  {req.leadReviewNotes && <p className="text-sm text-orange-700 italic mt-1">Lead Review Notes: {req.leadReviewNotes}</p>}
                  {req.storeKeeperNotes && <p className="text-sm text-gray-600 italic mt-1">Store Notes: {req.storeKeeperNotes} (Updated: {req.storeTimestamp ? new Date(req.storeTimestamp).toLocaleDateString() : 'N/A'})</p>}
                </CardContent>
                <CardFooter className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-2">
                  {user.role === 'project_lead' && req.status === 'pending_lead_approval' && (
                    <>
                      <Textarea placeholder="Notes for engineer (if returning for review)" value={leadReviewNotes} onChange={(e) => setLeadReviewNotes(e.target.value)} className="mb-2 sm:mb-0 sm:w-1/2"/>
                      <Button size="sm" className="bg-green-500 hover:bg-green-600 text-white" onClick={() => handleLeadAction(req.id, 'approve', leadReviewNotes)}><Check className="mr-1 h-4 w-4" />Approve</Button>
                      <Button size="sm" variant="outline" onClick={() => handleLeadAction(req.id, 'review', leadReviewNotes)}><RotateCcw className="mr-1 h-4 w-4" />Return for Review</Button>
                      <Button size="sm" variant="destructive" onClick={() => handleLeadAction(req.id, 'reject', leadReviewNotes)}><X className="mr-1 h-4 w-4" />Reject</Button>
                    </>
                  )}
                  {user.role === 'store_keeper' && req.status === 'approved_by_lead' && !editingRequest && (
                    <Button size="sm" variant="outline" onClick={() => setEditingRequest({...req, storeStatus: req.storeStatus || '', storeKeeperNotes: req.storeKeeperNotes || ''})}><Edit className="mr-1 h-4 w-4" />Update Store Status</Button>
                  )}
                  {user.role === 'team_member' && req.requestedBy === user.name && req.status === 'needs_review_by_engineer' && (
                     <Button size="sm" variant="outline" onClick={() => { /* Logic to edit and resubmit */ toast({title: "Resubmit functionality to be added."}) }}>
                        <Edit className="mr-1 h-4 w-4" /> Edit & Resubmit Request
                    </Button>
                  )}
                </CardFooter>
                {editingRequest && editingRequest.id === req.id && user.role === 'store_keeper' && (
                    <CardContent>{storeKeeperFormContent(req)}</CardContent>
                )}
              </Card>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default MaterialRequests;