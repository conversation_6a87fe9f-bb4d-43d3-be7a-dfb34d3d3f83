import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Plus, MessageSquare, Filter, FileText } from 'lucide-react';
import DailyLogForm from '@/components/dailylog/DailyLogForm';
import DailyLogItem from '@/components/dailylog/DailyLogItem';

const DailyLogPage = () => {
  const { user } = useAuth();
  const { projects, dailyLogs, addDailyLog, deleteDailyLogMessage } = useProjects();
  const [showForm, setShowForm] = useState(false);
  
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const initialProjectIdFilter = queryParams.get('projectId') || 'all';
  const [projectIdFilter, setProjectIdFilter] = useState(initialProjectIdFilter);

  const handleFormSubmit = (logData) => {
    addDailyLog(logData);
    toast({
      title: "Daily Log added successfully!",
      description: "Your project daily log has been recorded.",
    });
    setShowForm(false);
  };

  const handleDeleteLogMessage = async (logId, messageId, isReply = false) => {
    if(window.confirm("Are you sure you want to delete this message? This action cannot be undone.")) {
      const result = await deleteDailyLogMessage(logId, messageId, isReply);
      if (result.success) {
        toast({
          title: "Message Deleted",
          description: isReply ? "The reply has been removed." : "The daily log has been removed."
        });
      } else {
        toast({
          title: "Delete Failed",
          description: result.error || "Failed to delete message. Please try again.",
          variant: "destructive"
        });
      }
    }
  };

  const getVisibleProjectsForFilter = () => {
    if (user.role === 'client') {
      return projects.filter(p => p.client === user.name);
    }
    return projects;
  };

  const filteredDailyLogs = dailyLogs.filter(log => {
    const projectMatch = projectIdFilter === 'all' || log.projectId === projectIdFilter;
    if (user?.role === 'client') {
      const project = projects.find(p => p.id === log.projectId);
      return projectMatch && log.isClientVisible && project?.client === user.name;
    }
    return projectMatch;
  }).sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp));

  return (
    <div className="p-4 space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Daily Logs</h1>
          <p className="text-gray-600 dark:text-gray-400">Track progress and communicate with your team</p>
        </div>
        <div className="flex items-center space-x-2">
            <Select value={projectIdFilter} onValueChange={setProjectIdFilter}>
                <SelectTrigger className="w-[180px]"><Filter className="h-4 w-4 mr-2"/><SelectValue placeholder="Filter by project" /></SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">All Projects</SelectItem>
                    {getVisibleProjectsForFilter().map(p => <SelectItem key={p.id} value={p.id}>{p.title}</SelectItem>)}
                </SelectContent>
            </Select>
            {user?.role !== 'client' && (
              <Button onClick={() => setShowForm(!showForm)} className="gradient-primary text-white">
                <Plus className="mr-2 h-4 w-4" /> Add Log
              </Button>
            )}
        </div>
      </motion.div>

      {showForm && (
        <DailyLogForm 
          projects={projects} 
          onSubmit={handleFormSubmit} 
          onCancel={() => setShowForm(false)}
          initialProjectId={projectIdFilter}
        />
      )}

      <div className="space-y-4">
        {filteredDailyLogs.length > 0 ? (
          filteredDailyLogs.map((log, index) => {
            const project = projects.find(p => p.id === log.projectId);
            return (
              <DailyLogItem 
                key={log.id}
                log={log}
                project={project}
                index={index}
                onDeleteLog={handleDeleteLogMessage}
              />
            );
          })
        ) : (
          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.3 }} className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center"><FileText className="h-12 w-12 text-gray-400" /></div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No daily logs yet</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{user?.role === 'client' ? 'No daily logs have been shared with you yet.' : 'Start by adding your first project daily log.'}</p>
            {user?.role !== 'client' && (<Button onClick={() => setShowForm(true)} className="gradient-primary text-white"><Plus className="mr-2 h-4 w-4" />Add First Log</Button>)}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default DailyLogPage;