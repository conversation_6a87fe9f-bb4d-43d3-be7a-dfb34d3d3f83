import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Upload } from 'lucide-react';
import { motion } from 'framer-motion';
import MediaUploadForm from '@/components/media/MediaUploadForm';
import MediaGrid from '@/components/media/MediaGrid';
import MediaFilters from '@/components/media/MediaFilters';

const Media = () => {
  const { user } = useAuth();
  const { projects, media: allMedia, addMediaItem } = useProjects();
  const [searchParams] = useSearchParams();
  const [showUpload, setShowUpload] = useState(false);
  const [selectedProject, setSelectedProject] = useState('all');
  const [selectedType, setSelectedType] = useState('all');

  // Get projectId from URL parameters
  const urlProjectId = searchParams.get('projectId');

  // Set initial project selection from URL
  useEffect(() => {
    if (urlProjectId && projects.some(p => p.id === urlProjectId)) {
      setSelectedProject(urlProjectId);
      setShowUpload(true); // Auto-open upload form when coming from a specific project
    }
  }, [urlProjectId, projects]);

  const handleUploadComplete = (cancelled = false) => {
    if (!cancelled) {
      // Toast messages are handled inside MediaUploadForm
    }
    setShowUpload(false);
  };

  const filteredMedia = allMedia.filter(item => {
    const projectMatch = selectedProject === 'all' || item.projectId === selectedProject;
    const typeMatch = selectedType === 'all' || item.type === selectedType;
    if (user?.role === 'client') {
      const project = projects.find(p => p.id === item.projectId);
      return projectMatch && typeMatch && item.isClientVisible && project?.client === user.name;
    }
    return projectMatch && typeMatch;
  }).sort((a,b) => new Date(b.uploadDate) - new Date(a.uploadDate));

  return (
    <div className="p-4 space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div><h1 className="text-3xl font-bold text-gray-900">Project Media</h1><p className="text-gray-600">Manage project photos, videos, and documents</p></div>
        {user?.role !== 'client' && (<Button onClick={() => setShowUpload(!showUpload)} className="gradient-primary text-white"><Upload className="mr-2 h-4 w-4" />{showUpload ? 'Cancel Upload' : 'Upload Media'}</Button>)}
      </motion.div>

      {showUpload && <MediaUploadForm onUploadComplete={handleUploadComplete} initialProjectId={urlProjectId || ''} />}
      
      <MediaFilters 
        selectedProject={selectedProject} 
        setSelectedProject={setSelectedProject}
        selectedType={selectedType}
        setSelectedType={setSelectedType}
        mediaCount={filteredMedia.length}
      />

      {filteredMedia.length > 0 ? (
        <MediaGrid mediaItems={filteredMedia} />
      ) : (
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }} className="text-center py-12">
          <img  alt="No media files" className="mx-auto h-12 w-12 text-gray-400 mb-4" src="https://images.unsplash.com/photo-1663124178647-24f30cedd1ba" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No media found</h3>
          <p className="text-gray-600">{user?.role === 'client' ? "No media shared yet." : "Upload media to get started."}</p>
        </motion.div>
      )}
    </div>
  );
};

export default Media;