import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { motion } from 'framer-motion';
import { Bell, Palette, Settings as SettingsIcon, Lock, Mail, Eye, EyeOff, Save, RefreshCw } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { storage } from '@/utils/storage';
import { themeUtils } from '@/utils/theme';
import { useAuth } from '@/contexts/AuthContext';
import { settingsApi } from '@/services/settingsApi';
import apiService from '@/services/api';
import LoadingSpinner from '@/components/ui/LoadingSpinner';


const Settings = () => {
  const { user, updateUser } = useAuth();

  // Loading states
  const [loading, setLoading] = useState({
    notifications: false,
    appearance: false,
    security: false,
    zeptomail: false
  });

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    pushNotifications: false,
    projectUpdates: true,
    dailyDigest: false,
    weeklyReport: true,
    systemAlerts: true
  });

  // Appearance settings
  const [appearanceSettings, setAppearanceSettings] = useState(() =>
    themeUtils.getCurrentTheme()
  );

  // Security settings
  const [securitySettings, setSecuritySettings] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    sessionTimeout: storage.get('session_timeout') ?? '24h',
    loginNotifications: storage.get('login_notifications') ?? true
  });

  // ZeptoMail settings
  const [zeptomailSettings, setZeptomailSettings] = useState({
    apiKey: '',
    fromEmail: '',
    fromName: 'Karmod Project Hub',
    testEmail: user?.email ?? ''
  });

  // UI states
  const [showApiKey, setShowApiKey] = useState(false);
  const [testingEmail, setTestingEmail] = useState(false);

  // Load notification settings from backend
  useEffect(() => {
    const loadNotificationSettings = async () => {
      try {
        const result = await settingsApi.getNotificationSettings();
        if (result.success && result.data) {
          setNotificationSettings(result.data);
        }
      } catch (error) {
        console.error('Failed to load notification settings:', error);
        // Keep default settings if loading fails
      }
    };

    loadNotificationSettings();
  }, []);

  // Load ZeptoMail settings from backend (admin only)
  useEffect(() => {
    const loadZeptomailSettings = async () => {
      if (user?.role !== 'admin') return;

      try {
        const result = await settingsApi.getZeptomailSettings();
        if (result.success && result.data) {
          setZeptomailSettings(prev => ({
            ...prev,
            apiKey: result.data.zeptomail_api_key || '',
            fromEmail: result.data.zeptomail_from_email || '',
            fromName: result.data.zeptomail_from_name || 'Karmod Project Hub',
            testEmail: result.data.zeptomail_test_email || user?.email || ''
          }));
        }
      } catch (error) {
        console.error('Failed to load ZeptoMail settings:', error);
        // Keep default settings if loading fails
      }
    };

    loadZeptomailSettings();
  }, [user?.role]);

  // Apply theme on load
  useEffect(() => {
    if (appearanceSettings.darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Apply theme color
    document.documentElement.style.setProperty('--primary-color', appearanceSettings.themeColor);
  }, [appearanceSettings.darkMode, appearanceSettings.themeColor]);

  // Save notification settings
  const saveNotificationSettings = async () => {
    setLoading(prev => ({ ...prev, notifications: true }));
    try {
      // Save to backend
      const result = await settingsApi.updateNotificationSettings(notificationSettings);

      if (result.success) {
        // Also save to localStorage as backup
        Object.entries(notificationSettings).forEach(([key, value]) => {
          storage.set(key.replace(/([A-Z])/g, '_$1').toLowerCase(), value);
        });

        toast({
          title: "Notification Settings Saved",
          description: "Your notification preferences have been updated successfully.",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to save notification settings. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save notification settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, notifications: false }));
    }
  };

  // Save appearance settings
  const saveAppearanceSettings = async () => {
    setLoading(prev => ({ ...prev, appearance: true }));
    try {
      // Save to localStorage
      Object.entries(appearanceSettings).forEach(([key, value]) => {
        storage.set(key.replace(/([A-Z])/g, '_$1').toLowerCase(), value);
      });

      // Apply theme changes immediately using utility
      themeUtils.applyDarkMode(appearanceSettings.darkMode);
      themeUtils.applyThemeColor(appearanceSettings.themeColor);
      themeUtils.applyCompactMode(appearanceSettings.compactMode);

      toast({
        title: "Appearance Settings Saved",
        description: "Your appearance preferences have been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save appearance settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, appearance: false }));
    }
  };

  // Save security settings
  const saveSecuritySettings = async () => {
    setLoading(prev => ({ ...prev, security: true }));
    try {
      // Validate password change
      if (securitySettings.newPassword && securitySettings.newPassword !== securitySettings.confirmPassword) {
        toast({
          title: "Password Mismatch",
          description: "New password and confirmation password do not match.",
          variant: "destructive",
        });
        return;
      }

      if (securitySettings.newPassword && securitySettings.newPassword.length < 6) {
        toast({
          title: "Password Too Short",
          description: "Password must be at least 6 characters long.",
          variant: "destructive",
        });
        return;
      }

      // Save session settings
      storage.set('session_timeout', securitySettings.sessionTimeout);
      storage.set('login_notifications', securitySettings.loginNotifications);

      // If password change is requested
      if (securitySettings.newPassword && securitySettings.currentPassword) {
        try {
          await apiService.changePassword(
            securitySettings.currentPassword,
            securitySettings.newPassword
          );

          setSecuritySettings(prev => ({
            ...prev,
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
          }));
          toast({
            title: "Security Settings Updated",
            description: "Your password has been changed successfully.",
          });
        } catch (error) {
          toast({
            title: "Password Change Failed",
            description: error.message || "Failed to change password. Please check your current password.",
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Security Settings Saved",
          description: "Your security preferences have been updated successfully.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save security settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, security: false }));
    }
  };

  // Save ZeptoMail settings
  const saveZeptomailSettings = async () => {
    setLoading(prev => ({ ...prev, zeptomail: true }));
    try {
      const result = await settingsApi.updateZeptomailSettings({
        apiKey: zeptomailSettings.apiKey,
        fromEmail: zeptomailSettings.fromEmail,
        fromName: zeptomailSettings.fromName,
        testEmail: zeptomailSettings.testEmail
      });

      if (result.success) {
        toast({
          title: "ZeptoMail Settings Saved",
          description: "Your email integration settings have been securely saved on the server.",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to save ZeptoMail settings. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save ZeptoMail settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(prev => ({ ...prev, zeptomail: false }));
    }
  };

  // Test email functionality
  const testEmailSettings = async () => {
    if (!zeptomailSettings.testEmail) {
      toast({
        title: "Missing Information",
        description: "Please provide a test email address.",
        variant: "destructive",
      });
      return;
    }

    setTestingEmail(true);
    try {
      const result = await settingsApi.testEmailSettings(zeptomailSettings.testEmail);

      if (result.success) {
        toast({
          title: "Test Email Sent",
          description: result.message || `Test email sent successfully to ${zeptomailSettings.testEmail}`,
        });
      } else {
        toast({
          title: "Test Failed",
          description: result.error || "Failed to send test email. Please check your settings.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Test email error:', error);

      // Provide more specific error messages
      let errorMessage = "Failed to send test email. Please check your settings.";

      if (error.response?.status === 401) {
        errorMessage = "Invalid ZeptoMail API key. Please check your API key in the settings.";
      } else if (error.response?.status === 400) {
        errorMessage = "Invalid email configuration. Please check your from email and other settings.";
      } else if (error.message?.includes('Network Error')) {
        errorMessage = "Network error. Please check your internet connection and try again.";
      } else if (result?.error) {
        errorMessage = result.error;
      }

      toast({
        title: "Test Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setTestingEmail(false);
    }
  };

  return (
    <div className="p-4 space-y-6 max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Manage your application preferences and account settings.</p>
      </motion.div>

      {/* General Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle className="flex items-center">
              <SettingsIcon className="mr-2 h-5 w-5 text-gray-600" />
              General Settings
            </CardTitle>
            <CardDescription>Basic application preferences and user information.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="user-name">Display Name</Label>
                <Input
                  id="user-name"
                  value={user?.name || ''}
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-gray-500">Contact admin to change your name</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="user-email">Email Address</Label>
                <Input
                  id="user-email"
                  type="email"
                  value={user?.email || ''}
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-gray-500">Contact admin to change your email</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="user-role">Role</Label>
                <Input
                  id="user-role"
                  value={user?.role?.replace('_', ' ').toUpperCase() || ''}
                  disabled
                  className="bg-gray-50"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="user-company">Company</Label>
                <Input
                  id="user-company"
                  value={user?.company || 'Not specified'}
                  disabled
                  className="bg-gray-50"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Notifications Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bell className="mr-2 h-5 w-5 text-blue-600" />
              Notification Settings
            </CardTitle>
            <CardDescription>Control how and when you receive notifications.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between p-3 border rounded-md">
                <div>
                  <Label htmlFor="email-notifications" className="font-medium">Email Notifications</Label>
                  <p className="text-sm text-gray-500">Receive notifications via email</p>
                </div>
                <Switch
                  id="email-notifications"
                  checked={notificationSettings.emailNotifications}
                  onCheckedChange={(checked) =>
                    setNotificationSettings(prev => ({ ...prev, emailNotifications: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 border rounded-md">
                <div>
                  <Label htmlFor="push-notifications" className="font-medium">Push Notifications</Label>
                  <p className="text-sm text-gray-500">Browser push notifications</p>
                </div>
                <Switch
                  id="push-notifications"
                  checked={notificationSettings.pushNotifications}
                  onCheckedChange={(checked) =>
                    setNotificationSettings(prev => ({ ...prev, pushNotifications: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 border rounded-md">
                <div>
                  <Label htmlFor="project-updates" className="font-medium">Project Updates</Label>
                  <p className="text-sm text-gray-500">Updates on project progress</p>
                </div>
                <Switch
                  id="project-updates"
                  checked={notificationSettings.projectUpdates}
                  onCheckedChange={(checked) =>
                    setNotificationSettings(prev => ({ ...prev, projectUpdates: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 border rounded-md">
                <div>
                  <Label htmlFor="daily-digest" className="font-medium">Daily Digest</Label>
                  <p className="text-sm text-gray-500">Daily summary of activities</p>
                </div>
                <Switch
                  id="daily-digest"
                  checked={notificationSettings.dailyDigest}
                  onCheckedChange={(checked) =>
                    setNotificationSettings(prev => ({ ...prev, dailyDigest: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 border rounded-md">
                <div>
                  <Label htmlFor="weekly-report" className="font-medium">Weekly Reports</Label>
                  <p className="text-sm text-gray-500">Weekly project reports</p>
                </div>
                <Switch
                  id="weekly-report"
                  checked={notificationSettings.weeklyReport}
                  onCheckedChange={(checked) =>
                    setNotificationSettings(prev => ({ ...prev, weeklyReport: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 border rounded-md">
                <div>
                  <Label htmlFor="system-alerts" className="font-medium">System Alerts</Label>
                  <p className="text-sm text-gray-500">Important system notifications</p>
                </div>
                <Switch
                  id="system-alerts"
                  checked={notificationSettings.systemAlerts}
                  onCheckedChange={(checked) =>
                    setNotificationSettings(prev => ({ ...prev, systemAlerts: checked }))
                  }
                />
              </div>
            </div>

            <Button
              onClick={saveNotificationSettings}
              disabled={loading.notifications}
              className="mt-4 gradient-primary text-white"
            >
              {loading.notifications ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Notification Settings
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </motion.div>

      {/* Appearance Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Palette className="mr-2 h-5 w-5 text-purple-600" />
              Appearance Settings
            </CardTitle>
            <CardDescription>Customize the look and feel of the application.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center justify-between p-3 border rounded-md">
                <div>
                  <Label htmlFor="dark-mode" className="font-medium">Dark Mode</Label>
                  <p className="text-sm text-gray-500">Switch to dark theme</p>
                </div>
                <Switch
                  id="dark-mode"
                  checked={appearanceSettings.darkMode}
                  onCheckedChange={(checked) =>
                    setAppearanceSettings(prev => ({ ...prev, darkMode: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between p-3 border rounded-md">
                <div>
                  <Label htmlFor="compact-mode" className="font-medium">Compact Mode</Label>
                  <p className="text-sm text-gray-500">Reduce spacing and padding</p>
                </div>
                <Switch
                  id="compact-mode"
                  checked={appearanceSettings.compactMode}
                  onCheckedChange={(checked) =>
                    setAppearanceSettings(prev => ({ ...prev, compactMode: checked }))
                  }
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="theme-color">Theme Color</Label>
                <Input
                  id="theme-color"
                  type="color"
                  value={appearanceSettings.themeColor}
                  onChange={(e) =>
                    setAppearanceSettings(prev => ({ ...prev, themeColor: e.target.value }))
                  }
                  className="w-full h-10"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="language">Language</Label>
                <Select
                  value={appearanceSettings.language}
                  onValueChange={(value) =>
                    setAppearanceSettings(prev => ({ ...prev, language: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="date-format">Date Format</Label>
                <Select
                  value={appearanceSettings.dateFormat}
                  onValueChange={(value) =>
                    setAppearanceSettings(prev => ({ ...prev, dateFormat: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select date format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                    <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                    <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                    <SelectItem value="DD MMM YYYY">DD MMM YYYY</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="time-format">Time Format</Label>
                <Select
                  value={appearanceSettings.timeFormat}
                  onValueChange={(value) =>
                    setAppearanceSettings(prev => ({ ...prev, timeFormat: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select time format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="12h">12 Hour (AM/PM)</SelectItem>
                    <SelectItem value="24h">24 Hour</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Button
              onClick={saveAppearanceSettings}
              disabled={loading.appearance}
              className="mt-4 gradient-primary text-white"
            >
              {loading.appearance ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Appearance Settings
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </motion.div>
      
      {/* Security Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Lock className="mr-2 h-5 w-5 text-red-600" />
              Security Settings
            </CardTitle>
            <CardDescription>Manage your account security and password settings.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Password Change Section */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Change Password</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="current-password">Current Password</Label>
                  <Input
                    id="current-password"
                    type="password"
                    placeholder="Enter current password"
                    value={securitySettings.currentPassword}
                    onChange={(e) =>
                      setSecuritySettings(prev => ({ ...prev, currentPassword: e.target.value }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-password">New Password</Label>
                  <Input
                    id="new-password"
                    type="password"
                    placeholder="Enter new password"
                    value={securitySettings.newPassword}
                    onChange={(e) =>
                      setSecuritySettings(prev => ({ ...prev, newPassword: e.target.value }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirm-password">Confirm New Password</Label>
                  <Input
                    id="confirm-password"
                    type="password"
                    placeholder="Confirm new password"
                    value={securitySettings.confirmPassword}
                    onChange={(e) =>
                      setSecuritySettings(prev => ({ ...prev, confirmPassword: e.target.value }))
                    }
                  />
                </div>
              </div>
            </div>

            {/* Session Settings */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Session Settings</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="session-timeout">Session Timeout</Label>
                  <Select
                    value={securitySettings.sessionTimeout}
                    onValueChange={(value) =>
                      setSecuritySettings(prev => ({ ...prev, sessionTimeout: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select timeout" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1h">1 Hour</SelectItem>
                      <SelectItem value="8h">8 Hours</SelectItem>
                      <SelectItem value="24h">24 Hours</SelectItem>
                      <SelectItem value="7d">7 Days</SelectItem>
                      <SelectItem value="30d">30 Days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-md">
                  <div>
                    <Label htmlFor="login-notifications" className="font-medium">Login Notifications</Label>
                    <p className="text-sm text-gray-500">Get notified of new logins</p>
                  </div>
                  <Switch
                    id="login-notifications"
                    checked={securitySettings.loginNotifications}
                    onCheckedChange={(checked) =>
                      setSecuritySettings(prev => ({ ...prev, loginNotifications: checked }))
                    }
                  />
                </div>
              </div>
            </div>

            <Button
              onClick={saveSecuritySettings}
              disabled={loading.security}
              className="mt-4 gradient-primary text-white"
            >
              {loading.security ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Security Settings
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </motion.div>

      {/* ZeptoMail Settings - Admin Only */}
      {user?.role === 'admin' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="mr-2 h-5 w-5 text-cyan-600" />
                ZeptoMail Integration
              </CardTitle>
              <CardDescription>
                Configure email integration settings for automated notifications.
                You can test email functionality even with just the API key set in environment variables.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="zeptomail-api-key">ZeptoMail API Key</Label>
                  <div className="relative">
                    <Input
                      id="zeptomail-api-key"
                      type={showApiKey ? "text" : "password"}
                      placeholder="Enter your ZeptoMail API Key"
                      value={zeptomailSettings.apiKey}
                      onChange={(e) =>
                        setZeptomailSettings(prev => ({ ...prev, apiKey: e.target.value }))
                      }
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="from-email">From Email Address</Label>
                  <Input
                    id="from-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={zeptomailSettings.fromEmail}
                    onChange={(e) =>
                      setZeptomailSettings(prev => ({ ...prev, fromEmail: e.target.value }))
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="from-name">From Name</Label>
                  <Input
                    id="from-name"
                    placeholder="Karmod Project Hub"
                    value={zeptomailSettings.fromName}
                    onChange={(e) =>
                      setZeptomailSettings(prev => ({ ...prev, fromName: e.target.value }))
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="test-email">Test Email Address</Label>
                  <Input
                    id="test-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={zeptomailSettings.testEmail}
                    onChange={(e) =>
                      setZeptomailSettings(prev => ({ ...prev, testEmail: e.target.value }))
                    }
                  />
                </div>
              </div>

              <div className="flex flex-wrap gap-3">
                <Button
                  onClick={saveZeptomailSettings}
                  disabled={loading.zeptomail}
                  className="gradient-primary text-white"
                >
                  {loading.zeptomail ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Settings
                    </>
                  )}
                </Button>

                <Button
                  onClick={testEmailSettings}
                  disabled={testingEmail || !zeptomailSettings.testEmail}
                  variant="outline"
                >
                  {testingEmail ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Testing...
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      Test Email
                    </>
                  )}
                </Button>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-md p-3">
                <p className="text-xs text-green-800">
                  <strong>🔒 Secure Storage:</strong> API keys are encrypted and stored securely on the server.
                  Only administrators can access and modify these settings.
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

    </div>
  );
};

export default Settings;