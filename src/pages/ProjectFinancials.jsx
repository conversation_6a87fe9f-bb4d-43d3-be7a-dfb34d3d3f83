import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { ArrowLeft, Save, Plus, Trash2, Banknote } from 'lucide-react';

const ProjectFinancials = () => {
  const { id: projectId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { getProjectById, updateProjectFinancials, addNotification } = useProjects();

  const [project, setProject] = useState(null);
  const [financialData, setFinancialData] = useState({
    totalProjectValue: 0,
    initialPayment: 0,
    outstandingBalance: 0,
    installments: [{ amount: 0, dueDate: '', status: 'pending' }]
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProjectData = async () => {
      try {
        setLoading(true);

        // First try to get from context
        let currentProject = getProjectById(projectId);

        // If not found in context (e.g., page reload), fetch from API
        if (!currentProject) {
          const response = await fetch(`/api/projects/${projectId}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });

          if (response.ok) {
            const data = await response.json();
            currentProject = data.project;
          }
        }

        if (currentProject) {
          setProject(currentProject);
          setFinancialData({
            totalProjectValue: currentProject.projectFinancials?.totalProjectValue || currentProject.budget || 0,
            initialPayment: currentProject.projectFinancials?.initialPayment || 0,
            outstandingBalance: currentProject.projectFinancials?.outstandingBalance || (currentProject.projectFinancials?.totalProjectValue || currentProject.budget || 0) - (currentProject.projectFinancials?.initialPayment || 0),
            installments: currentProject.projectFinancials?.installments && currentProject.projectFinancials.installments.length > 0
              ? currentProject.projectFinancials.installments
              : [{ amount: 0, dueDate: '', status: 'pending' }]
          });
        } else {
          toast({ title: "Project not found", variant: "destructive" });
          navigate('/projects');
        }
      } catch (error) {
        console.error('Error loading project:', error);
        toast({ title: "Error loading project", variant: "destructive" });
        navigate('/projects');
      } finally {
        setLoading(false);
      }
    };

    loadProjectData();
  }, [projectId, getProjectById, navigate]);

  const handleInputChange = (field, value) => {
    setFinancialData(prev => {
      const newData = { ...prev, [field]: parseFloat(value) || 0 };
      if (field === 'totalProjectValue' || field === 'initialPayment') {
        newData.outstandingBalance = (newData.totalProjectValue || 0) - (newData.initialPayment || 0);
      }
      return newData;
    });
  };

  const handleInstallmentChange = (index, field, value) => {
    const updatedInstallments = financialData.installments.map((inst, i) =>
      i === index ? { ...inst, [field]: field === 'amount' ? parseFloat(value) || 0 : value } : inst
    );
    setFinancialData(prev => ({ ...prev, installments: updatedInstallments }));
  };

  const addInstallment = () => {
    setFinancialData(prev => ({
      ...prev,
      installments: [...prev.installments, { amount: 0, dueDate: '', status: 'pending' }]
    }));
  };

  const removeInstallment = (index) => {
    setFinancialData(prev => ({
      ...prev,
      installments: prev.installments.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = () => {
    setLoading(true);
    updateProjectFinancials(projectId, financialData);
    addNotification({
        title: 'Project Financials Updated',
        message: `Financial details for project "${project.title}" have been updated.`,
        type: 'financial',
        projectId: projectId,
        recipientRoles: ['admin', 'accountant']
    });
    toast({ title: "Financials Updated", description: "Project financial details have been saved." });
    setLoading(false);
    navigate(`/projects/${projectId}`);
  };

  if (loading || !project) {
    return <div className="p-4 text-center">Loading financial details...</div>;
  }
  
  const canManage = user.role === 'admin' || user.role === 'accountant';
  if(!canManage) {
    toast({ title: "Unauthorized", description: "You don't have permission to view this page.", variant: "destructive" });
    navigate(`/projects/${projectId}`);
    return null;
  }


  return (
    <div className="p-4 max-w-2xl mx-auto space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex items-center space-x-3">
        <Link to={`/projects/${projectId}`}>
          <Button variant="ghost" size="icon"><ArrowLeft className="h-5 w-5" /></Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Project Financials</h1>
          <p className="text-gray-600">Manage financial details for: {project.title}</p>
        </div>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle>Financial Overview</CardTitle>
            <CardDescription>Enter and update the project's financial information.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="totalProjectValue">Total Project Value (₦)</Label>
                <Input id="totalProjectValue" type="number" value={financialData.totalProjectValue} onChange={(e) => handleInputChange('totalProjectValue', e.target.value)} />
              </div>
              <div className="space-y-1">
                <Label htmlFor="initialPayment">Initial Payment Received (₦)</Label>
                <Input id="initialPayment" type="number" value={financialData.initialPayment} onChange={(e) => handleInputChange('initialPayment', e.target.value)} />
              </div>
            </div>
            <div className="p-3 bg-blue-50 rounded-md">
              <Label className="font-semibold">Outstanding Balance: </Label>
              <span className="text-lg font-bold text-blue-700">₦{financialData.outstandingBalance.toLocaleString()}</span>
            </div>

            <div>
              <Label className="text-md font-semibold block mb-2">Payment Installments</Label>
              {financialData.installments.map((installment, index) => (
                <Card key={index} className="mb-3 p-3 bg-white/50">
                  <CardContent className="space-y-3 p-0">
                    <div className="flex justify-between items-center">
                        <p className="font-medium text-sm">Installment {index + 1}</p>
                        {financialData.installments.length > 1 && (
                            <Button type="button" variant="ghost" size="icon" onClick={() => removeInstallment(index)} className="text-red-500 hover:bg-red-100 h-7 w-7">
                                <Trash2 className="h-4 w-4" />
                            </Button>
                        )}
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                        <div className="space-y-1">
                            <Label htmlFor={`instAmount-${index}`} className="text-xs">Amount (₦)</Label>
                            <Input id={`instAmount-${index}`} type="number" value={installment.amount} onChange={(e) => handleInstallmentChange(index, 'amount', e.target.value)} />
                        </div>
                        <div className="space-y-1">
                            <Label htmlFor={`instDueDate-${index}`} className="text-xs">Due Date</Label>
                            <Input id={`instDueDate-${index}`} type="date" value={installment.dueDate} onChange={(e) => handleInstallmentChange(index, 'dueDate', e.target.value)} />
                        </div>
                        <div className="space-y-1">
                            <Label htmlFor={`instStatus-${index}`} className="text-xs">Status</Label>
                            <Select value={installment.status} onValueChange={(val) => handleInstallmentChange(index, 'status', val)}>
                                <SelectTrigger id={`instStatus-${index}`}><SelectValue /></SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="pending">Pending</SelectItem>
                                    <SelectItem value="paid">Paid</SelectItem>
                                    <SelectItem value="overdue">Overdue</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              <Button type="button" variant="outline" onClick={addInstallment} className="w-full mt-2">
                <Plus className="mr-2 h-4 w-4" /> Add Installment
              </Button>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleSubmit} className="w-full gradient-primary text-white" disabled={loading}>
              <Save className="mr-2 h-4 w-4" /> {loading ? 'Saving...' : 'Save Financials'}
            </Button>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default ProjectFinancials;