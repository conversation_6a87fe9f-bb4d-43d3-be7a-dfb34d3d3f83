import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select as ShadSelect, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'; // Renamed to avoid conflict
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Star, Send, ArrowLeft, MessageCircle, Plus as PlusIcon } from 'lucide-react'; // Renamed Plus to PlusIcon

const Testimonials = () => {
  const { user } = useAuth();
  const { projects, testimonials, addTestimonial, getProjectById } = useProjects();
  const navigate = useNavigate();

  const [selectedProjectId, setSelectedProjectId] = useState('');
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [testimonialText, setTestimonialText] = useState('');
  const [showForm, setShowForm] = useState(false);

  const completedProjectsForClient = projects.filter(
    p => p.status === 'completed' && p.client === user?.name && !testimonials.find(t => t.projectId === p.id && t.author === user?.name)
  );

  const handleNewTestimonialSubmit = () => {
    if (!selectedProjectId || rating === 0 || !testimonialText.trim()) {
      toast({ title: "Incomplete Testimonial", description: "Please select a project, provide a rating, and your feedback.", variant: "destructive" });
      return;
    }
    addTestimonial({
      projectId: selectedProjectId,
      rating,
      text: testimonialText,
    }); // Pass as an object
    toast({ title: "Testimonial Submitted!", description: "Thank you for your feedback!" });
    setSelectedProjectId('');
    setRating(0);
    setTestimonialText('');
    setShowForm(false);
  };

  const clientTestimonials = testimonials.filter(t => t.author === user?.name);
  const allDisplayableTestimonials = user?.role === 'admin' ? testimonials : clientTestimonials;

  return (
    <div className="p-4 max-w-3xl mx-auto space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Client Testimonials</h1>
          <p className="text-gray-600">See what our clients say about their projects.</p>
        </div>
        {user?.role === 'client' && completedProjectsForClient.length > 0 && !showForm && (
          <Button onClick={() => setShowForm(true)} className="mt-4 md:mt-0 gradient-primary text-white">
            <PlusIcon className="mr-2 h-4 w-4" /> Add Your Testimonial
          </Button>
        )}
      </motion.div>

      {showForm && user?.role === 'client' && (
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle>Submit Your Testimonial</CardTitle>
              <CardDescription>Share your experience with a completed project.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="projectSelect">Select Project *</Label>
                <ShadSelect value={selectedProjectId} onValueChange={setSelectedProjectId}>
                    <SelectTrigger id="projectSelect"><SelectValue placeholder="Choose a completed project" /></SelectTrigger>
                    <SelectContent>
                        {completedProjectsForClient.map(p => (
                            <SelectItem key={p.id} value={p.id}>{p.title}</SelectItem>
                        ))}
                    </SelectContent>
                </ShadSelect>
              </div>
              <div>
                <Label className="mb-1 block">Overall Rating *</Label>
                <div className="flex space-x-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-7 w-7 cursor-pointer transition-colors ${
                        (hoverRating || rating) >= star ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
                      }`}
                      onClick={() => setRating(star)}
                      onMouseEnter={() => setHoverRating(star)}
                      onMouseLeave={() => setHoverRating(0)}
                    />
                  ))}
                </div>
              </div>
              <div>
                <Label htmlFor="testimonialTextClient">Your Testimonial *</Label>
                <Textarea
                  id="testimonialTextClient"
                  placeholder="Tell us about your experience..."
                  value={testimonialText}
                  onChange={(e) => setTestimonialText(e.target.value)}
                  rows={4}
                  required
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowForm(false)}>Cancel</Button>
                <Button onClick={handleNewTestimonialSubmit} className="gradient-primary text-white">
                    <Send className="mr-2 h-4 w-4" /> Submit
                </Button>
            </CardFooter>
          </Card>
        </motion.div>
      )}

      {allDisplayableTestimonials.length === 0 && !showForm ? (
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }} className="text-center py-12">
          <MessageCircle className="mx-auto h-16 w-16 text-gray-300 mb-4" />
          <h2 className="text-xl font-semibold text-gray-700">No Testimonials Yet</h2>
          <p className="text-gray-500">
            {user?.role === 'client' ? "You haven't submitted any testimonials." : "No client testimonials have been submitted yet."}
          </p>
        </motion.div>
      ) : (
        <div className="space-y-4">
          {allDisplayableTestimonials.map((testimonial, index) => {
            const project = getProjectById(testimonial.projectId);
            return (
              <motion.div
                key={testimonial.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 + index * 0.05 }}
              >
                <Card className="glass border-white/20">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 pt-1">
                        <div className="flex">
                            {[...Array(5)].map((_, i) => (
                                <Star key={i} className={`h-5 w-5 ${i < testimonial.rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} />
                            ))}
                        </div>
                      </div>
                      <div className="flex-1">
                        <p className="text-gray-700 italic mb-2">"{testimonial.text}"</p>
                        <div className="text-sm text-gray-600">
                          <span className="font-semibold">{testimonial.author}</span>
                          {project && <span className="text-gray-500"> for project "{project.title}"</span>}
                        </div>
                        <p className="text-xs text-gray-400 mt-0.5">{new Date(testimonial.timestamp).toLocaleDateString()}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default Testimonials;