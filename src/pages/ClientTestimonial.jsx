import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Star, Send, ArrowLeft } from 'lucide-react';

const ClientTestimonial = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { getProjectById, addTestimonial } = useProjects();
  
  const project = getProjectById(projectId);
  
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [testimonialText, setTestimonialText] = useState('');
  const [loading, setLoading] = useState(false);

  if (!project) {
    return <div className="p-4 text-center">Project not found or not accessible.</div>;
  }

  if (user?.role !== 'client' || project.client !== user.name) {
    return <div className="p-4 text-center">You are not authorized to leave a testimonial for this project.</div>;
  }
  
  if (project.status !== 'completed') {
    return <div className="p-4 text-center">Testimonials can only be submitted for completed projects.</div>;
  }

  const handleSubmit = () => {
    if (rating === 0 || !testimonialText.trim()) {
      toast({ title: "Incomplete Testimonial", description: "Please provide a rating and your feedback.", variant: "destructive" });
      return;
    }
    setLoading(true);
    addTestimonial(projectId, {
      clientName: user.name,
      rating,
      text: testimonialText,
      timestamp: new Date().toISOString()
    });
    toast({ title: "Testimonial Submitted!", description: "Thank you for your feedback!" });
    setLoading(false);
    navigate(`/projects/${projectId}`);
  };

  return (
    <div className="p-4 max-w-2xl mx-auto space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex items-center space-x-3">
        <Button variant="ghost" size="icon" onClick={() => navigate(`/projects/${projectId}`)}><ArrowLeft className="h-5 w-5" /></Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Leave a Testimonial</h1>
          <p className="text-gray-600">Share your experience with the project: {project.title}</p>
        </div>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle>Your Feedback</CardTitle>
            <CardDescription>Rate your overall satisfaction and write a few words about our service.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label className="mb-2 block">Overall Rating *</Label>
              <div className="flex space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`h-8 w-8 cursor-pointer transition-colors ${
                      (hoverRating || rating) >= star ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
                    }`}
                    onClick={() => setRating(star)}
                    onMouseEnter={() => setHoverRating(star)}
                    onMouseLeave={() => setHoverRating(0)}
                  />
                ))}
              </div>
            </div>
            <div>
              <Label htmlFor="testimonialText" className="mb-2 block">Your Testimonial *</Label>
              <Textarea
                id="testimonialText"
                placeholder="Tell us about your experience..."
                value={testimonialText}
                onChange={(e) => setTestimonialText(e.target.value)}
                rows={5}
                required
              />
            </div>
            <div className="flex justify-end">
              <Button onClick={handleSubmit} disabled={loading} className="gradient-primary text-white">
                <Send className="mr-2 h-4 w-4" />
                {loading ? 'Submitting...' : 'Submit Testimonial'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default ClientTestimonial;