import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { ArrowLeft, Save, UserPlus, Mail, Phone, Building, Lock } from 'lucide-react';

const AddClient = () => {
  const navigate = useNavigate();
  const { createUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '', // Clients also need a password to log in
    phone: '',
    company: '',
    location: '',
    role: 'client', // Fixed role for clients
    bio: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const result = await createUser(formData);
      if (result.success) {
        toast({ title: "Client Added Successfully!", description: `${formData.name} has been added as a client.` });
        navigate('/admin/clients');
      } else {
        toast({ title: "Error Adding Client", description: result.error || "Please try again.", variant: "destructive" });
      }
    } catch (error) {
      toast({ title: "Error Adding Client", description: "Please try again.", variant: "destructive" });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="p-4 max-w-2xl mx-auto space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex items-center space-x-4">
        <Button variant="ghost" size="icon" onClick={() => navigate('/admin/clients')}><ArrowLeft className="h-5 w-5" /></Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Add New Client</h1>
          <p className="text-gray-600">Enter the details for the new client.</p>
        </div>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle>Client Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name"><UserPlus className="inline mr-2 h-4 w-4" />Contact Person Name *</Label>
                <Input id="name" placeholder="Enter contact name" value={formData.name} onChange={(e) => handleInputChange('name', e.target.value)} required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="company"><Building className="inline mr-2 h-4 w-4" />Company Name</Label>
                <Input id="company" placeholder="Enter company name (optional)" value={formData.company} onChange={(e) => handleInputChange('company', e.target.value)} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="email"><Mail className="inline mr-2 h-4 w-4" />Email Address *</Label>
                  <Input id="email" type="email" placeholder="Enter email" value={formData.email} onChange={(e) => handleInputChange('email', e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone"><Phone className="inline mr-2 h-4 w-4" />Phone Number</Label>
                  <Input id="phone" type="tel" placeholder="Enter phone number" value={formData.phone} onChange={(e) => handleInputChange('phone', e.target.value)} />
                </div>
              </div>
               <div className="space-y-2">
                <Label htmlFor="password"><Lock className="inline mr-2 h-4 w-4" />Password *</Label>
                <Input id="password" type="password" placeholder="Enter initial password for client" value={formData.password} onChange={(e) => handleInputChange('password', e.target.value)} required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">Location</Label>
                <Input id="location" placeholder="Enter client location" value={formData.location} onChange={(e) => handleInputChange('location', e.target.value)} />
              </div>
              <div className="flex justify-end space-x-4 pt-6">
                <Button type="button" variant="outline" onClick={() => navigate('/admin/clients')}>Cancel</Button>
                <Button type="submit" className="gradient-primary text-white" disabled={loading}>
                  <Save className="mr-2 h-4 w-4" />{loading ? 'Adding...' : 'Add Client'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default AddClient;