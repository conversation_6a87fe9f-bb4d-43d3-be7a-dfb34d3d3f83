import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { motion } from 'framer-motion';
import { ArrowLeft, FolderOpen, MapPin, Calendar } from 'lucide-react';

const ClientProjects = () => {
  const { clientName } = useParams();
  const { getProjectsByClient } = useProjects();
  const projects = getProjectsByClient(decodeURIComponent(clientName));

   const getStatusColor = (status) => {
    switch (status) {
      case 'not_started': return 'bg-gray-100 text-gray-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'on_hold': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-4 space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex items-center space-x-3">
         <Link to="/admin/clients">
          <Button variant="ghost" size="icon"><ArrowLeft className="h-5 w-5" /></Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Projects for {decodeURIComponent(clientName)}</h1>
          <p className="text-gray-600">List of projects associated with this client.</p>
        </div>
      </motion.div>

      {projects.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <Link to={`/projects/${project.id}`}>
                <Card className="glass border-white/20 hover:shadow-xl transition-shadow duration-300 h-full">
                  <CardHeader className="pb-3">
                     <div className="flex items-start justify-between">
                        <CardTitle className="text-lg">{project.title}</CardTitle>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                        {project.status.replace('_', ' ').toUpperCase()}
                        </span>
                    </div>
                    <CardDescription>{project.category}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center text-sm text-gray-600"><MapPin className="mr-2 h-4 w-4" /> {project.location}</div>
                    <div className="flex items-center text-sm text-gray-600"><Calendar className="mr-2 h-4 w-4" /> {new Date(project.startDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })} - {new Date(project.endDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}</div>
                     <div className="space-y-1">
                      <div className="flex justify-between text-xs"><span>Progress</span><span>{project.progress}%</span></div>
                      <Progress value={project.progress} className="h-2" />
                    </div>
                    <Button variant="outline" size="sm" className="w-full mt-2">View Project Details</Button>
                  </CardContent>
                </Card>
              </Link>
            </motion.div>
          ))}
        </div>
      ) : (
         <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-center py-12 text-gray-500">
          <FolderOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          This client is not currently associated with any projects.
        </motion.div>
      )}
    </div>
  );
};

export default ClientProjects;