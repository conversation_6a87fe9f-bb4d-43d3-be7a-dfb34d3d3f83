import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { UserCheck, Briefcase, Mail, Phone, Building, Plus, Edit, Trash2, MoreVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ErrorDisplay from '@/components/ui/ErrorDisplay';
import ConfirmDialog from '@/components/ui/ConfirmDialog';

const ClientList = () => {
  const { getAllClients, deleteUser } = useAuth();
  const navigate = useNavigate();
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);

  useEffect(() => {
    const loadClients = async () => {
      try {
        setLoading(true);
        setError(null);
        const clientData = await getAllClients();
        setClients(clientData || []);
      } catch (err) {
        console.error('Error loading clients:', err);
        setError('Failed to load clients');
      } finally {
        setLoading(false);
      }
    };

    loadClients();
  }, [getAllClients]);

  const handleDeleteClick = (client, e) => {
    e.preventDefault();
    e.stopPropagation();
    setUserToDelete(client);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!userToDelete) return;

    const result = await deleteUser(userToDelete.id);
    if (result.success) {
      setClients(prev => prev.filter(client => client.id !== userToDelete.id));
      toast({
        title: "Client Deleted",
        description: `${userToDelete.name} has been removed from the system.`,
      });
    } else {
      toast({
        title: "Delete Failed",
        description: result.error || "Failed to delete client. Please try again.",
        variant: "destructive",
      });
    }

    setDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  if (loading) {
    return (
      <div className="p-4">
        <LoadingSpinner size="lg" text="Loading clients..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <ErrorDisplay
          error={error}
          title="Failed to Load Clients"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Client Management</h1>
          <p className="text-gray-600">View and manage all registered clients.</p>
        </div>
        <Button onClick={() => navigate('/admin/clients/new')} className="gradient-primary text-white">
          <Plus className="mr-2 h-4 w-4" /> Add New Client
        </Button>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {clients.map((client, index) => (
          <motion.div
            key={client.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
          >
            <Card className="glass border-white/20 hover:shadow-xl transition-shadow duration-300">
              <CardHeader className="flex flex-row items-center space-x-4 pb-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${client.name}`} />
                  <AvatarFallback>{client.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <CardTitle className="text-lg">{client.name}</CardTitle>
                  <CardDescription>Client</CardDescription>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 hover:bg-gray-100"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={(e) => handleDeleteClick(client, e)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Client
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent className="space-y-2 text-sm text-gray-700">
                <div className="flex items-center">
                  <Mail className="mr-2 h-4 w-4 text-gray-500" /> {client.email}
                </div>
                {client.phone && (
                  <div className="flex items-center">
                    <Phone className="mr-2 h-4 w-4 text-gray-500" /> {client.phone}
                  </div>
                )}
                {client.company && (
                  <div className="flex items-center">
                    <Building className="mr-2 h-4 w-4 text-gray-500" /> {client.company}
                  </div>
                )}
                <div className="pt-2 flex space-x-2">
                  <Link to={`/admin/clients/${encodeURIComponent(client.name)}/projects`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      <Briefcase className="mr-2 h-4 w-4" /> Projects
                    </Button>
                  </Link>
                  <Link to={`/admin/clients/edit/${client.id}`} className="flex-1">
                    <Button variant="secondary" size="sm" className="w-full">
                      <Edit className="mr-2 h-4 w-4" /> Edit
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
      {clients.length === 0 && (
        <motion.p initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-center text-gray-500 py-8">
          No clients found.
        </motion.p>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Client"
        description={`Are you sure you want to delete "${userToDelete?.name}"? This action cannot be undone and will remove all associated data including projects.`}
        confirmText="Delete Client"
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
};

export default ClientList;