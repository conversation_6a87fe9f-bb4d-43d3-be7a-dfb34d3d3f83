import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useProjects } from '@/contexts/ProjectContext';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { ArrowLeft, Save, Trash2, Search, X } from 'lucide-react';

const EditProject = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { getProjectById, getProjectByIdSync, updateProject } = useProjects();
  const { getAllStaff, getAllClients } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState(null);

  // Dropdown state
  const [allStaffList, setAllStaffList] = useState([]);
  const [projectLeadStaffList, setProjectLeadStaffList] = useState([]);
  const [clientList, setClientList] = useState([]);
  const [loadingData, setLoadingData] = useState(true);

  const [projectLeadSearch, setProjectLeadSearch] = useState('');
  const [clientSearch, setClientSearch] = useState('');
  const [teamMemberSearch, setTeamMemberSearch] = useState('');

  const [showProjectLeadDropdown, setShowProjectLeadDropdown] = useState(false);
  const [showClientDropdown, setShowClientDropdown] = useState(false);
  const [showTeamMemberDropdown, setShowTeamMemberDropdown] = useState(false);

  // Load staff and client data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoadingData(true);
        const [staff, clients] = await Promise.all([
          getAllStaff(),
          getAllClients()
        ]);

        setAllStaffList(staff || []);
        setProjectLeadStaffList((staff || []).filter(s => s.role === 'project_lead' || s.role === 'admin' || s.role === 'project_manager'));
        setClientList(clients || []);
      } catch (error) {
        console.error('Error loading data:', error);
        toast({
          title: "Error loading data",
          description: "Failed to load staff and client data. Please refresh the page.",
          variant: "destructive",
        });
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, [getAllStaff, getAllClients]);

  // Load project data
  useEffect(() => {
    const loadProject = async () => {
      try {
        const project = await getProjectById(id);
        if (project) {
      // Helper function to format dates for HTML date inputs
      const formatDateForInput = (date) => {
        if (!date) return '';
        if (typeof date === 'string') {
          // If it's already a string, try to parse it and format it
          const parsedDate = new Date(date);
          if (!isNaN(parsedDate.getTime())) {
            return parsedDate.toISOString().split('T')[0];
          }
          return date; // Return as-is if it's already in the right format
        }
        if (date instanceof Date) {
          return date.toISOString().split('T')[0];
        }
        return '';
      };

      setFormData({
        ...project,
        // Format dates properly for HTML date inputs
        startDate: formatDateForInput(project.startDate),
        endDate: formatDateForInput(project.endDate),
        // Ensure team members is an array
        teamMembers: project.teamMembers || [],
        // Extract IDs and names for dropdowns
        projectLeadId: project.projectLead?.id || project.projectLeadId,
        projectLeadName: project.projectLead?.name || project.projectLead,
        clientId: project.client?.id || project.clientId,
        clientName: project.client?.name || project.client,
      });

          // Set search fields for dropdowns
          setProjectLeadSearch(project.projectLead?.name || project.projectLead || '');
          setClientSearch(project.client?.name || project.client || '');
        } else {
          toast({ title: "Error", description: "Project not found or you don't have access.", variant: "destructive" });
          navigate('/projects');
        }
      } catch (error) {
        console.error('Error loading project:', error);
        toast({ title: "Error", description: "Failed to load project data.", variant: "destructive" });
        navigate('/projects');
      }
    };

    loadProject();
  }, [id, getProjectById, navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    if (!formData.category) {
      toast({
        title: "Category Required",
        description: "Please select a project category.",
        variant: "destructive",
      });
      setLoading(false);
      return;
    }

    try {
      // Only send allowed fields for project update (exclude id, createdAt, updatedAt, phases, etc.)
      const projectDataToUpdate = {
        title: formData.title,
        description: formData.description,
        location: formData.location,
        category: formData.category,
        status: formData.status,
        startDate: formData.startDate,
        endDate: formData.endDate,
        budget: parseFloat(formData.budget),
        spent: parseFloat(formData.spent),
        progress: parseInt(formData.progress, 10),
        // Use the ID values for API submission
        projectLeadId: formData.projectLeadId,
        clientId: formData.clientId,
        // Convert team members to the expected format
        teamMemberIds: formData.teamMembers?.map(tm => tm.userId || tm.id).filter(Boolean) || []
      };

      updateProject(id, projectDataToUpdate);
      
      toast({
        title: "Project updated successfully!",
        description: `Project ${id} has been updated.`,
      });

      navigate(`/projects/${id}`);
    } catch (error) {
      toast({
        title: "Error updating project",
        description: "Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Dropdown handlers
  const handleProjectLeadSelect = (staff) => {
    setFormData(prev => ({
      ...prev,
      projectLeadId: staff.id,
      projectLeadName: staff.name,
      projectLead: staff.id // For API compatibility
    }));
    setProjectLeadSearch(staff.name);
    setShowProjectLeadDropdown(false);
  };

  const handleClientSelect = (client) => {
    setFormData(prev => ({
      ...prev,
      clientId: client.id,
      clientName: client.name,
      client: client.id // For API compatibility
    }));
    setClientSearch(client.name);
    setShowClientDropdown(false);
  };

  const handleTeamMemberAdd = (staff) => {
    if (!formData.teamMembers.some(tm => (tm.userId || tm.id) === staff.id)) {
      setFormData(prev => ({
        ...prev,
        teamMembers: [...prev.teamMembers, { userId: staff.id, user: { id: staff.id, name: staff.name } }]
      }));
    }
    setTeamMemberSearch('');
    setShowTeamMemberDropdown(false);
  };

  const handleTeamMemberRemove = (staffId) => {
    setFormData(prev => ({
      ...prev,
      teamMembers: prev.teamMembers.filter(tm => (tm.userId || tm.id) !== staffId)
    }));
  };

  // Filter functions
  const filteredProjectLeads = projectLeadStaffList.filter(staff =>
    staff.name.toLowerCase().includes(projectLeadSearch.toLowerCase())
  );

  const filteredClients = clientList.filter(client =>
    (client.name?.toLowerCase().includes(clientSearch.toLowerCase()) ||
     client.company?.toLowerCase().includes(clientSearch.toLowerCase()))
  );

  const filteredTeamMembers = allStaffList.filter(staff =>
    staff.name.toLowerCase().includes(teamMemberSearch.toLowerCase()) &&
    !formData?.teamMembers?.some(tm => (tm.userId || tm.id) === staff.id)
  );

  const addTeamMember = () => {
    setFormData(prev => ({
      ...prev,
      teamMembers: [...prev.teamMembers, '']
    }));
  };

  const updateTeamMember = (index, value) => {
    setFormData(prev => ({
      ...prev,
      teamMembers: prev.teamMembers.map((member, i) => i === index ? value : member)
    }));
  };

  const removeTeamMember = (index) => {
    setFormData(prev => ({
      ...prev,
      teamMembers: prev.teamMembers.filter((_, i) => i !== index)
    }));
  };

  if (!formData) {
    return <div className="p-4 text-center">Loading project data...</div>;
  }

  return (
    <div className="p-4 max-w-4xl mx-auto space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4"
      >
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate(`/projects/${id}`)}
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Project</h1>
          <p className="text-gray-600">Update details for project: {formData.title}</p>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle>Project Details</CardTitle>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Project Title *</Label>
                  <Input id="title" value={formData.title} onChange={(e) => handleInputChange('title', e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)} required>
                    <SelectTrigger><SelectValue placeholder="Select category" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Commercial">Commercial</SelectItem>
                      <SelectItem value="Residential">Residential</SelectItem>
                      <SelectItem value="Industrial">Industrial</SelectItem>
                      <SelectItem value="Educational">Educational</SelectItem>
                      <SelectItem value="Government">Government</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea id="description" value={formData.description} onChange={(e) => handleInputChange('description', e.target.value)} rows={3} required />
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Location *</Label>
                <Input id="location" value={formData.location} onChange={(e) => handleInputChange('location', e.target.value)} required />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date *</Label>
                  <Input id="startDate" type="date" value={formData.startDate} onChange={(e) => handleInputChange('startDate', e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date *</Label>
                  <Input id="endDate" type="date" value={formData.endDate} onChange={(e) => handleInputChange('endDate', e.target.value)} required />
                </div>
                 <div className="space-y-2">
                  <Label htmlFor="status">Status *</Label>
                  <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)} required>
                    <SelectTrigger><SelectValue placeholder="Select status" /></SelectTrigger>
                    <SelectContent>
                        <SelectItem value="not_started">Not Started</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="on_hold">On Hold</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="budget">Budget (₦) *</Label>
                  <Input id="budget" type="number" value={formData.budget} onChange={(e) => handleInputChange('budget', e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="spent">Spent (₦) *</Label>
                  <Input id="spent" type="number" value={formData.spent} onChange={(e) => handleInputChange('spent', e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="progress">Progress (%) *</Label>
                  <Input id="progress" type="number" min="0" max="100" value={formData.progress} onChange={(e) => handleInputChange('progress', e.target.value)} required />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2 relative">
                  <Label htmlFor="projectLead">Project Lead *</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="projectLead"
                      placeholder="Search for project lead"
                      value={projectLeadSearch}
                      onChange={(e) => { setProjectLeadSearch(e.target.value); setShowProjectLeadDropdown(true); }}
                      onFocus={() => setShowProjectLeadDropdown(true)}
                      onBlur={() => setTimeout(() => setShowProjectLeadDropdown(false), 150)}
                      required
                      className="pl-10"
                    />
                  </div>
                  {showProjectLeadDropdown && filteredProjectLeads.length > 0 && (
                    <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }} className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {filteredProjectLeads.map(staff => (
                        <div key={staff.id} className="px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleProjectLeadSelect(staff)}>
                          <div className="font-medium">{staff.name}</div>
                          <div className="text-sm text-gray-500 capitalize">{staff.role.replace('_', ' ')}</div>
                        </div>
                      ))}
                    </motion.div>
                  )}
                </div>

                <div className="space-y-2 relative">
                  <Label htmlFor="client">Client *</Label>
                   <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="client"
                      placeholder="Search for client or company"
                      value={clientSearch}
                      onChange={(e) => { setClientSearch(e.target.value); setShowClientDropdown(true); }}
                      onFocus={() => setShowClientDropdown(true)}
                      onBlur={() => setTimeout(() => setShowClientDropdown(false), 150)}
                      required
                      className="pl-10"
                    />
                  </div>
                  {showClientDropdown && filteredClients.length > 0 && (
                    <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }} className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {filteredClients.map(client => (
                        <div key={client.id} className="px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleClientSelect(client)}>
                          <div className="font-medium">{client.name}</div>
                          <div className="text-sm text-gray-500">{client.company}</div>
                        </div>
                      ))}
                    </motion.div>
                  )}
                </div>
              </div>
              
              <div className="space-y-4">
                <Label>Team Members</Label>

                {/* Current team members */}
                <div className="space-y-2">
                  {formData?.teamMembers?.map((member, index) => (
                    <div key={member.userId || member.id || index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                      <div>
                        <div className="font-medium">{member.user?.name || member.name || 'Unknown Member'}</div>
                        <div className="text-sm text-gray-500 capitalize">{member.user?.role?.replace('_', ' ') || 'Team Member'}</div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => handleTeamMemberRemove(member.userId || member.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  {(!formData?.teamMembers || formData.teamMembers.length === 0) && (
                    <p className="text-gray-500 text-sm">No team members assigned yet.</p>
                  )}
                </div>

                {/* Add new team member */}
                <div className="relative">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search and add team members"
                      value={teamMemberSearch}
                      onChange={(e) => { setTeamMemberSearch(e.target.value); setShowTeamMemberDropdown(true); }}
                      onFocus={() => setShowTeamMemberDropdown(true)}
                      onBlur={() => setTimeout(() => setShowTeamMemberDropdown(false), 150)}
                      className="pl-10"
                    />
                  </div>
                  {showTeamMemberDropdown && filteredTeamMembers.length > 0 && (
                    <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }} className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {filteredTeamMembers.map(staff => (
                        <div key={staff.id} className="px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleTeamMemberAdd(staff)}>
                          <div className="font-medium">{staff.name}</div>
                          <div className="text-sm text-gray-500 capitalize">{staff.role.replace('_', ' ')}</div>
                        </div>
                      ))}
                    </motion.div>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                 <Label>Project Phases (Current)</Label>
                 {formData.phases?.map(phase => (
                    <div key={phase.name} className="text-sm p-2 border rounded bg-gray-50">
                        {phase.customLabel || phase.name}: {phase.status} ({phase.progress}%)
                    </div>
                 ))}
                 <p className="text-xs text-gray-500">Phase editing is done on the Project Detail page.</p>
              </div>


              <div className="flex justify-end space-x-4 pt-6">
                <Button type="button" variant="outline" onClick={() => navigate(`/projects/${id}`)}>Cancel</Button>
                <Button type="submit" className="gradient-primary text-white" disabled={loading}>
                  <Save className="mr-2 h-4 w-4" />
                  {loading ? 'Updating...' : 'Save Changes'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default EditProject;