
import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { useProjects } from '@/contexts/ProjectContext';
import { useAuth } from '@/contexts/AuthContext';
import { useNotifications } from '@/contexts/NotificationContext';
import apiService from '@/services/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, Save, Plus, Trash2, User<PERSON><PERSON>, SlidersHorizontal, CheckSquare, Square, Edit2, CalendarDays, AlertTriangle } from 'lucide-react';
import { generateId } from '@/utils/storage';

const EditPhase = () => {
  const { projectId, phaseName: encodedPhaseName } = useParams();
  const phaseName = decodeURIComponent(encodedPhaseName);
  const navigate = useNavigate();
  const { updateProject, getProjectById } = useProjects();
  const { user, getAllStaff } = useAuth();
  const { addNotification } = useNotifications();

  const [project, setProject] = useState(null);
  const [phase, setPhase] = useState(null);
  const [phaseLabel, setPhaseLabel] = useState('');
  const [tasks, setTasks] = useState([]);
  const [allEngineers, setAllEngineers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);

      try {
        // Load project data directly from API to get latest data including tasks
        const projectResponse = await apiService.getProjectById(projectId);

        if (projectResponse.project) {
          const currentProject = projectResponse.project;
          setProject(currentProject);

          const currentPhase = currentProject.phases?.find(p => p.name === phaseName);
          if (currentPhase) {
            setPhase(currentPhase);
            setPhaseLabel(currentPhase.customLabel || currentPhase.name);

            // Load tasks from the API response (these are the latest from database)
            setTasks(currentPhase.tasks?.map(task => ({
              ...task,
              // Format dates for HTML date inputs (YYYY-MM-DD)
              startDate: task.startDate ? new Date(task.startDate).toISOString().split('T')[0] : '',
              endDate: task.endDate ? new Date(task.endDate).toISOString().split('T')[0] : '',
              // Ensure progress is a number
              progress: task.progress || 0,
              // Ensure notes is a string
              notes: task.notes || ''
            })) || []);

            console.log(`Loaded ${currentPhase.tasks?.length || 0} tasks for phase "${phaseName}"`);
          } else {
            toast({ title: "Phase not found", variant: "destructive" });
            navigate(`/projects/${projectId}`);
            return;
          }
        } else {
          toast({ title: "Project not found", variant: "destructive" });
          navigate('/projects');
          return;
        }

        // Load staff data asynchronously
        const staff = await getAllStaff();
        setAllEngineers((staff || []).filter(s => s.role === 'team_member' || s.role === 'project_lead'));

      } catch (error) {
        console.error('Error loading project data:', error);
        toast({
          title: "Error loading project",
          description: "Failed to load project data. Please try again.",
          variant: "destructive"
        });
        navigate('/projects');
      }

      setIsLoading(false);
    };

    loadData();
  }, [projectId, phaseName, navigate, getAllStaff]);

  const handleTaskChange = (taskId, field, value) => {
    setTasks(prevTasks => prevTasks.map(task => 
      task.id === taskId ? { ...task, [field]: value } : task
    ));
  };

  const handleTaskProgressChange = (taskId, progress) => {
    setTasks(prevTasks => prevTasks.map(task => 
      task.id === taskId ? { ...task, progress: parseInt(progress, 10) } : task
    ));
  };

  const addTask = () => {
    setTasks(prevTasks => [...prevTasks, { id: generateId(), name: '', assignedTo: '', progress: 0, notes: '', startDate: '', endDate: '' }]);
  };

  const removeTask = (taskId) => {
    setTasks(prevTasks => prevTasks.filter(task => task.id !== taskId));
  };

  const handleSaveChanges = async () => {
    if (!project || !phase) {
      toast({ title: "Error", description: "Project or phase data is missing. Cannot save.", variant: "destructive" });
      return;
    }

    try {
      setIsLoading(true);

      // Calculate phase status and progress
      const phaseStatus = calculatePhaseStatus(tasks);
      const phaseProgress = calculatePhaseProgress(tasks);

      // Step 1: Update phase metadata (label, status, progress)
      const phaseUpdateData = {
        status: phaseStatus,
        progress: phaseProgress,
        customLabel: phaseLabel
      };

      await apiService.updateProjectPhase(projectId, phase.id, phaseUpdateData);

      // Step 2: Handle tasks - compare current tasks with original tasks
      const originalTasks = phase.tasks || [];

      // Find tasks to create (new tasks without database ID)
      const tasksToCreate = tasks.filter(task => !task.id || typeof task.id === 'string');

      // Find tasks to update (existing tasks with changes)
      const tasksToUpdate = tasks.filter(task => {
        if (!task.id || typeof task.id === 'string') return false; // Skip new tasks
        const originalTask = originalTasks.find(ot => ot.id === task.id);
        if (!originalTask) return false;

        // Helper function to format dates for comparison
        const formatDate = (date) => {
          if (!date) return '';
          if (typeof date === 'string') return date;
          return date.toISOString().split('T')[0]; // Convert to YYYY-MM-DD format
        };

        // Check if task has changes
        return (
          originalTask.name !== task.name ||
          originalTask.assignedTo !== task.assignedTo ||
          originalTask.progress !== task.progress ||
          originalTask.notes !== task.notes ||
          formatDate(originalTask.startDate) !== formatDate(task.startDate) ||
          formatDate(originalTask.endDate) !== formatDate(task.endDate)
        );
      });

      // Find tasks to delete (original tasks not in current tasks)
      const tasksToDelete = originalTasks.filter(originalTask =>
        !tasks.find(task => task.id === originalTask.id)
      );

      // Execute task operations
      const taskOperations = [];

      // Create new tasks
      for (const task of tasksToCreate) {
        taskOperations.push(
          apiService.addPhaseTask(projectId, phase.id, {
            name: task.name,
            assignedTo: task.assignedTo || null,
            progress: task.progress || 0,
            notes: task.notes || null,
            startDate: task.startDate || null,
            endDate: task.endDate || null
          })
        );
      }

      // Update existing tasks
      for (const task of tasksToUpdate) {
        taskOperations.push(
          apiService.updatePhaseTask(projectId, phase.id, task.id, {
            name: task.name,
            assignedTo: task.assignedTo || null,
            progress: task.progress || 0,
            notes: task.notes || null,
            startDate: task.startDate || null,
            endDate: task.endDate || null
          })
        );
      }

      // Delete removed tasks
      for (const task of tasksToDelete) {
        taskOperations.push(
          apiService.deletePhaseTask(projectId, phase.id, task.id)
        );
      }

      // Execute all task operations
      if (taskOperations.length > 0) {
        await Promise.all(taskOperations);
      }

      // Step 3: Data will be refreshed when the component navigates back
      // The useEffect will reload fresh data from the API when the page loads

      // Add a local notification for immediate feedback
      if (typeof addNotification === 'function') {
        addNotification({
          title: 'Phase Updated',
          message: `Phase "${phaseLabel}" in project "${project.title}" has been updated with ${tasks.length} tasks.`,
          type: 'info',
          link: `/projects/${project.id}`,
          projectId: project.id,
          recipientRoles: ['admin', 'project_lead'],
          recipientUsers: tasks.map(t => t.assignedTo).filter(Boolean)
        });
      }

      toast({ title: "Phase Updated", description: `Phase "${phaseLabel}" and its tasks have been saved successfully.` });
      navigate(`/projects/${projectId}`);

    } catch (error) {
      console.error("Error saving phase changes:", error);
      toast({
        title: "Error Saving Phase",
        description: error.message || "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculatePhaseProgress = (currentTasks) => {
    if (!currentTasks || currentTasks.length === 0) return phase?.progress || 0; 
    const totalProgress = currentTasks.reduce((sum, task) => sum + (task.progress || 0), 0);
    return Math.round(totalProgress / currentTasks.length);
  };
  
  const calculatePhaseStatus = (currentTasks) => {
    if (!currentTasks || currentTasks.length === 0) return phase?.status || 'not_started';
    const allCompleted = currentTasks.every(task => task.progress === 100);
    if (allCompleted) return 'completed';
    const anyInProgress = currentTasks.some(task => task.progress > 0 && task.progress < 100);
    if (anyInProgress || currentTasks.some(task => task.progress === 0)) return 'in_progress';
    return 'not_started';
  };

  const isTaskOverdue = (task) => {
    if (!task.endDate || task.progress === 100) return false;
    const today = new Date();
    today.setHours(0,0,0,0); 
    const endDate = new Date(task.endDate);
    return endDate < today;
  };

  const canEditPhaseDetails = user?.role === 'admin' || (user?.role === 'project_lead' && project?.projectLead === user.name);
  
  if (isLoading || !project || !phase) {
    return <div className="p-4 text-center">Loading phase details...</div>;
  }

  return (
    <div className="p-4 max-w-3xl mx-auto space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex items-center space-x-3">
        <Link to={`/projects/${projectId}`}>
          <Button variant="ghost" size="icon"><ArrowLeft className="h-5 w-5" /></Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Phase: {phase.name}</h1>
          <p className="text-gray-600">Project: {project.title}</p>
        </div>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle>Phase Details</CardTitle>
            {canEditPhaseDetails && (
              <div className="space-y-2 mt-2">
                <Label htmlFor="phaseLabel">Phase Label</Label>
                <Input id="phaseLabel" value={phaseLabel} onChange={(e) => setPhaseLabel(e.target.value)} placeholder="E.g., Foundation Work" />
              </div>
            )}
          </CardHeader>
          <CardContent>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">Tasks</h3>
            {tasks.map((task, index) => (
              <motion.div 
                key={task.id} 
                className={`p-4 border rounded-md mb-3 bg-white/70 shadow-sm ${isTaskOverdue(task) ? 'border-red-500 border-2' : ''}`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                {isTaskOverdue(task) && (
                  <div className="flex items-center text-red-600 mb-2 text-sm font-medium">
                    <AlertTriangle className="h-4 w-4 mr-1" /> Overdue
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <Label htmlFor={`taskName-${task.id}`}>Task Name</Label>
                    <Input 
                      id={`taskName-${task.id}`} 
                      value={task.name} 
                      onChange={(e) => handleTaskChange(task.id, 'name', e.target.value)}
                      disabled={!canEditPhaseDetails}
                      placeholder="Describe the task"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor={`taskAssignee-${task.id}`}>Assigned To</Label>
                    <Select 
                      value={task.assignedTo} 
                      onValueChange={(value) => handleTaskChange(task.id, 'assignedTo', value)}
                      disabled={!canEditPhaseDetails}
                    >
                      <SelectTrigger><SelectValue placeholder="Select engineer" /></SelectTrigger>
                      <SelectContent>
                        {allEngineers.map(eng => <SelectItem key={eng.id} value={eng.name}>{eng.name}</SelectItem>)}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                  <div className="space-y-1">
                    <Label htmlFor={`taskStartDate-${task.id}`} className="flex items-center"><CalendarDays className="mr-1 h-4 w-4 text-gray-500"/>Start Date</Label>
                    <Input 
                      id={`taskStartDate-${task.id}`} 
                      type="date"
                      value={task.startDate} 
                      onChange={(e) => handleTaskChange(task.id, 'startDate', e.target.value)}
                      disabled={!canEditPhaseDetails}
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor={`taskEndDate-${task.id}`} className="flex items-center"><CalendarDays className="mr-1 h-4 w-4 text-gray-500"/>End Date</Label>
                    <Input 
                      id={`taskEndDate-${task.id}`} 
                      type="date"
                      value={task.endDate} 
                      onChange={(e) => handleTaskChange(task.id, 'endDate', e.target.value)}
                      disabled={!canEditPhaseDetails}
                    />
                  </div>
                </div>

                <div className="mt-3 space-y-1">
                  <Label htmlFor={`taskNotes-${task.id}`}>Notes</Label>
                  <Textarea 
                    id={`taskNotes-${task.id}`} 
                    value={task.notes} 
                    onChange={(e) => handleTaskChange(task.id, 'notes', e.target.value)}
                    disabled={!canEditPhaseDetails && user?.name !== task.assignedTo}
                    placeholder="Add any relevant notes for this task"
                    rows={2}
                  />
                </div>
                <div className="mt-3 space-y-2">
                  <Label>Progress: {task.progress}%</Label>
                  <div className="flex items-center space-x-2">
                    {[0, 25, 50, 75, 100].map(pVal => (
                      <Button 
                        key={pVal}
                        type="button"
                        variant={task.progress === pVal ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleTaskProgressChange(task.id, pVal)}
                        disabled={!canEditPhaseDetails && user?.name !== task.assignedTo}
                        className={`flex-1 ${task.progress === pVal ? 'gradient-primary text-white' : ''}`}
                      >
                        {task.progress === pVal ? <CheckSquare className="mr-1 h-4 w-4" /> : <Square className="mr-1 h-4 w-4" />}
                        {pVal}%
                      </Button>
                    ))}
                  </div>
                  <Progress value={task.progress} className="h-2 mt-1" />
                </div>
                {canEditPhaseDetails && (
                  <div className="text-right mt-2">
                    <Button type="button" variant="ghost" size="icon" onClick={() => removeTask(task.id)} className="text-red-500 hover:text-red-700">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </motion.div>
            ))}
            {canEditPhaseDetails && (
              <Button type="button" variant="outline" onClick={addTask} className="mt-4 w-full">
                <Plus className="mr-2 h-4 w-4" /> Add Task
              </Button>
            )}
          </CardContent>
          <CardFooter>
            <Button onClick={handleSaveChanges} className="w-full gradient-primary text-white">
              <Save className="mr-2 h-4 w-4" /> Save Phase Changes
            </Button>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default EditPhase;
