import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Plus, Edit, Package, PackagePlus, PackageSearch, AlertTriangle, Send, Check, X, History, Filter, Trash2 } from 'lucide-react';
import InventoryRequestCard from '@/components/inventory/InventoryRequestCard';
import ConfirmDialog from '@/components/ui/ConfirmDialog';


const Inventory = () => {
  const { user } = useAuth();
  const {
    inventory,
    addInventoryItem,
    updateInventoryItem,
    deleteInventoryItem,
    inventoryRequests,
    addInventoryRequest,
    updateInventoryRequest,
    projects
  } = useProjects();

  const [showItemForm, setShowItemForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [itemFormData, setItemFormData] = useState({ name: '', category: '', quantity: '', unit: '', lowStockThreshold: '' });

  const [showRequestForm, setShowRequestForm] = useState(false);
  const [requestFormData, setRequestFormData] = useState({ inventoryItemId: '', quantityRequested: '', projectId: '', notes: '' });
  const [submittingRequest, setSubmittingRequest] = useState(false);
  const [updatingRequests, setUpdatingRequests] = useState({}); // Track loading state for request updates
  
  const [filterCategory, setFilterCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('inventory_list');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);

  const canAddItem = ['admin', 'project_manager', 'store_keeper'].includes(user?.role);
  const canEditItem = ['admin', 'project_manager', 'store_keeper'].includes(user?.role);
  const canDeleteItem = ['admin', 'project_manager'].includes(user?.role);
  const canRequestInventory = ['project_lead', 'team_member'].includes(user?.role);

  useEffect(() => {
    if (editingItem) {
      setItemFormData({
        name: editingItem.name || '',
        category: editingItem.category || '',
        quantity: editingItem.quantity || '',
        unit: editingItem.unit || '',
        lowStockThreshold: editingItem.lowStockThreshold || ''
      });
    } else {
      setItemFormData({ name: '', category: '', quantity: '', unit: '', lowStockThreshold: '' });
    }
  }, [editingItem]);

  const handleItemFormChange = (field, value) => {
    setItemFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleItemSubmit = (e) => {
    e.preventDefault();
    if (!itemFormData.name || !itemFormData.quantity || !itemFormData.unit) {
      toast({ title: "Missing Fields", description: "Name, quantity, and unit are required.", variant: "destructive" });
      return;
    }
    if (editingItem) {
      updateInventoryItem(editingItem.id, itemFormData);
      toast({ title: "Inventory Item Updated", description: `${itemFormData.name} has been updated.` });
    } else {
      addInventoryItem(itemFormData);
      toast({ title: "Inventory Item Added", description: `${itemFormData.name} has been added to inventory.` });
    }
    setEditingItem(null);
    setShowItemForm(false);
    setItemFormData({ name: '', category: '', quantity: '', unit: '', lowStockThreshold: '' });
  };

  const handleDeleteClick = (item) => {
    setItemToDelete(item);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!itemToDelete) return;

    const result = await deleteInventoryItem(itemToDelete.id);
    if (result.success) {
      toast({
        title: "Inventory Item Deleted",
        description: `${itemToDelete.name} has been deleted successfully.`,
      });
    } else {
      toast({
        title: "Delete Failed",
        description: result.error || "Failed to delete inventory item. Please try again.",
        variant: "destructive",
      });
    }

    setDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  const handleRequestFormChange = (field, value) => {
    setRequestFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleRequestSubmit = async (e) => {
    e.preventDefault();

    // Prevent double submission
    if (submittingRequest) return;

    // Find selected item - now that values are strings, we need to handle the comparison properly
    const selectedItem = inventory.find(item => item.id.toString() === requestFormData.inventoryItemId);

    // Validate all required fields
    if (!requestFormData.inventoryItemId || !requestFormData.quantityRequested || !requestFormData.projectId) {
      toast({ title: "Missing Fields", description: "Item, quantity, and project are required.", variant: "destructive" });
      return;
    }

    if (!selectedItem) {
      toast({ title: "Invalid Item", description: "Selected item not found.", variant: "destructive" });
      return;
    }

    // Validate quantity is a positive number
    const qty = parseInt(requestFormData.quantityRequested);
    if (isNaN(qty) || qty <= 0) {
      toast({ title: "Invalid Quantity", description: "Please enter a valid quantity.", variant: "destructive" });
      return;
    }

    setSubmittingRequest(true);
    try {
      // Prepare the request data with proper data types
      const requestData = {
        inventoryItemId: requestFormData.inventoryItemId, // Keep as string, API will convert
        projectId: requestFormData.projectId,
        quantityRequested: qty, // Use parsed integer
        notes: requestFormData.notes || '',
        itemName: selectedItem.name // Add item name for display
      };

      console.log('Submitting inventory request:', requestData);
      console.log('Current inventory requests before submission:', inventoryRequests.length);

      const result = await addInventoryRequest(requestData);
      console.log('API result:', result);

      if (result.success) {
        toast({ title: "Inventory Request Submitted", description: `Request for ${selectedItem.name} has been submitted successfully.` });
        setShowRequestForm(false);
        setRequestFormData({ inventoryItemId: '', quantityRequested: '', projectId: '', notes: '' });
      } else {
        toast({ title: "Submission Failed", description: result.error || "Failed to submit inventory request.", variant: "destructive" });
      }
    } catch (error) {
      console.error('Error submitting inventory request:', error);
      toast({ title: "Submission Error", description: "An error occurred while submitting the request.", variant: "destructive" });
    } finally {
      setSubmittingRequest(false);
    }
  };
  
  const handleUpdateRequestStatus = async (requestId, newStatus, payload = {}) => {
    const request = inventoryRequests.find(r => r.id === requestId);
    if (!request) return;

    const updateKey = `${requestId}-${newStatus}`;

    // Prevent double clicks
    if (updatingRequests[updateKey]) return;

    setUpdatingRequests(prev => ({ ...prev, [updateKey]: true }));

    try {
      // Combine status and payload into a single request data object
      const requestData = {
        status: newStatus,
        ...payload
      };

      console.log('Updating inventory request:', { requestId, requestData });

      const result = await updateInventoryRequest(requestId, requestData);

      if (result.success) {
        toast({ title: `Request ${newStatus.replace(/_/g, ' ')}`, description: `Inventory request ${requestId} has been updated.` });
      } else {
        toast({ title: "Update Failed", description: result.error || "Failed to update inventory request.", variant: "destructive" });
      }
    } catch (error) {
      console.error('Error updating inventory request:', error);
      toast({ title: "Update Error", description: "An error occurred while updating the request.", variant: "destructive" });
    } finally {
      setUpdatingRequests(prev => ({ ...prev, [updateKey]: false }));
    }
  };

  const inventoryCategories = [...new Set(inventory.map(item => item.category).filter(Boolean))];

  const filteredInventory = inventory.filter(item => {
    const matchesCategory = filterCategory === 'all' || item.category === filterCategory;
    const searchLower = (searchTerm || '').toLowerCase();
    const matchesSearch = (item.name && typeof item.name === 'string' && item.name.toLowerCase().includes(searchLower)) ||
                         (item.category && typeof item.category === 'string' && item.category.toLowerCase().includes(searchLower));
    return matchesCategory && matchesSearch;
  }).sort((a,b) => (a.name || '').localeCompare(b.name || ''));

  const filteredRequests = inventoryRequests.filter(req => {
    const currentProject = projects.find(p=>p.id === req.projectId);
    const searchLower = (searchTerm || '').toLowerCase();
    const matchesSearch = (req.itemName && typeof req.itemName === 'string' && req.itemName.toLowerCase().includes(searchLower)) ||
                         (req.requestedBy && typeof req.requestedBy === 'string' && req.requestedBy.toLowerCase().includes(searchLower)) ||
                         (currentProject && currentProject.title && typeof currentProject.title === 'string' && currentProject.title.toLowerCase().includes(searchLower));

    if (user?.role === 'team_member') {
        return req.requestedBy === user.name && matchesSearch;
    }
    if (user?.role === 'project_lead') {
        const projectLeadProjects = projects.filter(p => p.projectLead === user.name).map(p => p.id);
        return (req.requestedBy === user.name || projectLeadProjects.includes(req.projectId)) && matchesSearch;
    }
    return matchesSearch;
  }).sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp));


  const ItemForm = (
    <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="mb-6">
      <Card className="glass border-white/20">
        <CardHeader><CardTitle>{editingItem ? 'Edit' : 'Add New'} Inventory Item</CardTitle></CardHeader>
        <CardContent>
          <form onSubmit={handleItemSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input placeholder="Item Name" value={itemFormData.name} onChange={e => handleItemFormChange('name', e.target.value)} required />
              <Input placeholder="Category (e.g., Building Materials)" value={itemFormData.category} onChange={e => handleItemFormChange('category', e.target.value)} />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input type="number" placeholder="Quantity" value={itemFormData.quantity} onChange={e => handleItemFormChange('quantity', e.target.value)} required min="0"/>
              <Input placeholder="Unit (e.g., bags, pcs)" value={itemFormData.unit} onChange={e => handleItemFormChange('unit', e.target.value)} required />
              <Input type="number" placeholder="Low Stock Threshold" value={itemFormData.lowStockThreshold} onChange={e => handleItemFormChange('lowStockThreshold', e.target.value)} min="0"/>
            </div>
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => { setShowItemForm(false); setEditingItem(null); setItemFormData({ name: '', category: '', quantity: '', unit: '', lowStockThreshold: '' }); }}>Cancel</Button>
              <Button type="submit" className="gradient-primary text-white">{editingItem ? 'Save Changes' : 'Add Item'}</Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );



  const RequestForm = (
    <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="mb-6">
      <Card className="glass border-white/20">
        <CardHeader><CardTitle>Request Inventory Item</CardTitle></CardHeader>
        <CardContent>
          <form onSubmit={handleRequestSubmit} className="space-y-4">
            <Select value={requestFormData.inventoryItemId ? requestFormData.inventoryItemId.toString() : ''} onValueChange={val => handleRequestFormChange('inventoryItemId', val)} required>
              <SelectTrigger><SelectValue placeholder="Select Item" /></SelectTrigger>
              <SelectContent>{inventory.map(item => <SelectItem key={item.id} value={item.id.toString()}>{item.name} (Available: {item.quantity} {item.unit})</SelectItem>)}</SelectContent>
            </Select>
            <Input type="number" placeholder="Quantity Requested" value={requestFormData.quantityRequested} onChange={e => handleRequestFormChange('quantityRequested', e.target.value)} required min="1" />
            <Select value={requestFormData.projectId ? requestFormData.projectId.toString() : ''} onValueChange={val => handleRequestFormChange('projectId', val)} required>
                <SelectTrigger><SelectValue placeholder="Select Project" /></SelectTrigger>
                <SelectContent>{projects.filter(p => user?.role === 'admin' || user?.role === 'project_manager' || user?.role === 'project_lead' || (p.teamMembers && p.teamMembers.includes(user?.name))).map(p => <SelectItem key={p.id} value={p.id.toString()}>{p.title}</SelectItem>)}</SelectContent>
            </Select>
            <Textarea placeholder="Notes / Reason for request" value={requestFormData.notes} onChange={e => handleRequestFormChange('notes', e.target.value)} />
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => {setShowRequestForm(false); setRequestFormData({ inventoryItemId: '', quantityRequested: '', projectId: '', notes: '' });}}>Cancel</Button>
              <Button type="submit" className="gradient-primary text-white" disabled={submittingRequest}>
                {submittingRequest ? 'Submitting...' : 'Submit Request'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
  
  return (
    <div className="p-4 space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div><h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Inventory Management</h1><p className="text-gray-600 dark:text-gray-400">Track and manage material stock levels.</p></div>
        <div className="flex space-x-2 mt-4 md:mt-0">
          {canAddItem && !showItemForm && activeTab === 'inventory_list' && (<Button onClick={() => { setShowItemForm(true); setEditingItem(null); }} className="gradient-primary text-white"><PackagePlus className="mr-2 h-4 w-4" />Add Item</Button>)}
          {canRequestInventory && !showRequestForm && activeTab === 'inventory_list' && (<Button onClick={() => {setShowRequestForm(true); setRequestFormData({ inventoryItemId: '', quantityRequested: '', projectId: '', notes: '' });}} className="gradient-accent text-white"><Send className="mr-2 h-4 w-4" />Request Item</Button>)}
        </div>
      </motion.div>

      <div className="flex border-b">
        <Button variant={activeTab === 'inventory_list' ? 'default': 'ghost'} onClick={() => setActiveTab('inventory_list')} className="rounded-b-none">Inventory List</Button>
        <Button variant={activeTab === 'requests_log' ? 'default': 'ghost'} onClick={() => setActiveTab('requests_log')} className="rounded-b-none">Requests Log</Button>
      </div>

      {showItemForm && ItemForm}
      {showRequestForm && RequestForm}

      <Card className="glass border-white/20">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <Input placeholder="Search items or requests..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="md:col-span-2"/>
            {activeTab === 'inventory_list' && (
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger><SelectValue placeholder="Filter by Category" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {inventoryCategories.map(cat => <SelectItem key={cat} value={cat}>{cat}</SelectItem>)}
                </SelectContent>
              </Select>
            )}
          </div>
        </CardContent>
      </Card>

      {activeTab === 'inventory_list' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredInventory.map((item, index) => (
            <motion.div key={item.id} initial={{ opacity: 0, y: 15 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: index * 0.05 }}>
              <Card className={`glass border-white/20 ${item.quantity < item.lowStockThreshold ? 'border-red-500 border-2' : ''}`}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle>{item.name}</CardTitle>
                    {item.quantity < item.lowStockThreshold && <AlertTriangle className="h-5 w-5 text-red-500" title="Low Stock!" />}
                  </div>
                  <CardDescription>{item.category}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Quantity: <span className="font-semibold">{item.quantity} {item.unit}</span></p>
                  <p>Low Stock At: {item.lowStockThreshold} {item.unit}</p>
                  <p className="text-xs text-gray-500">Last Updated: {item.updatedAt ? new Date(item.updatedAt).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' }) : 'N/A'}</p>
                </CardContent>
                {(canEditItem || canDeleteItem) && (
                  <CardFooter className="flex justify-end space-x-2">
                    {canEditItem && (
                      <Button variant="outline" size="sm" onClick={() => { setEditingItem(item); setShowItemForm(true); }}>
                        <Edit className="mr-1 h-3 w-3" />Edit
                      </Button>
                    )}
                    {canDeleteItem && (
                      <Button variant="outline" size="sm" onClick={() => handleDeleteClick(item)} className="text-red-600 hover:text-red-700 hover:bg-red-50">
                        <Trash2 className="mr-1 h-3 w-3" />Delete
                      </Button>
                    )}
                  </CardFooter>
                )}
              </Card>
            </motion.div>
          ))}
          {filteredInventory.length === 0 && <p className="text-center text-gray-500 py-8 md:col-span-full">No inventory items match your criteria.</p>}
        </div>
      )}

      {activeTab === 'requests_log' && (
         <div className="space-y-4 flex flex-col items-stretch md:items-center">
            {filteredRequests.map((req, index) => {
                const project = projects.find(p => p.id === req.projectId);
                return (
                  <InventoryRequestCard
                    key={req.id}
                    request={req}
                    project={project}
                    user={user}
                    onUpdateStatus={handleUpdateRequestStatus}
                    updatingRequests={updatingRequests}
                    index={index}
                  />
                );
            })}
            {filteredRequests.length === 0 && <p className="text-center text-gray-500 py-8">No inventory requests match your criteria.</p>}
         </div>
      )}

      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Inventory Item"
        description={`Are you sure you want to delete "${itemToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteConfirm}
        variant="destructive"
      />

    </div>
  );
};

export default Inventory;