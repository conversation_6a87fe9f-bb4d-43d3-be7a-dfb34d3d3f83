import React from 'react';
import { useLocation } from 'react-router-dom';
import Header from '@/components/Header';
import MobileNav from '@/components/MobileNav';

const Layout = ({ children }) => {
  const location = useLocation();

  return (
    <div className="flex flex-col h-screen">
      <Header />
      <main className="main-content flex-1 overflow-y-auto pt-16 pb-20 md:pb-0 no-scrollbar">
        {children}
      </main>
      <MobileNav />
    </div>
  );
};

export default Layout;