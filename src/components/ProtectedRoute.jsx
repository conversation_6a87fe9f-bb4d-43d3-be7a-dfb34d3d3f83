import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ErrorDisplay from '@/components/ui/ErrorDisplay';

const ProtectedRoute = ({ children, requiredRole = null }) => {
  const { user, loading, error } = useAuth();

  if (loading) {
    return (
      <LoadingSpinner
        fullScreen
        size="lg"
        text="Loading your session..."
      />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <ErrorDisplay
            error={error}
            title="Authentication Error"
            onRetry={() => window.location.reload()}
          />
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Check role-based access if required
  if (requiredRole) {
    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    if (!allowedRoles.includes(user.role)) {
      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <div className="max-w-md w-full">
            <ErrorDisplay
              error="You don't have permission to access this page."
              title="Access Denied"
              variant="warning"
            />
          </div>
        </div>
      );
    }
  }

  return children;
};

export default ProtectedRoute;