import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, FolderOpen, MessageSquare, User, Package } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';

const MobileNav = () => {
  const location = useLocation();
  const { user } = useAuth();

  const navItemsBase = [
    { icon: Home, label: 'Dashboard', path: '/' },
    { icon: FolderOpen, label: 'Projects', path: '/projects' },
    { icon: MessageSquare, label: 'Daily Log', path: '/daily-log' },
  ];

  const inventoryNavItem = { icon: Package, label: 'Inventory', path: '/inventory' };

  const navItemsEnd = [
    { icon: User, label: 'Profile', path: '/profile' }
  ];

  let navItems = [...navItemsBase];

  const canViewInventory = user && ['admin', 'project_manager', 'project_lead', 'store_keeper', 'team_member'].includes(user.role);

  if (canViewInventory) {
    navItems.push(inventoryNavItem);
  }

  navItems = [...navItems, ...navItemsEnd];


  return (
    <motion.nav 
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      className="mobile-nav md:hidden"
    >
      <div className="flex items-center justify-around py-2">
        {navItems.map((item, index) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path || (item.path === '/daily-log' && location.pathname === '/updates');
          
          return (
            <Link
              key={item.path}
              to={item.path}
              className={cn(
                "flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-all duration-200",
                isActive 
                  ? "text-white bg-white/20" 
                  : "text-white/70 hover:text-white hover:bg-white/10"
              )}
            >
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <Icon className="h-5 w-5" />
              </motion.div>
              <span className="text-xs font-medium">{item.label}</span>
            </Link>
          );
        })}
      </div>
    </motion.nav>
  );
};

export default MobileNav;