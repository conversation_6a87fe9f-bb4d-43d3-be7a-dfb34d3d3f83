import React, { useState, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from '@/components/ui/progress';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Paperclip, Trash2, Upload, Loader2 } from 'lucide-react';
import { generateId } from '@/utils/storage';

const DailyLogForm = ({ projects, onSubmit, onCancel, initialProjectId = '' }) => {
  const { user } = useAuth();
  const fileInputRef = useRef(null);
  const [attachments, setAttachments] = useState([]);
  const [submitting, setSubmitting] = useState(false);

  const [formData, setFormData] = useState({
    projectId: initialProjectId !== 'all' ? initialProjectId : '',
    title: '',
    description: '',
    type: 'update',
    isClientVisible: false
  });

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    const newAttachments = files.map(file => ({
      id: generateId(),
      name: file.name,
      size: file.size,
      type: file.type,
      file: file, // Store the actual file object
      url: URL.createObjectURL(file) // Create a temporary URL for preview
    }));
    setAttachments(prev => [...prev, ...newAttachments]);
  };

  const removeAttachment = (idToRemove) => {
    const attachmentToRemove = attachments.find(att => att.id === idToRemove);
    if (attachmentToRemove && attachmentToRemove.url) {
      URL.revokeObjectURL(attachmentToRemove.url); // Revoke the object URL to free memory
    }
    setAttachments(prev => prev.filter(att => att.id !== idToRemove));
  };

  const handleSubmitForm = async (e) => {
    e.preventDefault();

    if (!formData.projectId) {
      toast({ title: "Project required", description: "Please select a project.", variant: "destructive" });
      return;
    }

    if (!formData.title.trim()) {
      toast({ title: "Title required", description: "Please enter a title.", variant: "destructive" });
      return;
    }

    if (!formData.description.trim()) {
      toast({ title: "Description required", description: "Please enter a description.", variant: "destructive" });
      return;
    }

    setSubmitting(true);

    try {
      const logData = {
        ...formData,
        author: user.name,
        authorRole: user.role,
        attachments: attachments.map(att => att.file).filter(Boolean), // Pass actual file objects
      };

      await onSubmit(logData);

      // Reset form on success
      setFormData({ projectId: initialProjectId !== 'all' ? initialProjectId : '', title: '', description: '', type: 'update', isClientVisible: false });
      attachments.forEach(att => URL.revokeObjectURL(att.url)); // Clean up all URLs
      setAttachments([]);
      if (fileInputRef.current) fileInputRef.current.value = "";
    } catch (error) {
      console.error('Submit error:', error);
      toast({
        title: "Submit Failed",
        description: error.message || "Failed to submit daily log. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle>Add New Daily Log</CardTitle>
          <CardDescription>Share progress, milestones, or important notes about your project</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmitForm} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="project">Project *</Label>
                <Select value={formData.projectId} onValueChange={(value) => setFormData(prev => ({ ...prev, projectId: value }))} required>
                  <SelectTrigger><SelectValue placeholder="Select project" /></SelectTrigger>
                  <SelectContent>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id}>{project.title}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">Log Type *</Label>
                <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))} required>
                  <SelectTrigger><SelectValue placeholder="Select type" /></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="update">Progress Update</SelectItem>
                    <SelectItem value="milestone">Milestone</SelectItem>
                    <SelectItem value="issue">Issue/Problem</SelectItem>
                    <SelectItem value="note">General Note</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input id="title" placeholder="Enter log title" value={formData.title} onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))} required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea id="description" placeholder="Describe the log in detail" value={formData.description} onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))} rows={4} required />
            </div>
            <div>
              <Label htmlFor="attachments">Attachments</Label>
              <Button type="button" size="sm" variant="outline" onClick={() => fileInputRef.current?.click()} className="ml-2">
                <Paperclip className="mr-2 h-4 w-4" /> Add Files
              </Button>
              <Input type="file" multiple ref={fileInputRef} onChange={handleFileChange} className="hidden" />
              <div className="mt-2 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                {attachments.map(att => (
                  <div key={att.id} className="relative group bg-gray-100 p-1 rounded border">
                    {att.type.startsWith('image/') && att.url ? (
                      <img src={att.url} alt={att.name} className="h-20 w-full object-cover rounded" />
                    ) : (
                      <div className="h-20 w-full flex items-center justify-center bg-gray-200 rounded">
                        <Paperclip className="h-8 w-8 text-gray-500" />
                      </div>
                    )}
                    <p className="text-xs truncate mt-1" title={att.name}>{att.name}</p>
                    <p className="text-xs text-gray-500">{(att.size / 1024).toFixed(1)} KB</p>
                    <Button type="button" variant="ghost" size="icon" className="absolute top-0 right-0 h-6 w-6 bg-red-500 text-white opacity-0 group-hover:opacity-100 rounded-full" onClick={() => removeAttachment(att.id)}><Trash2 className="h-3 w-3" /></Button>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="clientVisible" checked={formData.isClientVisible} onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isClientVisible: checked }))} />
              <Label htmlFor="clientVisible">Make visible to client</Label>
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={submitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="gradient-primary text-white"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding Log...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Add Log
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default DailyLogForm;