import React, { useState, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Send, Paperclip, Trash2, Loader2 } from 'lucide-react';
import { generateId } from '@/utils/storage';

const ReplyForm = ({ logId, parentReplyId, onReplySubmitted, onCancel }) => {
  const [replyText, setReplyText] = useState('');
  const [attachments, setAttachments] = useState([]);
  const [submitting, setSubmitting] = useState(false);
  const fileInputRef = useRef(null);
  const { addReplyToDailyLog } = useProjects();
  const { user } = useAuth();

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    const newAttachments = files.map(file => ({
      id: generateId(),
      name: file.name,
      size: file.size,
      type: file.type,
      file: file, // Store the actual file object
      url: URL.createObjectURL(file) // Create a temporary URL for preview
    }));
    setAttachments(prev => [...prev, ...newAttachments]);
  };

  const removeAttachment = (idToRemove) => {
    const attachmentToRemove = attachments.find(att => att.id === idToRemove);
    if (attachmentToRemove && attachmentToRemove.url) {
      URL.revokeObjectURL(attachmentToRemove.url);
    }
    setAttachments(prev => prev.filter(att => att.id !== idToRemove));
  };

  const handleReplySubmit = async (e) => {
    e.preventDefault();
    if (!replyText.trim()) {
      toast({ title: "Reply cannot be empty", variant: "destructive" });
      return;
    }

    setSubmitting(true);

    try {
      const fileObjects = attachments.map(att => att.file).filter(Boolean);
      await addReplyToDailyLog(logId, replyText, parentReplyId, fileObjects);

      setReplyText('');
      // Clean up URLs
      attachments.forEach(att => {
        if (att.url) URL.revokeObjectURL(att.url);
      });
      setAttachments([]);
      if (fileInputRef.current) fileInputRef.current.value = "";
      if (onReplySubmitted) onReplySubmitted();
    } catch (error) {
      console.error('Reply submit error:', error);
      toast({
        title: "Reply Failed",
        description: error.message || "Failed to submit reply. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <motion.form 
      onSubmit={handleReplySubmit} 
      className="mt-2 ml-4 pl-4 border-l-2 border-gray-200 space-y-2"
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
    >
      <Textarea
        placeholder={`Reply as ${user.name}...`}
        value={replyText}
        onChange={(e) => setReplyText(e.target.value)}
        rows={2}
        required
      />
      <div>
        <Button type="button" size="sm" variant="outline" onClick={() => fileInputRef.current?.click()}>
          <Paperclip className="mr-2 h-4 w-4" /> Attach Files
        </Button>
        <Input type="file" multiple ref={fileInputRef} onChange={handleFileChange} className="hidden" />
        <div className="mt-2 space-y-1">
          {attachments.map(att => (
            <div key={att.id} className="text-xs flex items-center justify-between bg-gray-100 p-1 rounded">
              <span>{att.name} ({(att.size / 1024).toFixed(1)} KB)</span>
              <Button type="button" variant="ghost" size="icon" className="h-5 w-5" onClick={() => removeAttachment(att.id)}><Trash2 className="h-3 w-3 text-red-500" /></Button>
            </div>
          ))}
        </div>
      </div>
      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onCancel}
            disabled={submitting}
          >
            Cancel
          </Button>
        )}
        <Button
          type="submit"
          size="sm"
          className="gradient-primary text-white"
          disabled={submitting}
        >
          {submitting ? (
            <>
              <Loader2 className="mr-1 h-3 w-3 animate-spin" />
              Posting...
            </>
          ) : (
            <>
              <Send className="mr-1 h-3 w-3" />
              Post Reply
            </>
          )}
        </Button>
      </div>
    </motion.form>
  );
};

export default ReplyForm;