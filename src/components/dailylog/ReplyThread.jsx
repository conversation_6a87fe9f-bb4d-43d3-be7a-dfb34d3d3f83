import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { CornerDownLeft, Paperclip, Trash2, Eye, Download, FileText, Film } from 'lucide-react';
import ReplyForm from '@/components/dailylog/ReplyForm';
import apiService from '@/services/api';

const ReplyThread = ({ replies, logId, level = 0, canClientReply }) => {
  const [replyingTo, setReplyingTo] = useState(null);
  const { user } = useAuth();
  const { deleteDailyLogMessage } = useProjects();

  if (!replies || replies.length === 0) return null;

  const handleDeleteReply = async (replyId) => {
    if(window.confirm("Are you sure you want to delete this reply? This action cannot be undone.")) {
      const result = await deleteDailyLogMessage(logId, replyId, true);
      if (result.success) {
        toast({
          title: "Reply Deleted",
          description: "The reply has been removed."
        });
      } else {
        toast({
          title: "Delete Failed",
          description: result.error || "Failed to delete reply. Please try again.",
          variant: "destructive"
        });
      }
    }
  };

  const handleViewAttachment = async (attachment) => {
    try {
      const viewUrl = await apiService.viewAttachment(attachment.id);

      // Check if we're in a mobile webview or if new tab opening might fail
      const isMobileWebview = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                              window.navigator.standalone || // iOS PWA
                              window.matchMedia('(display-mode: standalone)').matches; // PWA

      if (isMobileWebview) {
        // For mobile webview, navigate in current window
        window.location.href = viewUrl;
      } else {
        // For desktop/regular browsers, try to open in new tab
        const newWindow = window.open(viewUrl, '_blank');
        if (!newWindow) {
          // Fallback if popup blocked
          window.location.href = viewUrl;
        }
      }
    } catch (error) {
      console.error('Error viewing attachment:', error);
      toast({
        title: "View Failed",
        description: "Failed to view attachment. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleDownloadAttachment = async (attachment) => {
    try {
      const blob = await apiService.downloadAttachment(attachment.id);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = attachment.originalName || attachment.filename || 'download';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Download Started",
        description: `Downloading ${attachment.originalName || attachment.filename}`
      });
    } catch (error) {
      console.error('Error downloading attachment:', error);
      toast({
        title: "Download Failed",
        description: "Failed to download attachment. Please try again.",
        variant: "destructive"
      });
    }
  };

  const getAttachmentIcon = (type) => {
    if (!type || typeof type !== 'string') {
      return <FileText className="h-6 w-6 text-gray-500" />;
    }
    if (type.startsWith('image/')) return <Paperclip className="h-6 w-6 text-gray-500" />;
    if (type.startsWith('video/')) return <Film className="h-6 w-6 text-blue-500" />;
    return <FileText className="h-6 w-6 text-purple-500" />;
  };

  return (
    <div className={`mt-3 ${level > 0 ? 'ml-6 pl-6 border-l-2 border-gray-200' : ''}`}>
      {replies.map(reply => (
        <motion.div 
          key={reply.id} 
          className="py-2"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: level * 0.1 }}
        >
          <div className="flex items-start space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${reply.author}`} />
              <AvatarFallback>{reply.author.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-sm">{reply.author}</span>
                <span className="text-xs text-gray-500">{new Date(reply.timestamp).toLocaleString()}</span>
              </div>
              <p className="text-sm text-gray-700 mt-1 whitespace-pre-wrap">{reply.text}</p>
              {reply.attachments && reply.attachments.length > 0 && (
                <div className="mt-2">
                  <p className="text-xs font-medium text-gray-700 mb-1">Attachments:</p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                  {reply.attachments.map(att => (
                    <div key={att.id} className="group relative border rounded-lg p-2 bg-gray-50 hover:shadow-md transition-shadow">
                      <div className="h-16 w-full flex items-center justify-center overflow-hidden rounded mb-1">
                        {getAttachmentIcon(att.mimeType)}
                      </div>
                      <p className="text-xs font-medium truncate" title={att.originalName || att.filename}>{att.originalName || att.filename}</p>
                      <p className="text-xs text-gray-500">{(att.size / 1024).toFixed(1)} KB</p>
                       <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                        <Button variant="ghost" size="icon" className="h-5 w-5 bg-white/70 hover:bg-white rounded-full" onClick={() => handleViewAttachment(att)} title="View Attachment">
                          <Eye className="h-3 w-3 text-blue-600" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-5 w-5 bg-white/70 hover:bg-white rounded-full" onClick={() => handleDownloadAttachment(att)} title="Download Attachment">
                          <Download className="h-3 w-3 text-green-600" />
                        </Button>
                      </div>
                    </div>
                  ))}
                  </div>
                </div>
              )}
              <div className="flex items-center space-x-2 mt-1">
                {(user.role !== 'client' || (user.role === 'client' && canClientReply)) && reply.text !== "[Message deleted by user]" && (
                  <Button variant="link" size="sm" className="p-0 h-auto text-xs" onClick={() => setReplyingTo(replyingTo === reply.id ? null : reply.id)}>
                    <CornerDownLeft className="mr-1 h-3 w-3" /> Reply
                  </Button>
                )}
                {user.name === reply.author && reply.text !== "[Message deleted by user]" && (
                  <Button variant="link" size="sm" className="p-0 h-auto text-xs text-red-500" onClick={() => handleDeleteReply(reply.id)}>
                    <Trash2 className="mr-1 h-3 w-3" /> Delete
                  </Button>
                )}
              </div>
            </div>
          </div>
          {replyingTo === reply.id && (
            <ReplyForm 
              logId={logId} 
              parentReplyId={reply.id} 
              onReplySubmitted={() => setReplyingTo(null)}
              onCancel={() => setReplyingTo(null)}
            />
          )}
          {reply.replies && reply.replies.length > 0 && (
            <ReplyThread replies={reply.replies} logId={logId} level={level + 1} canClientReply={canClientReply} attachmentPreviews={attachmentPreviews} />
          )}
        </motion.div>
      ))}
    </div>
  );
};

export default ReplyThread;