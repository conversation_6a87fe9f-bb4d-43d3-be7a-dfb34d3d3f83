import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { Clock, User, CornerDownLeft, Paperclip, Trash2, Eye, Download, FileText, Film } from 'lucide-react';
import ReplyForm from '@/components/dailylog/ReplyForm';
import ReplyThread from '@/components/dailylog/ReplyThread';
import apiService from '@/services/api';

const DailyLogItem = ({ log, project, index, onDeleteLog }) => {
  const { user } = useAuth();
  const [replyingToLogId, setReplyingToLogId] = useState(null);

  // Removed preview generation - just show icons like replies


  const getLogTypeColor = (type) => {
    switch (type) {
      case 'progress': return 'bg-blue-100 text-blue-800';
      case 'milestone': return 'bg-green-100 text-green-800';
      case 'issue': return 'bg-red-100 text-red-800';
      case 'note': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const canClientReplyToThisLog = user.role === 'client' && log.isClientVisible;

  const handleViewAttachment = async (attachment) => {
    try {
      const viewUrl = await apiService.viewAttachment(attachment.id);

      // Check if we're in a mobile webview or if new tab opening might fail
      const isMobileWebview = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                              window.navigator.standalone || // iOS PWA
                              window.matchMedia('(display-mode: standalone)').matches; // PWA

      if (isMobileWebview) {
        // For mobile webview, navigate in current window
        window.location.href = viewUrl;
      } else {
        // For desktop/regular browsers, try to open in new tab
        const newWindow = window.open(viewUrl, '_blank');
        if (!newWindow) {
          // Fallback if popup blocked
          window.location.href = viewUrl;
        }
      }
    } catch (error) {
      console.error('Error viewing attachment:', error);
      alert("Failed to view attachment.");
    }
  };

  const handleDownloadAttachment = async (attachment) => {
    try {
      const blob = await apiService.downloadAttachment(attachment.id);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = attachment.originalName || attachment.filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading attachment:', error);
      alert("Failed to download attachment.");
    }
  };

  const getAttachmentIcon = (mimeType) => {
    if (!mimeType || typeof mimeType !== 'string') {
      return <FileText className="h-8 w-8 text-gray-500" />;
    }
    if (mimeType.startsWith('image/')) return <Paperclip className="h-8 w-8 text-gray-500" />;
    if (mimeType.startsWith('video/')) return <Film className="h-8 w-8 text-blue-500" />;
    return <FileText className="h-8 w-8 text-purple-500" />;
  };


  return (
    <motion.div 
      key={log.id} 
      initial={{ opacity: 0, y: 20 }} 
      animate={{ opacity: 1, y: 0 }} 
      transition={{ delay: 0.2 + index * 0.1 }}
    >
      <Card className="card-hover glass border-white/20">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h3 className="text-lg font-semibold text-gray-900">{log.title}</h3>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLogTypeColor(log.type)}`}>{log.type.toUpperCase()}</span>
                {log.isClientVisible && (<span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">CLIENT VISIBLE</span>)}
              </div>
              <p className="text-sm text-gray-600 mb-2">Project: <span className="font-medium">{project?.title}</span></p>
            </div>
            <div className="text-right text-sm text-gray-500">
              <div className="flex items-center space-x-1 mb-1"><Clock className="h-3 w-3" /><span>{new Date(log.timestamp).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}</span></div>
              <div className="flex items-center space-x-1"><User className="h-3 w-3" /><span>{log.author}</span></div>
            </div>
          </div>
          <p className="text-gray-700 whitespace-pre-wrap">{log.description}</p>
          {log.attachments && log.attachments.length > 0 && (
            <div className="mt-3">
              <p className="text-sm font-medium text-gray-700 mb-1">Attachments:</p>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                {log.attachments.map(att => (
                  <div key={att.id} className="group relative border rounded-lg p-2 bg-gray-50 hover:shadow-md transition-shadow">
                    <div className="h-20 w-full flex items-center justify-center overflow-hidden rounded mb-1">
                      {getAttachmentIcon(att.mimeType)}
                    </div>
                    <p className="text-xs font-medium truncate" title={att.originalName}>{att.originalName}</p>
                    <p className="text-xs text-gray-500">{(att.size / 1024).toFixed(1)} KB</p>
                    <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                      <Button variant="ghost" size="icon" className="h-6 w-6 bg-white/70 hover:bg-white rounded-full" onClick={() => handleViewAttachment(att)} title="View Attachment">
                        <Eye className="h-3.5 w-3.5 text-blue-600" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6 bg-white/70 hover:bg-white rounded-full" onClick={() => handleDownloadAttachment(att)} title="Download Attachment">
                        <Download className="h-3.5 w-3.5 text-green-600" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          <div className="flex items-center space-x-2 mt-3">
            {(user.role !== 'client' || canClientReplyToThisLog) && log.description !== "[Message deleted by user]" && (
              <Button variant="link" size="sm" className="p-0 h-auto text-xs" onClick={() => setReplyingToLogId(replyingToLogId === log.id ? null : log.id)}>
                <CornerDownLeft className="mr-1 h-3 w-3" /> Reply to Log
              </Button>
            )}
            {user.name === log.author && log.description !== "[Message deleted by user]" && (
              <Button variant="link" size="sm" className="p-0 h-auto text-xs text-red-500" onClick={() => onDeleteLog(log.id, log.id, false)}>
                <Trash2 className="mr-1 h-3 w-3" /> Delete Log
              </Button>
            )}
          </div>
          {replyingToLogId === log.id && (
            <ReplyForm 
              logId={log.id} 
              onReplySubmitted={() => setReplyingToLogId(null)}
              onCancel={() => setReplyingToLogId(null)}
            />
          )}
          <ReplyThread replies={log.replies} logId={log.id} canClientReply={canClientReplyToThisLog} />
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default DailyLogItem;