import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Image as ImageIcon, Video, FileText, Download, Eye, Loader2, Play, File, Trash2 } from 'lucide-react';
import apiService from '@/services/api';

// Component for handling image preview with async loading
const ImagePreview = ({ item }) => {
  const [imageSrc, setImageSrc] = useState(null);
  const [imageError, setImageError] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadImage = async () => {
      try {
        setLoading(true);
        setImageError(false);

        // Fetch the image as blob and create object URL
        const token = apiService.getToken();
        const response = await fetch(`${apiService.baseURL}/media/${item.id}/view`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const blob = await response.blob();
          const objectUrl = URL.createObjectURL(blob);
          setImageSrc(objectUrl);
        } else {
          console.error('Failed to fetch image:', response.status);
          setImageError(true);
        }
      } catch (error) {
        console.error('Error loading image:', error);
        setImageError(true);
      } finally {
        setLoading(false);
      }
    };

    loadImage();

    // Cleanup function to revoke object URL
    return () => {
      if (imageSrc && imageSrc.startsWith('blob:')) {
        URL.revokeObjectURL(imageSrc);
      }
    };
  }, [item.id]);

  if (loading) {
    return (
      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
        <Loader2 className="h-8 w-8 text-gray-400 animate-spin" />
      </div>
    );
  }

  if (imageError || !imageSrc) {
    return (
      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
        <ImageIcon className="h-12 w-12 text-gray-400" />
      </div>
    );
  }

  return (
    <>
      <img
        src={imageSrc}
        alt={item.originalName || item.filename}
        className="w-full h-full object-cover"
        onError={() => setImageError(true)}
      />
      {/* Hover overlay for images */}
      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
        <Eye className="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>
    </>
  );
};

const MediaGrid = ({ mediaItems, onMediaSelect }) => {
  const navigate = useNavigate();
  const { projects, deleteMediaItem } = useProjects();
  const [downloadingFiles, setDownloadingFiles] = useState(new Set());
  const [viewingFiles, setViewingFiles] = useState(new Set());
  const [deletingFiles, setDeletingFiles] = useState(new Set());

  const getMediaIcon = (type) => {
    switch (type) {
      case 'image': return ImageIcon;
      case 'video': return Video;
      case 'document': return FileText;
      default: return File;
    }
  };

  const getMediaType = (mimeType) => {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    return 'document';
  };

  const isImageFile = (mimeType) => {
    return mimeType.startsWith('image/');
  };

  const isVideoFile = (mimeType) => {
    return mimeType.startsWith('video/');
  };

  const handleMediaClick = async (mediaItem) => {
    try {
      // Open the media file directly for viewing
      const viewUrl = apiService.getMediaFileViewUrl(mediaItem.id);
      window.open(viewUrl, '_blank');

      toast({
        title: "Opening File",
        description: `Opening ${mediaItem.originalName || mediaItem.filename} in new tab`
      });
    } catch (error) {
      console.error('View error:', error);
      toast({
        title: "View Failed",
        description: "Failed to open file. Please try again.",
        variant: "destructive"
      });
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getMediaTypeColor = (type) => {
    switch (type) {
      case 'image': return 'bg-green-100 text-green-800';
      case 'video': return 'bg-blue-100 text-blue-800';
      case 'document': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDownload = async (mediaFile) => {
    setDownloadingFiles(prev => new Set(prev).add(mediaFile.id));

    try {
      const blob = await apiService.downloadMediaFile(mediaFile.id);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = mediaFile.originalName || mediaFile.filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Download Started",
        description: `Downloading ${mediaFile.originalName || mediaFile.filename}`
      });
    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: "Download Failed",
        description: "Failed to download file. Please try again.",
        variant: "destructive"
      });
    } finally {
      setDownloadingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(mediaFile.id);
        return newSet;
      });
    }
  };

  const handleView = async (mediaFile) => {
    setViewingFiles(prev => new Set(prev).add(mediaFile.id));

    try {
      const viewUrl = await apiService.getMediaFileViewUrl(mediaFile.id);
      window.open(viewUrl, '_blank');

      toast({
        title: "Opening File",
        description: `Opening ${mediaFile.originalName || mediaFile.filename} in new tab`
      });
    } catch (error) {
      console.error('View error:', error);
      toast({
        title: "View Failed",
        description: "Failed to open file. Please try again.",
        variant: "destructive"
      });
    } finally {
      setViewingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(mediaFile.id);
        return newSet;
      });
    }
  };

  const handleDelete = async (mediaFile) => {
    if (!confirm(`Are you sure you want to delete "${mediaFile.originalName || mediaFile.filename}"? This action cannot be undone.`)) {
      return;
    }

    setDeletingFiles(prev => new Set(prev).add(mediaFile.id));

    try {
      await deleteMediaItem(mediaFile.id);

      toast({
        title: "File Deleted",
        description: `${mediaFile.originalName || mediaFile.filename} has been deleted successfully.`
      });
    } catch (error) {
      console.error('Delete error:', error);
      toast({
        title: "Delete Failed",
        description: "Failed to delete file. Please try again.",
        variant: "destructive"
      });
    } finally {
      setDeletingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(mediaFile.id);
        return newSet;
      });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
    >
      {mediaItems.map((item, index) => {
        const mediaType = getMediaType(item.mimeType);
        const MediaIcon = getMediaIcon(mediaType);
        const project = projects.find(p => p.id === item.projectId);

        return (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
          >
            <Card className="glass border-white/20 hover:shadow-lg transition-all duration-300 overflow-hidden cursor-pointer">
              <CardContent className="p-0">
                <div
                  className="aspect-video bg-gray-100 flex items-center justify-center overflow-hidden relative group"
                  onClick={() => handleMediaClick(item)}
                >
                  {isImageFile(item.mimeType) ? (
                    <ImagePreview item={item} />
                  ) : isVideoFile(item.mimeType) ? (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center relative group">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20"></div>
                      <div className="relative z-10 flex flex-col items-center">
                        <div className="bg-white/90 rounded-full p-4 mb-2 group-hover:bg-white transition-colors duration-300">
                          <Play className="h-8 w-8 text-blue-600 fill-current" />
                        </div>
                        <Video className="h-6 w-6 text-gray-600" />
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center relative group">
                      <div className="flex flex-col items-center">
                        <div className="bg-white rounded-full p-4 mb-2 group-hover:bg-gray-50 transition-colors duration-300">
                          <MediaIcon className="h-8 w-8 text-gray-600" />
                        </div>
                        <span className="text-xs text-gray-500 font-medium">
                          {item.mimeType.split('/')[1]?.toUpperCase() || 'FILE'}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                <div className="p-3">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 truncate text-sm" title={item.originalName || item.filename}>{item.originalName || item.filename}</h3>
                      <p className="text-xs text-gray-600 truncate" title={project?.title}>{project?.title || 'N/A'}</p>
                    </div>
                    {item.isClientVisible && (
                      <span className="ml-2 flex-shrink-0 px-1.5 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">Client</span>
                    )}
                  </div>

                  {item.description && (
                    <p className="text-xs text-gray-600 mb-2 h-8 overflow-y-auto">{item.description}</p>
                  )}
                  <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                    <span>By {item.uploadedBy}</span>
                    <span className="flex items-center space-x-2">
                      <span className="px-2 py-1 bg-gray-100 rounded text-xs font-medium">
                        {mediaType.toUpperCase()}
                      </span>
                      <span>{formatFileSize(item.size)}</span>
                    </span>
                  </div>
                  <div className="flex space-x-2">
                    {onMediaSelect ? (
                       <Button size="sm" variant="outline" className="flex-1" onClick={() => onMediaSelect(item)}>Select</Button>
                    ) : (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1"
                          onClick={() => handleView(item)}
                          disabled={viewingFiles.has(item.id)}
                        >
                          {viewingFiles.has(item.id) ? (
                            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                          ) : (
                            <Eye className="mr-1 h-3 w-3" />
                          )}
                          View
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1"
                          onClick={() => handleDownload(item)}
                          disabled={downloadingFiles.has(item.id)}
                        >
                          {downloadingFiles.has(item.id) ? (
                            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                          ) : (
                            <Download className="mr-1 h-3 w-3" />
                          )}
                          Download
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDelete(item)}
                          disabled={deletingFiles.has(item.id)}
                        >
                          {deletingFiles.has(item.id) ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            <Trash2 className="h-3 w-3" />
                          )}
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        );
      })}
    </motion.div>
  );
};

export default MediaGrid;