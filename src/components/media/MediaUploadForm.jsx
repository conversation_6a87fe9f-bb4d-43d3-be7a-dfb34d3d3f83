import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Upload, Paperclip, PlusCircle, Trash2 } from 'lucide-react';
import { generateId } from '@/utils/storage';

const MediaUploadForm = ({ onUploadComplete, initialProjectId = '' }) => {
  const { user } = useAuth();
  const { projects, addMediaItem } = useProjects();
  const [filesToUpload, setFilesToUpload] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef(null);

  const [uploadData, setUploadData] = useState({
    projectId: initialProjectId,
    description: '',
    isClientVisible: false
  });

  // Update projectId when initialProjectId changes
  useEffect(() => {
    if (initialProjectId && initialProjectId !== uploadData.projectId) {
      setUploadData(prev => ({ ...prev, projectId: initialProjectId }));
    }
  }, [initialProjectId]);

  const handleFileSelection = (e) => {
    const selectedFiles = Array.from(e.target.files);
    const newFiles = selectedFiles.map(file => {
      let type = 'document';
      const extension = file.name.split('.').pop().toLowerCase();
      if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) type = 'image';
      else if (['mp4', 'mov', 'avi', 'mkv'].includes(extension)) type = 'video';
      
      return {
        id: generateId(),
        file,
        title: file.name.split('.').slice(0, -1).join('.'), 
        type,
        previewUrl: URL.createObjectURL(file) 
      };
    });
    setFilesToUpload(prev => [...prev, ...newFiles]);
    if(fileInputRef.current) fileInputRef.current.value = ""; 
  };

  const removeFileFromQueue = (fileId) => {
    const fileToRemove = filesToUpload.find(f => f.id === fileId);
    if (fileToRemove && fileToRemove.previewUrl) {
      URL.revokeObjectURL(fileToRemove.previewUrl);
    }
    setFilesToUpload(prev => prev.filter(f => f.id !== fileId));
  };

  const handleUpload = async (e) => {
    e.preventDefault();

    if (filesToUpload.length === 0) {
      toast({ title: "No files selected", description: "Please select at least one file to upload.", variant: "destructive" });
      return;
    }
    if (!uploadData.projectId) {
      toast({ title: "Project not selected", description: "Please select a project for the media.", variant: "destructive" });
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const totalFiles = filesToUpload.length;
      let completedFiles = 0;

      // Upload files one by one with progress tracking
      for (const fileObj of filesToUpload) {
        const result = await addMediaItem(
          fileObj.file,
          uploadData.projectId,
          uploadData.description,
          (fileProgress) => {
            // Calculate overall progress: completed files + current file progress
            const overallProgress = Math.round(
              ((completedFiles + (fileProgress / 100)) / totalFiles) * 100
            );
            setUploadProgress(overallProgress);
          }
        );

        if (!result.success) {
          throw new Error(result.error || 'Upload failed');
        }

        completedFiles++;
        setUploadProgress(Math.round((completedFiles / totalFiles) * 100));
      }

      toast({ title: "Media Uploaded!", description: `${filesToUpload.length} file(s) have been uploaded successfully.` });

      // Clean up
      setUploadData({ projectId: initialProjectId, description: '', isClientVisible: false });
      filesToUpload.forEach(f => URL.revokeObjectURL(f.previewUrl));
      setFilesToUpload([]);
      if (onUploadComplete) onUploadComplete();

    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload Failed",
        description: error.message || "Failed to upload files. Please try again.",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle>Upload Media</CardTitle>
          <CardDescription>Add photos, videos, or documents to your project. You can add multiple files.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleUpload} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="project-upload">Project *</Label>
              {initialProjectId ? (
                <div className="flex items-center justify-between p-3 border rounded-md bg-gray-50">
                  <span className="font-medium">
                    {projects.find(p => p.id === initialProjectId)?.title || 'Selected Project'}
                  </span>
                  <span className="text-sm text-gray-500">Pre-selected</span>
                </div>
              ) : (
                <Select value={uploadData.projectId} onValueChange={(value) => setUploadData(prev => ({ ...prev, projectId: value }))}>
                  <SelectTrigger id="project-upload"><SelectValue placeholder="Select project" /></SelectTrigger>
                  <SelectContent>{projects.map((p) => (<SelectItem key={p.id} value={p.id}>{p.title}</SelectItem>))}</SelectContent>
                </Select>
              )}
            </div>
            
            <div className="space-y-2">
              <Label>Files to Upload</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                {filesToUpload.length > 0 && (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 mb-3">
                    {filesToUpload.map(fileObj => (
                      <div key={fileObj.id} className="relative group border p-2 rounded-md bg-gray-50">
                        {fileObj.type === 'image' && fileObj.previewUrl ? (
                          <img-replace src={fileObj.previewUrl} alt={fileObj.title} className="h-20 w-full object-contain rounded" />
                        ) : (
                          <div className="h-20 w-full flex items-center justify-center text-gray-400">
                            <Paperclip className="h-8 w-8" />
                          </div>
                        )}
                        <p className="text-xs truncate mt-1" title={fileObj.title}>{fileObj.title}</p>
                        <Button type="button" variant="ghost" size="icon" className="absolute top-1 right-1 h-5 w-5 bg-red-500 text-white opacity-0 group-hover:opacity-100 rounded-full p-0.5" onClick={() => removeFileFromQueue(fileObj.id)}><Trash2 className="h-3 w-3" /></Button>
                      </div>
                    ))}
                  </div>
                )}
                <Button type="button" variant="outline" className="w-full" onClick={() => fileInputRef.current?.click()}>
                  <PlusCircle className="mr-2 h-4 w-4" /> Add More Files
                </Button>
                <Input id="file-upload-main" type="file" multiple className="hidden" ref={fileInputRef} onChange={handleFileSelection} />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description-upload">Common Description (Optional)</Label>
              <Textarea id="description-upload" placeholder="This description will apply to all uploaded files in this batch." value={uploadData.description} onChange={(e) => setUploadData(prev => ({ ...prev, description: e.target.value }))} rows={2} />
            </div>
            <div className="flex items-center space-x-2">
              <Input type="checkbox" id="clientVisible-upload" checked={uploadData.isClientVisible} onChange={(e) => setUploadData(prev => ({ ...prev, isClientVisible: e.target.checked }))} className="rounded w-4 h-4" />
              <Label htmlFor="clientVisible-upload" className="font-normal">Make visible to client</Label>
            </div>
            {uploading && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Uploading files...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="w-full" />
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                disabled={uploading}
                onClick={() => {
                  filesToUpload.forEach(f => URL.revokeObjectURL(f.previewUrl));
                  setFilesToUpload([]);
                  if (onUploadComplete) onUploadComplete(true);
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="gradient-primary text-white"
                disabled={filesToUpload.length === 0 || uploading}
              >
                {uploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Uploading... ({uploadProgress}%)
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" /> Upload ({filesToUpload.length}) File(s)
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default MediaUploadForm;