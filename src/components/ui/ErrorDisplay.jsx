import React from 'react';
import { AlertTriangle, RefreshCw, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

const ErrorDisplay = ({
  error,
  onRetry = null,
  onDismiss = null,
  variant = 'default',
  className = '',
  title = 'Error'
}) => {
  if (!error) return null;

  const errorMessage = typeof error === 'string' ? error : error.message || 'An unexpected error occurred';

  const variants = {
    default: 'border-red-200 bg-red-50 text-red-800',
    destructive: 'border-red-500 bg-red-500 text-white',
    warning: 'border-yellow-200 bg-yellow-50 text-yellow-800'
  };

  return (
    <div
      role="alert"
      className={cn(
        'relative w-full rounded-lg border p-4',
        variants[variant],
        className
      )}
    >
      <div className="flex items-start">
        <AlertTriangle className="h-4 w-4 mt-0.5 mr-3 flex-shrink-0" />
        <div className="flex-1">
          <div className="font-medium">{title}</div>
          <div className="text-sm mt-1">{errorMessage}</div>
        </div>
        <div className="flex items-center space-x-2 ml-4">
          {onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="h-8 px-3"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry
            </Button>
          )}
          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="h-8 w-8 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorDisplay;
