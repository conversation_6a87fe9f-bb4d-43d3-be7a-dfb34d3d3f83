import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { motion } from 'framer-motion';
import { Package, PackageSearch, Check, X, History, Send, AlertCircle, Edit3 } from 'lucide-react';

const InventoryRequestCard = ({ request, project, user, onUpdateStatus, updatingRequests = {}, index }) => {
  const [tempQuantity, setTempQuantity] = useState(request.quantityApproved || request.quantityRequested);
  const [tempNotes, setTempNotes] = useState(request.storeKeeperNotes || request.adminNotes || request.projectLeadNotes || '');

  const getRequestStatusChip = (status) => {
    let Icon, color, text;
    switch (status) {
        case 'pending_store_keeper_approval': Icon = History; color = 'bg-yellow-100 text-yellow-800'; text = 'Pending Store Approval'; break;
        case 'pending_admin_approval': Icon = AlertCircle; color = 'bg-orange-100 text-orange-800'; text = 'Pending Admin Approval'; break;
        case 'approved_by_store_pending_release': Icon = PackageSearch; color = 'bg-blue-100 text-blue-800'; text = 'Store Approved - Pending Release'; break;
        case 'approved_by_admin_pending_store_release': Icon = Check; color = 'bg-teal-100 text-teal-800'; text = 'Admin Approved - Pending Release'; break;
        case 'approved': Icon = Check; color = 'bg-green-100 text-green-800'; text = 'Approved & Released'; break;
        case 'rejected_by_store': Icon = X; color = 'bg-red-100 text-red-800'; text = 'Rejected by Store'; break;
        case 'rejected_by_admin': Icon = X; color = 'bg-pink-100 text-pink-800'; text = 'Rejected by Admin'; break;
        case 'partially_approved_by_store': Icon = PackageSearch; color = 'bg-indigo-100 text-indigo-800'; text = 'Partially Approved by Store'; break;
        case 'revision_requested_by_admin': Icon = Edit3; color = 'bg-purple-100 text-purple-800'; text = 'Revision Requested by Admin'; break;
        default: Icon = Package; color = 'bg-gray-100 text-gray-800'; text = status; break;
    }
    return <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${color}`}><Icon className="mr-1.5 h-3 w-3" />{text}</span>;
  };

  const canProjectLeadPushToAdmin = user.role === 'project_lead' && request.status === 'pending_store_keeper_approval' && request.requestedBy !== user.name;
  const canStoreKeeperPushToAdmin = user.role === 'store_keeper' && ['approved_by_store_pending_release', 'partially_approved_by_store'].includes(request.status);
  const canAdminAct = (user.role === 'admin' || user.role === 'project_manager') && request.status === 'pending_admin_approval';
  const canStoreKeeperActOnPending = user.role === 'store_keeper' && request.status === 'pending_store_keeper_approval';
  const canStoreKeeperRelease = user.role === 'store_keeper' && request.status === 'approved_by_admin_pending_store_release';


  return (
    <motion.div 
      key={request.id} 
      initial={{ opacity: 0, y: 15 }} 
      animate={{ opacity: 1, y: 0 }} 
      transition={{ delay: index * 0.05 }}
      className="w-full"
    >
      <Card className="glass border-white/20 w-full">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div className="mb-2 sm:mb-0">
              <CardTitle className="text-lg sm:text-xl">Request: {request.itemName}</CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                For: {project?.title || 'N/A'} | By: {request.requestedBy} <br className="sm:hidden"/> on {new Date(request.timestamp).toLocaleDateString()}
              </CardDescription>
            </div>
            {getRequestStatusChip(request.status)}
          </div>
        </CardHeader>
        <CardContent className="text-sm">
          <p>Quantity Requested: {request.quantityRequested}</p>
          {request.quantityApproved && <p>Quantity Approved: {request.quantityApproved}</p>}
          {request.notes && <p className="text-gray-600 italic">Requester Notes: {request.notes}</p>}
          {request.storeKeeperNotes && <p className="text-blue-600 italic">Store Keeper Notes: {request.storeKeeperNotes}</p>}
          {request.adminNotes && <p className="text-purple-600 italic">Admin Notes: {request.adminNotes}</p>}
          {request.projectLeadNotes && <p className="text-orange-600 italic">Project Lead Notes: {request.projectLeadNotes}</p>}
        </CardContent>
        
        <CardFooter className="flex flex-col items-stretch space-y-2 sm:flex-row sm:items-center sm:justify-end sm:space-y-0 sm:space-x-2">
          {(canStoreKeeperActOnPending || canAdminAct || canStoreKeeperRelease) && (
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 w-full sm:w-auto">
              <Input 
                type="number" 
                placeholder="Qty" 
                value={tempQuantity} 
                onChange={(e) => setTempQuantity(e.target.value)} 
                className="max-w-full sm:max-w-[100px] h-9 text-sm" 
                disabled={(request.status === 'approved_by_admin_pending_store_release' && !canStoreKeeperRelease) || (request.status === 'pending_admin_approval' && !canAdminAct)}
              />
              <Textarea 
                placeholder="Notes" 
                value={tempNotes} 
                onChange={(e) => setTempNotes(e.target.value)} 
                rows={1} 
                className="h-9 text-sm flex-grow"
              />
            </div>
          )}

          {canStoreKeeperActOnPending && (
            <div className="flex flex-wrap gap-2 justify-end w-full sm:w-auto">
              <Button
                size="sm"
                className="bg-green-500 hover:bg-green-600 text-white flex-grow sm:flex-grow-0"
                onClick={() => onUpdateStatus(request.id, 'approved_by_store_pending_release', { storeKeeperNotes: tempNotes, quantityApproved: parseInt(tempQuantity) })}
                disabled={updatingRequests[`${request.id}-approved_by_store_pending_release`]}
              >
                {updatingRequests[`${request.id}-approved_by_store_pending_release`] ? 'Approving...' : 'Approve'}
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="flex-grow sm:flex-grow-0"
                onClick={() => onUpdateStatus(request.id, 'partially_approved_by_store', { storeKeeperNotes: tempNotes, quantityApproved: parseInt(tempQuantity) })}
                disabled={updatingRequests[`${request.id}-partially_approved_by_store`]}
              >
                {updatingRequests[`${request.id}-partially_approved_by_store`] ? 'Processing...' : 'Partial'}
              </Button>
              <Button
                size="sm"
                variant="destructive"
                className="flex-grow sm:flex-grow-0"
                onClick={() => onUpdateStatus(request.id, 'rejected_by_store', { storeKeeperNotes: tempNotes })}
                disabled={updatingRequests[`${request.id}-rejected_by_store`]}
              >
                {updatingRequests[`${request.id}-rejected_by_store`] ? 'Rejecting...' : 'Reject'}
              </Button>
            </div>
          )}

          {canProjectLeadPushToAdmin && (
             <Button size="sm" className="bg-orange-500 hover:bg-orange-600 text-white w-full sm:w-auto" onClick={() => onUpdateStatus(request.id, 'pending_admin_approval', { projectLeadNotes: tempNotes || 'Pushed by Project Lead' })}>
                <Send className="mr-2 h-4 w-4" /> Push to Admin
            </Button>
          )}

          {canStoreKeeperPushToAdmin && (
             <Button size="sm" className="bg-orange-500 hover:bg-orange-600 text-white w-full sm:w-auto" onClick={() => onUpdateStatus(request.id, 'pending_admin_approval', { storeKeeperNotes: tempNotes || 'Pushed by Store Keeper' })}>
                <Send className="mr-2 h-4 w-4" /> Push to Admin
            </Button>
          )}

          {canAdminAct && (
            <div className="flex flex-wrap gap-2 justify-end w-full sm:w-auto">
              <Button
                size="sm"
                className="bg-teal-500 hover:bg-teal-600 text-white flex-grow sm:flex-grow-0"
                onClick={() => onUpdateStatus(request.id, 'approved_by_admin_pending_store_release', { adminNotes: tempNotes, quantityApproved: parseInt(tempQuantity) })}
                disabled={updatingRequests[`${request.id}-approved_by_admin_pending_store_release`]}
              >
                {updatingRequests[`${request.id}-approved_by_admin_pending_store_release`] ? 'Approving...' : 'Admin Approve'}
              </Button>
              <Button
                size="sm"
                className="bg-purple-500 hover:bg-purple-600 text-white flex-grow sm:flex-grow-0"
                onClick={() => onUpdateStatus(request.id, 'revision_requested_by_admin', { adminNotes: tempNotes })}
                disabled={updatingRequests[`${request.id}-revision_requested_by_admin`]}
              >
                {updatingRequests[`${request.id}-revision_requested_by_admin`] ? 'Requesting...' : 'Request Revision'}
              </Button>
              <Button
                size="sm"
                variant="destructive"
                className="flex-grow sm:flex-grow-0"
                onClick={() => onUpdateStatus(request.id, 'rejected_by_admin', { adminNotes: tempNotes })}
                disabled={updatingRequests[`${request.id}-rejected_by_admin`]}
              >
                {updatingRequests[`${request.id}-rejected_by_admin`] ? 'Rejecting...' : 'Admin Reject'}
              </Button>
            </div>
          )}
          
          {canStoreKeeperRelease && (
             <Button
               size="sm"
               className="bg-green-500 hover:bg-green-600 text-white w-full sm:w-auto"
               onClick={() => onUpdateStatus(request.id, 'approved', { storeKeeperNotes: tempNotes || 'Released by Store Keeper', quantityApproved: request.quantityApproved || parseInt(tempQuantity) })}
               disabled={updatingRequests[`${request.id}-approved`]}
             >
                <Check className="mr-2 h-4 w-4" />
                {updatingRequests[`${request.id}-approved`] ? 'Releasing...' : 'Release Item'}
            </Button>
          )}
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default InventoryRequestCard;