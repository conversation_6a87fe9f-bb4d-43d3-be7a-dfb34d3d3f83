import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { Bell, LogOut, Settings, User, Users, Briefcase, Package } from 'lucide-react';
import { motion } from 'framer-motion';

const Header = () => {
  const { user, logout } = useAuth();
  const { unreadCount } = useNotifications();
  const navigate = useNavigate();

  const handleNavigation = (path) => {
    navigate(path);
  };

  return (
    <motion.header 
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className="fixed top-0 left-0 right-0 z-40 bg-white/80 backdrop-blur-lg border-b border-white/20 shadow-lg"
    >
      <div className="flex items-center justify-between px-4 py-3">
        <Link to="/" className="flex items-center space-x-3 cursor-pointer">
          <img  alt="Karmod Nigeria Logo" className="h-10" src="https://storage.googleapis.com/hostinger-horizons-assets-prod/74a73e54-022f-4f4d-b954-1cc48cb5ff4a/bc79a4caab2cc7bc7938932506be0698.png" />
        </Link>

        <div className="flex items-center space-x-3">
          <Button variant="ghost" size="icon" className="relative" onClick={() => handleNavigation('/notifications')}>
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 min-w-[1.25rem] h-5 bg-red-500 rounded-full text-xs text-white flex items-center justify-center px-1">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2 px-2">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${user?.name}`} />
                  <AvatarFallback>{user?.name?.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium">{user?.name}</div>
                  <div className="text-xs text-gray-500 capitalize">{user?.role?.replace('_', ' ')}</div>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {(user?.role === 'admin' || user?.role === 'project_manager') && (
                <>
                  <DropdownMenuLabel>
                    {user?.role === 'admin' ? 'Admin Panel' : 'Management Panel'}
                  </DropdownMenuLabel>
                  <DropdownMenuItem onClick={() => handleNavigation('/admin/staff')}>
                    <Users className="mr-2 h-4 w-4" />
                    Manage Staff
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleNavigation('/admin/clients')}>
                    <Briefcase className="mr-2 h-4 w-4" />
                    Manage Clients
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleNavigation('/inventory')}>
                    <Package className="mr-2 h-4 w-4" />
                    Manage Inventory
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                </>
              )}
              <DropdownMenuItem onClick={() => handleNavigation('/profile')}>
                <User className="mr-2 h-4 w-4" />
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleNavigation('/settings')}>
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout}>
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;