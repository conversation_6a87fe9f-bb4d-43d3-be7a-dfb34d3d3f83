/**
 * Centralized API service for Karmod Project Hub
 * Replaces localStorage with HTTP requests to backend API
 */

class ApiService {
  constructor() {
    // Handle both full URLs and relative paths
    const viteApiUrl = import.meta.env.VITE_API_URL;

    if (viteApiUrl) {
      // If VITE_API_URL is a full URL, append /api if not already present
      this.baseURL = viteApiUrl.endsWith('/api') ? viteApiUrl : `${viteApiUrl}/api`;
    } else {
      // Fallback to relative path
      this.baseURL = '/api';
    }

    this.token = localStorage.getItem('karmod_auth_token');

    // Debug logging
    // console.log('API Service initialized:', {
    //   baseURL: this.baseURL,
    //   VITE_API_URL: viteApiUrl
    // });
  }

  /**
   * Set authentication token
   */
  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('karmod_auth_token', token);
    } else {
      localStorage.removeItem('karmod_auth_token');
    }
  }

  // Helper method to distinguish between authentication errors and external service errors
  async isAuthenticationError(response, url) {
    try {
      const responseData = await response.json();

      // Check if this is a login endpoint - these 401s are login validation errors, not auth errors
      if (url.includes('/auth/login')) {
        console.log('401 error from login endpoint - login validation error, not auth error');
        return false;
      }

      // Check if this is a test email endpoint - these 401s are from external services
      if (url.includes('/notifications/test-email')) {
        console.log('401 error from test email endpoint - external service error, not auth error');
        return false;
      }

      // Check for specific external service error patterns
      if (responseData.error && typeof responseData.error === 'object') {
        // ZeptoMail error pattern
        if (responseData.error.code && responseData.error.code.startsWith('TM_')) {
          console.log('ZeptoMail API error detected - not an auth error');
          return false;
        }
      }

      // Check for our backend's specific error messages that indicate external service issues
      if (responseData.error && typeof responseData.error === 'string') {
        const externalServiceErrors = [
          'Invalid or unauthorized API key',
          'ZeptoMail API key',
          'Failed to send test email',
          'email configuration'
        ];

        if (externalServiceErrors.some(pattern => responseData.error.includes(pattern))) {
          console.log('External service error detected - not an auth error');
          return false;
        }
      }

      // If none of the above patterns match, treat as authentication error
      return true;
    } catch (error) {
      // If we can't parse the response, assume it's an auth error to be safe
      console.log('Could not parse 401 response, treating as auth error');
      return true;
    }
  }

  /**
   * Get authentication token
   */
  getToken() {
    return this.token || localStorage.getItem('karmod_auth_token');
  }

  /**
   * Make HTTP request with authentication
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const token = this.getToken();

    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      // Handle authentication errors - but not for external service errors
      if (response.status === 401) {
        // Clone response to avoid consuming the body
        const responseClone = response.clone();
        const isAuthError = await this.isAuthenticationError(responseClone, url);
        if (isAuthError) {
          this.setToken(null);
          window.location.href = '/login';
          throw new Error('Authentication required');
        }
        // If it's not an auth error, let it fall through to normal error handling
      }
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`API Error [${endpoint}]:`, error);
      throw error;
    }
  }

  /**
   * GET request
   */
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url, { method: 'GET' });
  }

  /**
   * POST request
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * PUT request
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  /**
   * DELETE request
   */
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  /**
   * Upload file
   */
  async upload(endpoint, formData) {
    const token = this.getToken();
    const url = `${this.baseURL}${endpoint}`;
    
    const config = {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      body: formData,
    };

    try {
      const response = await fetch(url, config);
      
      if (response.status === 401) {
        // Clone response to avoid consuming the body
        const responseClone = response.clone();
        const isAuthError = await this.isAuthenticationError(responseClone, url);
        if (isAuthError) {
          this.setToken(null);
          window.location.href = '/login';
          throw new Error('Authentication required');
        }
        // If it's not an auth error, let it fall through to normal error handling
      }
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Upload Error [${endpoint}]:`, error);
      throw error;
    }
  }

  /**
   * Upload file with progress tracking
   */
  async uploadWithProgress(endpoint, formData, onProgress = null) {
    const token = this.getToken();
    const url = `${this.baseURL}${endpoint}`;

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percentComplete = Math.round((event.loaded / event.total) * 100);
            onProgress(percentComplete);
          }
        });
      }

      xhr.addEventListener('load', async () => {
        if (xhr.status === 401) {
          try {
            const responseText = xhr.responseText;
            const isAuthError = responseText.includes('token') || responseText.includes('auth');
            if (isAuthError) {
              this.setToken(null);
              window.location.href = '/login';
              reject(new Error('Authentication required'));
              return;
            }
          } catch (e) {
            // Continue with normal error handling
          }
        }

        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid response format'));
          }
        } else {
          try {
            const errorData = JSON.parse(xhr.responseText);
            reject(new Error(errorData.error || `HTTP error! status: ${xhr.status}`));
          } catch (error) {
            reject(new Error(`HTTP error! status: ${xhr.status}`));
          }
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.addEventListener('abort', () => {
        reject(new Error('Upload aborted'));
      });

      xhr.open('POST', url);

      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      }

      xhr.send(formData);
    });
  }

  // ==========================================
  // AUTHENTICATION METHODS
  // ==========================================

  async login(email, password) {
    const response = await this.post('/auth/login', { email, password });
    if (response.token) {
      this.setToken(response.token);
    }
    return response;
  }

  async logout() {
    try {
      await this.post('/auth/logout');
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      this.setToken(null);
    }
  }

  async getCurrentUser() {
    return this.get('/auth/me');
  }

  async changePassword(currentPassword, newPassword) {
    return this.post('/auth/change-password', { currentPassword, newPassword });
  }

  // ==========================================
  // USER METHODS
  // ==========================================

  async getUsers(params = {}) {
    return this.get('/users', params);
  }

  async getStaffUsers(params = {}) {
    return this.get('/users/staff', params);
  }

  async getClientUsers(params = {}) {
    return this.get('/users/clients', params);
  }

  async getUserById(id) {
    return this.get(`/users/${id}`);
  }

  async createUser(userData) {
    return this.post('/users', userData);
  }

  async updateUser(id, userData) {
    return this.put(`/users/${id}`, userData);
  }

  async deleteUser(id) {
    return this.delete(`/users/${id}`);
  }

  // ==========================================
  // PROJECT METHODS
  // ==========================================

  async getProjects(params = {}) {
    return this.get('/projects', params);
  }

  async getProjectById(id) {
    return this.get(`/projects/${id}`);
  }

  async createProject(projectData) {
    return this.post('/projects', projectData);
  }

  async updateProject(id, projectData) {
    return this.put(`/projects/${id}`, projectData);
  }

  async deleteProject(id) {
    return this.delete(`/projects/${id}`);
  }

  async getProjectFinancials(projectId) {
    return this.get(`/projects/${projectId}/financials`);
  }

  async updateProjectFinancials(projectId, financialData) {
    return this.put(`/projects/${projectId}/financials`, financialData);
  }

  async updateProjectPhase(projectId, phaseId, phaseData) {
    return this.put(`/projects/${projectId}/phases/${phaseId}`, phaseData);
  }

  async addPhaseTask(projectId, phaseId, taskData) {
    return this.post(`/projects/${projectId}/phases/${phaseId}/tasks`, taskData);
  }

  async updatePhaseTask(projectId, phaseId, taskId, taskData) {
    return this.put(`/projects/${projectId}/phases/${phaseId}/tasks/${taskId}`, taskData);
  }

  async deletePhaseTask(projectId, phaseId, taskId) {
    return this.delete(`/projects/${projectId}/phases/${phaseId}/tasks/${taskId}`);
  }

  // ==========================================
  // DAILY LOG METHODS
  // ==========================================

  async getDailyLogs(params = {}) {
    return this.get('/daily-logs', params);
  }

  async getProjectDailyLogs(projectId, params = {}) {
    return this.get(`/daily-logs/project/${projectId}`, params);
  }

  async createDailyLog(logData, files = []) {
    if (files.length > 0) {
      const formData = new FormData();

      // Add text fields
      Object.keys(logData).forEach(key => {
        if (logData[key] !== null && logData[key] !== undefined) {
          formData.append(key, logData[key]);
        }
      });

      // Add files
      files.forEach(file => {
        formData.append('attachments', file);
      });

      return this.upload('/daily-logs', formData);
    } else {
      return this.post('/daily-logs', logData);
    }
  }

  async addReplyToDailyLog(logId, replyData, files = []) {
    if (files.length > 0) {
      const formData = new FormData();

      // Add text fields
      Object.keys(replyData).forEach(key => {
        if (replyData[key] !== null && replyData[key] !== undefined) {
          formData.append(key, replyData[key]);
        }
      });

      // Add files
      files.forEach(file => {
        formData.append('attachments', file);
      });

      return this.upload(`/daily-logs/${logId}/replies`, formData);
    } else {
      return this.post(`/daily-logs/${logId}/replies`, replyData);
    }
  }

  async deleteDailyLog(logId) {
    return this.delete(`/daily-logs/${logId}`);
  }

  async deleteDailyLogReply(logId, replyId) {
    return this.delete(`/daily-logs/${logId}/replies/${replyId}`);
  }

  // Attachment methods
  async downloadAttachment(attachmentId) {
    const token = this.getToken();
    const response = await fetch(`${this.baseURL}/attachments/${attachmentId}/download`, {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      throw new Error('Failed to download attachment');
    }

    return response.blob();
  }

  async viewAttachment(attachmentId) {
    const token = this.getToken();
    return `${this.baseURL}/attachments/${attachmentId}/view?token=${token}`;
  }

  async deleteAttachment(attachmentId) {
    return this.delete(`/attachments/${attachmentId}`);
  }

  // ==========================================
  // BUDGET METHODS
  // ==========================================

  async getBudgets(params = {}) {
    return this.get('/budgets', params);
  }

  async getProjectBudgets(projectId, params = {}) {
    return this.get(`/budgets/project/${projectId}`, params);
  }

  async createBudget(budgetData) {
    return this.post('/budgets', budgetData);
  }

  async updateBudget(id, budgetData) {
    return this.put(`/budgets/${id}`, budgetData);
  }

  async deleteBudget(id) {
    return this.delete(`/budgets/${id}`);
  }

  // Budget workflow actions
  async pushBudgetToAdmin(budgetId) {
    return this.post(`/budgets/${budgetId}/push-to-admin`);
  }

  async returnBudgetToLead(budgetId, remarks = '') {
    return this.post(`/budgets/${budgetId}/return-to-lead`, { remarks });
  }

  async approveBudget(budgetId) {
    return this.post(`/budgets/${budgetId}/approve`);
  }

  // Project phase methods
  async addProjectPhase(projectId, phaseData) {
    return this.post(`/projects/${projectId}/phases`, phaseData);
  }

  async updateProjectPhase(projectId, phaseId, phaseData) {
    return this.put(`/projects/${projectId}/phases/${phaseId}`, phaseData);
  }

  async deleteProjectPhase(projectId, phaseId) {
    return this.delete(`/projects/${projectId}/phases/${phaseId}`);
  }

  // ==========================================
  // MATERIAL REQUEST METHODS
  // ==========================================

  async getMaterialRequests(params = {}) {
    return this.get('/material-requests', params);
  }

  async createMaterialRequest(requestData) {
    return this.post('/material-requests', requestData);
  }

  async updateMaterialRequest(id, requestData) {
    return this.put(`/material-requests/${id}`, requestData);
  }

  async deleteMaterialRequest(id) {
    return this.delete(`/material-requests/${id}`);
  }

  // ==========================================
  // INVENTORY METHODS
  // ==========================================

  async getInventoryItems(params = {}) {
    return this.get('/inventory', params);
  }

  async createInventoryItem(itemData) {
    return this.post('/inventory', itemData);
  }

  async updateInventoryItem(id, itemData) {
    return this.put(`/inventory/${id}`, itemData);
  }

  async deleteInventoryItem(id) {
    return this.delete(`/inventory/${id}`);
  }

  async getInventoryRequests(params = {}) {
    return this.get('/inventory/requests', params);
  }

  async createInventoryRequest(requestData) {
    return this.post('/inventory/requests', requestData);
  }

  async updateInventoryRequest(id, requestData) {
    return this.put(`/inventory/requests/${id}`, requestData);
  }

  // ==========================================
  // MEDIA METHODS
  // ==========================================

  async getMediaFiles(params = {}) {
    return this.get('/media', params);
  }

  async getProjectMediaFiles(projectId, params = {}) {
    return this.get(`/media/project/${projectId}`, params);
  }

  async uploadMediaFile(file, projectId = null, description = null, onProgress = null) {
    const formData = new FormData();
    formData.append('file', file);
    if (projectId) formData.append('projectId', projectId);
    if (description) formData.append('description', description);

    return this.uploadWithProgress('/media/upload', formData, onProgress);
  }

  async downloadMediaFile(mediaFileId) {
    const token = this.getToken();
    const response = await fetch(`${this.baseURL}/media/${mediaFileId}/download`, {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      throw new Error('Failed to download file');
    }

    return response.blob();
  }

  getMediaFileViewUrl(mediaFileId) {
    const token = this.getToken();
    return `${this.baseURL}/media/${mediaFileId}/view?token=${token}`;
  }

  async deleteMediaFile(id) {
    return this.delete(`/media/${id}`);
  }

  // ==========================================
  // NOTIFICATION METHODS
  // ==========================================

  async getNotifications(params = {}) {
    return this.get('/notifications', params);
  }

  async markNotificationAsRead(id) {
    return this.put(`/notifications/${id}/read`);
  }

  async markAllNotificationsAsRead() {
    return this.put('/notifications/read-all');
  }

  async deleteNotification(id) {
    return this.delete(`/notifications/${id}`);
  }

  // Notification Settings
  async getNotificationSettings() {
    return this.get('/notifications/settings');
  }

  async updateNotificationSettings(settings) {
    return this.put('/notifications/settings', settings);
  }

  async testEmailSettings(testEmail) {
    return this.post('/notifications/test-email', { testEmail });
  }

  // System Settings (Admin only)
  async getSystemSettings() {
    return this.get('/admin/settings');
  }

  async updateSystemSettings(settings) {
    return this.put('/admin/settings', settings);
  }

  async getZeptomailSettings() {
    return this.get('/admin/settings/email/zeptomail');
  }

  async updateZeptomailSettings(settings) {
    return this.put('/admin/settings/email/zeptomail', settings);
  }

  // User Profile Methods
  async getUserStats(userId) {
    return this.get(`/users/${userId}/stats`);
  }

  async getUserActivity(userId) {
    return this.get(`/users/${userId}/activity`);
  }

  async uploadUserAvatar(userId, formData) {
    return this.post(`/users/${userId}/avatar`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
}

// Create and export singleton instance
const apiService = new ApiService();
export default apiService;
