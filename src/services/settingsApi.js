/**
 * Settings API service for managing user preferences and configurations
 */

import apiService from './api';

export const settingsApi = {
  // Notification Settings
  getNotificationSettings: async () => {
    try {
      const response = await apiService.getNotificationSettings();
      return { success: true, data: response.settings };
    } catch (error) {
      console.error('Get notification settings error:', error);
      return {
        success: false,
        error: error.message || 'Failed to get notification settings'
      };
    }
  },

  updateNotificationSettings: async (settings) => {
    try {
      const response = await apiService.updateNotificationSettings(settings);
      return { success: true, data: response.settings };
    } catch (error) {
      console.error('Update notification settings error:', error);
      return {
        success: false,
        error: error.message || 'Failed to update notification settings'
      };
    }
  },

  // Email Testing
  testEmailSettings: async (testEmail) => {
    try {
      const response = await apiService.testEmailSettings(testEmail);
      return {
        success: response.success || true,
        message: response.message || 'Test email sent successfully'
      };
    } catch (error) {
      console.error('Test email error:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to send test email',
        details: error.response?.data?.details
      };
    }
  },

  // User Settings (for security settings like password change)
  updateUserSettings: async (userId, settings) => {
    try {
      const response = await apiService.put(`/users/${userId}`, settings);
      return { success: true, data: response.user };
    } catch (error) {
      console.error('Update user settings error:', error);
      return {
        success: false,
        error: error.message || 'Failed to update user settings'
      };
    }
  },

  // System Settings (Admin only)
  getSystemSettings: async () => {
    try {
      const response = await apiService.get('/admin/settings');
      return { success: true, data: response.settings };
    } catch (error) {
      console.error('Get system settings error:', error);
      return {
        success: false,
        error: error.message || 'Failed to get system settings'
      };
    }
  },

  updateSystemSettings: async (settings) => {
    try {
      const response = await apiService.put('/admin/settings', settings);
      return { success: true, data: response.settings };
    } catch (error) {
      console.error('Update system settings error:', error);
      return {
        success: false,
        error: error.message || 'Failed to update system settings'
      };
    }
  },

  // ZeptoMail Settings (Admin only)
  getZeptomailSettings: async () => {
    try {
      const response = await apiService.get('/admin/settings/email/zeptomail');
      // Backend now returns { success: true, settings: {...} }
      return {
        success: response.success || true,
        data: response.settings,
        message: response.message
      };
    } catch (error) {
      console.error('Get ZeptoMail settings error:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to get ZeptoMail settings',
        details: error.response?.data?.details
      };
    }
  },

  updateZeptomailSettings: async (settings) => {
    try {
      const response = await apiService.put('/admin/settings/email/zeptomail', settings);
      // Backend returns { success: true, settings: [...] }
      return {
        success: response.success || true,
        data: response.settings,
        message: response.message
      };
    } catch (error) {
      console.error('Update ZeptoMail settings error:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to update ZeptoMail settings',
        details: error.response?.data?.details
      };
    }
  }
};
