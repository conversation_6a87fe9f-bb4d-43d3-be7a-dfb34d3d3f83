import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from '@/contexts/AuthContext';
import { ProjectProvider } from '@/contexts/ProjectContext';
import { NotificationProvider } from '@/contexts/NotificationContext';
import { Toaster } from '@/components/ui/toaster';
import Layout from '@/components/Layout';
import Login from '@/pages/Login';
import Dashboard from '@/pages/Dashboard';
import Projects from '@/pages/Projects';
import ProjectDetail from '@/pages/ProjectDetail';
import CreateProject from '@/pages/CreateProject';
import EditProject from '@/pages/EditProject';
import DailyLogPage from '@/pages/DailyLog'; 
import Media from '@/pages/Media';
import Profile from '@/pages/Profile';
import Settings from '@/pages/Settings';
import Budget from '@/pages/Budget';
import Reports from '@/pages/Reports';
import ProtectedRoute from '@/components/ProtectedRoute';
import StaffList from '@/pages/admin/StaffList';
import ClientList from '@/pages/admin/ClientList';
import StaffProjects from '@/pages/admin/StaffProjects';
import ClientProjects from '@/pages/admin/ClientProjects';
import AddStaff from '@/pages/admin/AddStaff';
import AddClient from '@/pages/admin/AddClient';
import EditStaff from '@/pages/admin/EditStaff';
import EditClient from '@/pages/admin/EditClient';
import Notifications from '@/pages/Notifications';
import EditPhase from '@/pages/EditPhase';
import MaterialRequests from '@/pages/MaterialRequests';
import ProjectFinancials from '@/pages/ProjectFinancials';
import Testimonials from '@/pages/Testimonials';
import Inventory from '@/pages/Inventory';


function App() {
  return (
    <Router>
      <AuthProvider>
        <NotificationProvider>
          <ProjectProvider>
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route path="/" element={
                  <ProtectedRoute>
                    <Layout>
                      <Dashboard />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/projects" element={
                  <ProtectedRoute>
                    <Layout>
                      <Projects />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/projects/new" element={
                  <ProtectedRoute>
                    <Layout>
                      <CreateProject />
                    </Layout>
                  </ProtectedRoute>
                } />
                 <Route path="/projects/:id/edit" element={
                  <ProtectedRoute>
                    <Layout>
                      <EditProject />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/projects/:id" element={
                  <ProtectedRoute>
                    <Layout>
                      <ProjectDetail />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/projects/:projectId/phases/:phaseName/edit" element={
                  <ProtectedRoute>
                    <Layout>
                      <EditPhase />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/projects/:id/budget" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager', 'accountant', 'project_lead', 'quantity_surveyor']}>
                    <Layout>
                      <Budget />
                    </Layout>
                  </ProtectedRoute>
                } />
                 <Route path="/projects/:id/financials" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager', 'accountant']}>
                    <Layout>
                      <ProjectFinancials />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/daily-log" element={
                  <ProtectedRoute>
                    <Layout>
                      <DailyLogPage />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/media" element={
                  <ProtectedRoute>
                    <Layout>
                      <Media />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/material-requests" element={
                  <ProtectedRoute requiredRole={['project_lead', 'team_member', 'store_keeper', 'admin', 'project_manager']}>
                    <Layout>
                      <MaterialRequests />
                    </Layout>
                  </ProtectedRoute>
                } />
                 <Route path="/inventory" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager', 'project_lead', 'store_keeper', 'team_member']}>
                    <Layout>
                      <Inventory />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/reports" element={
                  <ProtectedRoute>
                    <Layout>
                      <Reports />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/profile" element={
                  <ProtectedRoute>
                    <Layout>
                      <Profile />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/settings" element={
                  <ProtectedRoute>
                    <Layout>
                      <Settings />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/notifications" element={
                  <ProtectedRoute>
                    <Layout>
                      <Notifications />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/testimonials" element={
                  <ProtectedRoute>
                    <Layout>
                      <Testimonials />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/admin/staff" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager']}>
                    <Layout>
                      <StaffList />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/admin/staff/new" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager']}>
                    <Layout>
                      <AddStaff />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/admin/staff/edit/:userId" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager']}>
                    <Layout>
                      <EditStaff />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/admin/staff/:staffName/projects" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager']}>
                    <Layout>
                      <StaffProjects />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/admin/clients" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager']}>
                    <Layout>
                      <ClientList />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/admin/clients/new" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager']}>
                    <Layout>
                      <AddClient />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/admin/clients/edit/:userId" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager']}>
                    <Layout>
                      <EditClient />
                    </Layout>
                  </ProtectedRoute>
                } />
                <Route path="/admin/clients/:clientName/projects" element={
                  <ProtectedRoute requiredRole={['admin', 'project_manager']}>
                    <Layout>
                      <ClientProjects />
                    </Layout>
                  </ProtectedRoute>
                } />
              </Routes>
              <Toaster />
            </div>
          </ProjectProvider>
        </NotificationProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;