import { storage, generateId } from '@/utils/storage';

export const handleCreateProject = (projectData, contextState, contextSetters, addNotification) => {
  const { projects } = contextState;
  const { setProjects } = contextSetters;
  const newProject = {
    ...projectData,
    id: `KM-${new Date().getFullYear()}-${String(projects.length + 1).padStart(3, '0')}`,
    status: 'not_started', progress: 0, spent: 0,
    phases: [
      { name: 'Phase 1', status: 'not_started', progress: 0, tasks: [], customLabel: 'Phase 1' },
      { name: 'Phase 2', status: 'not_started', progress: 0, tasks: [], customLabel: 'Phase 2' },
      { name: 'Phase 3', status: 'not_started', progress: 0, tasks: [], customLabel: 'Phase 3' },
    ],
    financials: { totalProjectValue: 0, initialPayment: 0, outstandingBalance: 0, installments: [] },
    zeptomailApiKey: null
  };
  const updatedProjects = [...projects, newProject];
  setProjects(updatedProjects);
  storage.set('karmod_projects', updatedProjects);
  addNotification({ title: 'New Project Created', message: `Project "${newProject.title}" has been added.`, type: 'project', link: `/projects/${newProject.id}`, projectId: newProject.id, recipientRoles: ['admin', 'project_lead'] });
  return newProject;
};

export const handleUpdateProject = (projectId, projectUpdates, contextState, contextSetters, addNotification) => {
  const { projects } = contextState;
  const { setProjects } = contextSetters;
  const updatedProjects = projects.map(p => p.id === projectId ? { ...p, ...projectUpdates } : p);
  setProjects(updatedProjects);
  storage.set('karmod_projects', updatedProjects);
  addNotification({ title: 'Project Updated', message: `Project "${projectUpdates.title || projectId}" has been updated.`, type: 'project', link: `/projects/${projectId}`, projectId: projectId, recipientRoles: ['admin', 'project_manager', 'project_lead'] });
};

export const handleUpdateProjectPhase = (projectId, phaseName, phaseUpdates, contextState, contextSetters) => {
  const { projects } = contextState;
  const { setProjects } = contextSetters;
  const updatedProjects = projects.map(p => {
    if (p.id === projectId) {
      return {
        ...p,
        phases: p.phases.map(phase => phase.name === phaseName ? { ...phase, ...phaseUpdates } : phase)
      };
    }
    return p;
  });
  setProjects(updatedProjects);
  storage.set('karmod_projects', updatedProjects);
};

export const handleUpdatePhaseTask = (projectId, phaseName, taskId, taskUpdates, contextState, contextSetters) => {
  const { projects } = contextState;
  const { setProjects } = contextSetters;
  const updatedProjects = projects.map(p => {
    if (p.id === projectId) {
      return {
        ...p,
        phases: p.phases.map(phase => {
          if (phase.name === phaseName) {
            return {
              ...phase,
              tasks: phase.tasks.map(task => task.id === taskId ? { ...task, ...taskUpdates } : task)
            };
          }
          return phase;
        })
      };
    }
    return p;
  });
  setProjects(updatedProjects);
  storage.set('karmod_projects', updatedProjects);
};

export const handleAddPhaseTask = (projectId, phaseName, taskData, contextState, contextSetters) => {
  const { projects } = contextState;
  const { setProjects } = contextSetters;
  const newTask = { ...taskData, id: generateId(), progress: 0, notes: '' };
  const updatedProjects = projects.map(p => {
    if (p.id === projectId) {
      return {
        ...p,
        phases: p.phases.map(phase => {
          if (phase.name === phaseName) {
            return { ...phase, tasks: [...(phase.tasks || []), newTask] };
          }
          return phase;
        })
      };
    }
    return p;
  });
  setProjects(updatedProjects);
  storage.set('karmod_projects', updatedProjects);
};

export const handleAddDailyLog = (logData, contextState, contextSetters, addNotification) => {
  const { dailyLogs } = contextState;
  const { setDailyLogs } = contextSetters;
  const newLog = {
    ...logData, id: `DL${generateId()}`, timestamp: new Date().toISOString(), replies: [], attachments: logData.attachments || []
  };
  const updatedLogs = [newLog, ...dailyLogs];
  setDailyLogs(updatedLogs);
  storage.set('karmod_daily_logs', updatedLogs);
  addNotification({ title: 'New Daily Log', message: `Log "${newLog.title}" added for project ${newLog.projectId}.`, type: 'log', link: `/daily-log`, projectId: newLog.projectId, recipientRoles: ['admin', 'project_lead', 'team_member'] });
  return newLog;
};

export const handleAddReplyToDailyLog = (logId, replyText, parentReplyId = null, attachments = [], contextState, contextSetters) => {
  const { user, dailyLogs } = contextState;
  const { setDailyLogs } = contextSetters;
  const newReply = {
    id: `R${generateId()}`, parentId: parentReplyId, author: user.name, authorRole: user.role,
    text: replyText, timestamp: new Date().toISOString(), replies: [], attachments: attachments || []
  };
  const addReplyRecursively = (repliesArray) => {
    for (let i = 0; i < repliesArray.length; i++) {
      if (repliesArray[i].id === parentReplyId) {
        repliesArray[i].replies.unshift(newReply); return true;
      }
      if (repliesArray[i].replies && addReplyRecursively(repliesArray[i].replies)) return true;
    }
    return false;
  };
  const updatedLogs = dailyLogs.map(log => {
    if (log.id === logId) {
      const updatedLog = { ...log };
      if (parentReplyId) {
        if (!addReplyRecursively(updatedLog.replies)) updatedLog.replies.unshift(newReply);
      } else {
        updatedLog.replies.unshift(newReply);
      }
      return updatedLog;
    }
    return log;
  });
  setDailyLogs(updatedLogs);
  storage.set('karmod_daily_logs', updatedLogs);
};

export const handleDeleteDailyLogMessage = (logId, messageId, isReply = false, contextState, contextSetters) => {
  const { user, dailyLogs } = contextState;
  const { setDailyLogs } = contextSetters;
  const markAsDeletedRecursively = (repliesArray) => {
    for (let i = 0; i < repliesArray.length; i++) {
      if (repliesArray[i].id === messageId && repliesArray[i].author === user.name) {
        repliesArray[i].text = "[Message deleted by user]";
        repliesArray[i].attachments = [];
        return true;
      }
      if (repliesArray[i].replies && markAsDeletedRecursively(repliesArray[i].replies)) {
        return true;
      }
    }
    return false;
  };

  const updatedLogs = dailyLogs.map(log => {
    if (log.id === logId) {
      if (!isReply && log.author === user.name && log.id === messageId) {
        return { ...log, description: "[Message deleted by user]", attachments: [] };
      } else if (isReply) {
        const updatedLog = { ...log };
        markAsDeletedRecursively(updatedLog.replies);
        return updatedLog;
      }
    }
    return log;
  });
  setDailyLogs(updatedLogs);
  storage.set('karmod_daily_logs', updatedLogs);
};

export const handleAddMediaItem = (mediaData, contextState, contextSetters) => {
  const { media } = contextState;
  const { setMedia } = contextSetters;
  const newMedia = { ...mediaData, id: generateId(), uploadDate: new Date().toISOString() };
  const updatedMedia = [newMedia, ...media];
  setMedia(updatedMedia);
  storage.set('karmod_media', updatedMedia);
  return newMedia;
};

export const handleAddBudget = (budgetData, contextState, contextSetters, addNotification) => {
  const { user, budgets } = contextState;
  const { setBudgets } = contextSetters;
  const newBudget = {
    ...budgetData, id: `B${generateId()}`, timestamp: new Date().toISOString(),
    status: 'pending_accountant_review', pushedByAccountant: false, submittedBy: user.name,
  };
  const updatedBudgets = [...budgets, newBudget];
  setBudgets(updatedBudgets);
  storage.set('karmod_budgets', updatedBudgets);
  addNotification({ title: 'New Budget Submitted', message: `Budget for project ${newBudget.projectId} submitted.`, type: 'budget', link: `/projects/${newBudget.projectId}/budget`, projectId: newBudget.projectId, recipientRoles: ['accountant', 'admin'] });
  return newBudget;
};

export const handleUpdateBudget = (budgetId, budgetUpdates, contextState, contextSetters, addNotification) => {
  const { budgets } = contextState;
  const { setBudgets } = contextSetters;
  const updatedBudgetsList = budgets.map(b => b.id === budgetId ? { ...b, ...budgetUpdates } : b);
  setBudgets(updatedBudgetsList);
  storage.set('karmod_budgets', updatedBudgetsList);
  const targetBudget = updatedBudgetsList.find(b => b.id === budgetId);
  if (targetBudget) {
      addNotification({ title: 'Budget Status Updated', message: `Budget ${budgetId} status changed to ${targetBudget.status}.`, type: 'budget', link: `/projects/${targetBudget.projectId}/budget`, projectId: targetBudget.projectId, recipientRoles: ['admin', 'accountant', 'project_lead'] });
  }
};

export const handleAddMaterialRequest = (requestData, contextState, contextSetters, addNotification) => {
  const { user, materialRequests } = contextState;
  const { setMaterialRequests } = contextSetters;
  const newRequest = { ...requestData, id: generateId(), requestedBy: user.name, status: 'pending_lead_approval', timestamp: new Date().toISOString() };
  const updatedRequests = [...materialRequests, newRequest];
  setMaterialRequests(updatedRequests);
  storage.set('karmod_material_requests', updatedRequests);
  addNotification({ title: 'New Material Request', message: `Request for project ${newRequest.projectId} by ${user.name}.`, type: 'material', link: '/material-requests', projectId: newRequest.projectId, recipientRoles: ['project_lead', 'admin'] });
};

export const handleUpdateMaterialRequest = (requestId, updates, contextState, contextSetters, addNotification) => {
  const { materialRequests } = contextState;
  const { setMaterialRequests } = contextSetters;
  const updatedRequests = materialRequests.map(req => req.id === requestId ? { ...req, ...updates, ...(updates.status && { [`${updates.status.split('_')[0]}Timestamp`]: new Date().toISOString() }) } : req);
  setMaterialRequests(updatedRequests);
  storage.set('karmod_material_requests', updatedRequests);
  const targetReq = updatedRequests.find(r => r.id === requestId);
  if(targetReq) {
      addNotification({ title: 'Material Request Updated', message: `Request ${requestId} status changed to ${targetReq.status}.`, type: 'material', link: '/material-requests', projectId: targetReq.projectId, recipientRoles: ['admin', 'project_lead', 'store_keeper', 'team_member'], recipientUsers: [targetReq.requestedBy] });
  }
};

export const handleAddTestimonial = (testimonialData, contextState, contextSetters, addNotification) => {
  const { user, testimonials } = contextState;
  const { setTestimonials } = contextSetters;
  const newTestimonial = { ...testimonialData, id: generateId(), author: user.name, timestamp: new Date().toISOString() };
  const updatedTestimonials = [...testimonials, newTestimonial];
  setTestimonials(updatedTestimonials);
  storage.set('karmod_testimonials', updatedTestimonials);
  addNotification({ title: 'New Testimonial', message: `Testimonial received for project ${newTestimonial.projectId}.`, type: 'testimonial', link: '/testimonials', projectId: newTestimonial.projectId, recipientRoles: ['admin'] });
};

export const handleUpdateProjectFinancials = (projectId, financialData, contextState, contextSetters) => {
  const { projects } = contextState;
  const { setProjects } = contextSetters;
  const updatedProjects = projects.map(p => {
    if (p.id === projectId) {
      return { ...p, financials: { ...(p.financials || {}), ...financialData } };
    }
    return p;
  });
  setProjects(updatedProjects);
  storage.set('karmod_projects', updatedProjects);
};

export const handleAddProjectPhase = (projectId, phaseData, contextState, contextSetters, addNotification) => {
  const { projects } = contextState;
  const { setProjects } = contextSetters;
  const newPhase = { ...phaseData, id: generateId(), status: 'not_started', progress: 0, tasks: [] };
  const updatedProjects = projects.map(p => {
    if (p.id === projectId) {
      return { ...p, phases: [...(p.phases || []), newPhase] };
    }
    return p;
  });
  setProjects(updatedProjects);
  storage.set('karmod_projects', updatedProjects);
  addNotification({ title: 'New Phase Added', message: `Phase "${newPhase.name}" added to project ${projectId}.`, type: 'project', link: `/projects/${projectId}`, projectId: projectId, recipientRoles: ['admin', 'project_lead'] });
};

export const handleDeleteProjectPhase = (projectId, phaseName, contextState, contextSetters, addNotification) => {
  const { projects } = contextState;
  const { setProjects } = contextSetters;
  const updatedProjects = projects.map(p => {
    if (p.id === projectId) {
      return { ...p, phases: p.phases.filter(phase => phase.name !== phaseName) };
    }
    return p;
  });
  setProjects(updatedProjects);
  storage.set('karmod_projects', updatedProjects);
  addNotification({ title: 'Phase Deleted', message: `Phase "${phaseName}" deleted from project ${projectId}.`, type: 'project', link: `/projects/${projectId}`, projectId: projectId, recipientRoles: ['admin', 'project_lead'] });
};

export const handleAddInventoryItem = (itemData, contextState, contextSetters, addNotification) => {
  const { inventory } = contextState;
  const { setInventory } = contextSetters;
  const now = new Date().toISOString();
  const newItem = { ...itemData, id: generateId(), createdAt: now, updatedAt: now };
  const updatedInventory = [...inventory, newItem];
  setInventory(updatedInventory);
  storage.set('karmod_inventory', updatedInventory);
  addNotification({ title: 'Inventory Item Added', message: `Item "${newItem.name}" added to inventory.`, type: 'inventory', recipientRoles: ['admin', 'store_keeper'] });
};

export const handleUpdateInventoryItem = (itemId, itemUpdates, contextState, contextSetters, addNotification) => {
  const { inventory } = contextState;
  const { setInventory } = contextSetters;
  const updatedInventory = inventory.map(item =>
    item.id === itemId ? { ...item, ...itemUpdates, updatedAt: new Date().toISOString() } : item
  );
  setInventory(updatedInventory);
  storage.set('karmod_inventory', updatedInventory);
  const updatedItem = updatedInventory.find(i => i.id === itemId);
  if (updatedItem) {
    addNotification({ title: 'Inventory Item Updated', message: `Item "${updatedItem.name}" has been updated.`, type: 'inventory', recipientRoles: ['admin', 'store_keeper'] });
    if (updatedItem.quantity < updatedItem.lowStockThreshold) {
      addNotification({ title: 'Low Stock Alert', message: `Item "${updatedItem.name}" is low on stock (${updatedItem.quantity} ${updatedItem.unit} remaining).`, type: 'inventory_alert', recipientRoles: ['admin', 'store_keeper'] });
    }
  }
};

export const handleAddInventoryRequest = (requestData, contextState, contextSetters, addNotification) => {
  const { user, inventoryRequests } = contextState;
  const { setInventoryRequests } = contextSetters;
  const newRequest = { 
    ...requestData, 
    id: generateId(), 
    requestedBy: user.name, 
    status: 'pending_store_keeper_approval', 
    timestamp: new Date().toISOString() 
  };
  const updatedRequests = [...inventoryRequests, newRequest];
  setInventoryRequests(updatedRequests);
  storage.set('karmod_inventory_requests', updatedRequests);
  addNotification({ title: 'New Inventory Request', message: `Request for item "${requestData.itemName}" by ${user.name}.`, type: 'inventory_request', recipientRoles: ['store_keeper', 'admin', 'project_lead'] });
};

export const handleUpdateInventoryRequest = (requestId, newStatus, updatePayload, contextState, contextSetters, addNotification) => {
  const { inventoryRequests, inventory } = contextState;
  const { setInventoryRequests, setInventory } = contextSetters;
  
  let inventoryItemToUpdate = null;
  let quantityChange = 0;

  const updatedRequests = inventoryRequests.map(req => {
    if (req.id === requestId) {
      const originalStatus = req.status;
      const updatedReq = { 
        ...req, 
        status: newStatus, 
        ...updatePayload, 
        lastActionTimestamp: new Date().toISOString() 
      };
      
      if (originalStatus !== 'approved' && updatedReq.status === 'approved') {
        inventoryItemToUpdate = inventory.find(item => item.id === updatedReq.inventoryItemId);
        if (inventoryItemToUpdate) {
          quantityChange = -parseInt(updatedReq.quantityApproved || updatedReq.quantityRequested, 10);
        }
      }
      return updatedReq;
    }
    return req;
  });

  setInventoryRequests(updatedRequests);
  storage.set('karmod_inventory_requests', updatedRequests);

  if (inventoryItemToUpdate && quantityChange !== 0) {
    const updatedInventory = inventory.map(item =>
      item.id === inventoryItemToUpdate.id
        ? { ...item, quantity: item.quantity + quantityChange, updatedAt: new Date().toISOString() }
        : item
    );
    setInventory(updatedInventory);
    storage.set('karmod_inventory', updatedInventory);
    const finalItemState = updatedInventory.find(i => i.id === inventoryItemToUpdate.id);
    if (finalItemState && finalItemState.quantity < finalItemState.lowStockThreshold) {
       addNotification({ title: 'Low Stock Alert', message: `Item "${finalItemState.name}" is low on stock (${finalItemState.quantity} ${finalItemState.unit} remaining) after request fulfillment.`, type: 'inventory_alert', recipientRoles: ['admin', 'store_keeper'] });
    }
  }
  
  const targetReq = updatedRequests.find(r => r.id === requestId);
  if (targetReq) {
    addNotification({ title: 'Inventory Request Updated', message: `Request ${requestId} for "${targetReq.itemName}" status changed to ${targetReq.status}.`, type: 'inventory_request', recipientRoles: ['admin', 'store_keeper', 'project_lead', 'team_member'], recipientUsers: [targetReq.requestedBy] });
  }
};