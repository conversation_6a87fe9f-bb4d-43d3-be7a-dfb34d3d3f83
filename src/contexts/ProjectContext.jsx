import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/use-toast';
import apiService from '@/services/api';

const ProjectContext = createContext();

export const useProjects = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProjects must be used within a ProjectProvider');
  }
  return context;
};

export const ProjectProvider = ({ children }) => {
  const { user } = useAuth();

  const [projects, setProjects] = useState([]);
  const [dailyLogs, setDailyLogs] = useState([]);
  const [media, setMedia] = useState([]);
  const [budgets, setBudgets] = useState([]);
  const [materialRequests, setMaterialRequests] = useState([]);
  const [inventory, setInventory] = useState([]);
  const [inventoryRequests, setInventoryRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);


  // Load initial data when user is available
  useEffect(() => {
    if (user) {
      loadInitialData();
    }
  }, [user]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load projects and other data in parallel
      const [
        projectsResponse,
        dailyLogsResponse,
        mediaResponse,
        budgetsResponse,
        materialRequestsResponse,
        inventoryResponse,
        inventoryRequestsResponse
      ] = await Promise.allSettled([
        apiService.getProjects(),
        apiService.getDailyLogs(),
        apiService.getMediaFiles(),
        apiService.getBudgets(),
        apiService.getMaterialRequests(),
        apiService.getInventoryItems(),
        apiService.getInventoryRequests()
      ]);

      // Set data from successful responses
      if (projectsResponse.status === 'fulfilled') {
        setProjects(projectsResponse.value.projects || []);
      }
      if (dailyLogsResponse.status === 'fulfilled') {
        // Transform API response to match frontend expectations
        const transformedLogs = (dailyLogsResponse.value.dailyLogs || []).map(log => ({
          ...log,
          title: log.title || `${log.type.charAt(0).toUpperCase() + log.type.slice(1)} Update`, // Use database title or generate from type
          description: log.message, // Map message to description
          timestamp: log.createdAt, // Map createdAt to timestamp
          author: log.user?.name || 'Unknown User', // Map user.name to author
          authorRole: log.user?.role || 'unknown',
          replies: (log.replies || []).map(reply => ({
            ...reply,
            text: reply.message, // Map message to text for replies
            timestamp: reply.createdAt,
            author: reply.user?.name || 'Unknown User'
          }))
        }));
        setDailyLogs(transformedLogs);
      }
      if (mediaResponse.status === 'fulfilled') {
        setMedia(mediaResponse.value.mediaFiles || []);
      }
      if (budgetsResponse.status === 'fulfilled') {
        setBudgets(budgetsResponse.value.budgets || []);
      }
      if (materialRequestsResponse.status === 'fulfilled') {
        setMaterialRequests(materialRequestsResponse.value.materialRequests || []);
      }
      if (inventoryResponse.status === 'fulfilled') {
        setInventory(inventoryResponse.value.inventoryItems || []);
      }
      if (inventoryRequestsResponse.status === 'fulfilled') {
        // Transform API response to match frontend expectations
        const transformedRequests = (inventoryRequestsResponse.value.inventoryRequests || []).map(request => ({
          ...request,
          requestedBy: request.user?.name || 'Unknown User', // Map user.name to requestedBy
          itemName: request.inventoryItem?.name || 'Unknown Item', // Map inventoryItem.name to itemName
          timestamp: request.createdAt, // Map createdAt to timestamp
          // Keep original fields for compatibility
          inventoryItemId: request.inventoryItemId,
          projectId: request.projectId,
          quantityRequested: request.quantityRequested,
          notes: request.notes,
          status: request.status
        }));
        setInventoryRequests(transformedRequests);
      }

    } catch (error) {
      console.error('Error loading initial data:', error);
      setError('Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  // Project Management Functions
  const createProject = async (projectData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.createProject(projectData);
      setProjects(prev => [...prev, response.project]);

      return { success: true, project: response.project };
    } catch (error) {
      console.error('Create project error:', error);
      const errorMessage = error.message || 'Failed to create project';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const updateProject = async (projectId, projectData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateProject(projectId, projectData);
      setProjects(prev => prev.map(p => p.id === projectId ? response.project : p));

      return { success: true, project: response.project };
    } catch (error) {
      console.error('Update project error:', error);
      const errorMessage = error.message || 'Failed to update project';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const deleteProject = async (projectId) => {
    try {
      setLoading(true);
      setError(null);

      await apiService.deleteProject(projectId);
      setProjects(prev => prev.filter(p => p.id !== projectId));

      return { success: true };
    } catch (error) {
      console.error('Delete project error:', error);
      const errorMessage = error.message || 'Failed to delete project';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const getProjectById = async (projectId) => {
    // First, try to find in local state
    const localProject = projects.find(p => p.id === projectId);
    if (localProject) {
      console.log('Found project in local state:', projectId);
      return localProject;
    }

    // If projects array is empty and we haven't loaded initial data, try loading it first
    if (projects.length === 0 && !loading) {
      console.log('No projects in context, loading initial data...');
      try {
        await loadInitialData();
        // After loading, try to find the project again
        const projectAfterLoad = projects.find(p => p.id === projectId);
        if (projectAfterLoad) {
          console.log('Found project after loading initial data:', projectId);
          return projectAfterLoad;
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
        // Continue to API fetch if initial data load fails
      }
    }

    // If not found locally, try to fetch from API
    try {
      console.log('Fetching project from API:', projectId);
      const response = await apiService.getProjectById(projectId);
      console.log('API response:', response);

      // Handle different response structures
      const project = response?.project || response;

      if (project && project.id) {
        console.log('Successfully fetched project from API:', project.id);
        // Add to local state to avoid future API calls
        setProjects(prev => {
          const exists = prev.find(p => p.id === projectId);
          if (!exists) {
            return [...prev, project];
          }
          return prev;
        });
        return project;
      }
    } catch (error) {
      console.error('Error fetching project by ID:', error);
      // If it's a 404, the project doesn't exist
      if (error.message && (error.message.includes('404') || error.message.includes('Not Found'))) {
        console.log('Project not found (404):', projectId);
        return null;
      }
      // For other errors, re-throw to let the component handle it
      throw error;
    }

    console.log('Project not found anywhere:', projectId);
    return null;
  };

  // Synchronous version for backward compatibility
  const getProjectByIdSync = (projectId) => {
    return projects.find(p => p.id === projectId);
  };

  // Project Phase Functions
  const addProjectPhase = async (projectId, phaseData) => {
    try {
      const response = await apiService.addProjectPhase(projectId, phaseData);
      if (response && response.phase) {
        // Update local state
        setProjects(prev =>
          prev.map(project =>
            project.id === projectId
              ? { ...project, phases: [...(project.phases || []), response.phase] }
              : project
          )
        );

        toast({
          title: "Phase Added",
          description: `Phase "${response.phase.customLabel || response.phase.name}" has been added successfully.`
        });

        return response.phase;
      }
    } catch (error) {
      console.error('Add project phase error:', error);
      toast({
        title: "Failed to Add Phase",
        description: error.message || "Failed to add phase. Please try again.",
        variant: "destructive"
      });
      throw error;
    }
  };

  const updateProjectPhase = async (projectId, phaseId, phaseData) => {
    try {
      const response = await apiService.updateProjectPhase(projectId, phaseId, phaseData);
      if (response && response.phase) {
        // Update local state
        setProjects(prev =>
          prev.map(project =>
            project.id === projectId
              ? {
                  ...project,
                  phases: project.phases.map(phase =>
                    phase.id === phaseId ? response.phase : phase
                  )
                }
              : project
          )
        );

        toast({
          title: "Phase Updated",
          description: `Phase "${response.phase.customLabel || response.phase.name}" has been updated successfully.`
        });

        return response.phase;
      }
    } catch (error) {
      console.error('Update project phase error:', error);
      toast({
        title: "Failed to Update Phase",
        description: error.message || "Failed to update phase. Please try again.",
        variant: "destructive"
      });
      throw error;
    }
  };

  const deleteProjectPhase = async (projectId, phaseId) => {
    try {
      await apiService.deleteProjectPhase(projectId, phaseId);

      // Update local state
      setProjects(prev =>
        prev.map(project =>
          project.id === projectId
            ? {
                ...project,
                phases: project.phases.filter(phase => phase.id !== phaseId)
              }
            : project
        )
      );

      toast({
        title: "Phase Deleted",
        description: "Phase has been deleted successfully."
      });
    } catch (error) {
      console.error('Delete project phase error:', error);
      toast({
        title: "Failed to Delete Phase",
        description: error.message || "Failed to delete phase. Please try again.",
        variant: "destructive"
      });
      throw error;
    }
  };

  // Daily Log Functions
  const addDailyLog = async (logData) => {
    try {
      setLoading(true);
      setError(null);

      // Transform frontend data structure to match API expectations
      const apiData = {
        projectId: logData.projectId,
        title: logData.title,
        message: logData.description || logData.message, // Map description to message
        type: logData.type || 'update'
      };

      const response = await apiService.createDailyLog(apiData, logData.attachments || []);

      // Transform the response to match frontend expectations
      const transformedLog = {
        ...response.dailyLog,
        title: logData.title, // Use the original title from the form
        description: response.dailyLog.message,
        timestamp: response.dailyLog.createdAt,
        author: response.dailyLog.user?.name || 'Unknown User',
        authorRole: response.dailyLog.user?.role || 'unknown',
        replies: []
      };

      setDailyLogs(prev => [transformedLog, ...prev]);

      return { success: true, dailyLog: transformedLog };
    } catch (error) {
      console.error('Add daily log error:', error);
      const errorMessage = error.message || 'Failed to add daily log';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const addReplyToDailyLog = async (logId, replyText, parentReplyId = null, attachments = []) => {
    try {
      setLoading(true);
      setError(null);

      // Prepare reply data for API
      const replyData = {
        message: replyText
      };

      const response = await apiService.addReplyToDailyLog(logId, replyData, attachments || []);

      // Transform the reply response to match frontend expectations
      const transformedReply = {
        ...response.reply,
        text: response.reply.message,
        timestamp: response.reply.createdAt,
        author: response.reply.user?.name || 'Unknown User'
      };

      // Update the daily log with the new reply
      setDailyLogs(prev => prev.map(log =>
        log.id === parseInt(logId)
          ? { ...log, replies: [...(log.replies || []), transformedReply] }
          : log
      ));

      return { success: true, reply: transformedReply };
    } catch (error) {
      console.error('Add reply error:', error);
      const errorMessage = error.message || 'Failed to add reply';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const deleteDailyLogMessage = async (logId, messageId, isReply = false) => {
    try {
      setLoading(true);
      setError(null);

      if (isReply) {
        // Delete reply via API
        await apiService.deleteDailyLogReply(logId, messageId);

        // Update local state - remove the reply from the daily log
        setDailyLogs(prev => prev.map(log => {
          if (log.id === parseInt(logId)) {
            return {
              ...log,
              replies: (log.replies || []).filter(reply => reply.id !== messageId)
            };
          }
          return log;
        }));
      } else {
        // Delete entire daily log via API
        await apiService.deleteDailyLog(messageId);

        // Update local state - remove the daily log
        setDailyLogs(prev => prev.filter(log => log.id !== messageId));
      }

      return { success: true };
    } catch (error) {
      console.error('Delete daily log message error:', error);
      const errorMessage = error.message || 'Failed to delete message';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Budget Functions
  const addBudget = async (budgetData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.createBudget(budgetData);
      setBudgets(prev => [...prev, response.budget]);

      return { success: true, budget: response.budget };
    } catch (error) {
      console.error('Add budget error:', error);
      const errorMessage = error.message || 'Failed to add budget';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const updateBudget = async (budgetId, budgetData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateBudget(budgetId, budgetData);
      setBudgets(prev => prev.map(b => b.id === parseInt(budgetId) ? response.budget : b));

      return { success: true, budget: response.budget };
    } catch (error) {
      console.error('Update budget error:', error);
      const errorMessage = error.message || 'Failed to update budget';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };


  // Material Request Functions
  const addMaterialRequest = async (requestData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.createMaterialRequest(requestData);
      setMaterialRequests(prev => [...prev, response.materialRequest]);

      return { success: true, materialRequest: response.materialRequest };
    } catch (error) {
      console.error('Add material request error:', error);
      const errorMessage = error.message || 'Failed to add material request';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const updateMaterialRequest = async (requestId, requestData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateMaterialRequest(requestId, requestData);
      setMaterialRequests(prev => prev.map(r => r.id === parseInt(requestId) ? response.materialRequest : r));

      return { success: true, materialRequest: response.materialRequest };
    } catch (error) {
      console.error('Update material request error:', error);
      const errorMessage = error.message || 'Failed to update material request';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Inventory Functions
  const addInventoryItem = async (itemData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.createInventoryItem(itemData);
      setInventory(prev => [...prev, response.inventoryItem]);

      return { success: true, inventoryItem: response.inventoryItem };
    } catch (error) {
      console.error('Add inventory item error:', error);
      const errorMessage = error.message || 'Failed to add inventory item';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const updateInventoryItem = async (itemId, itemData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateInventoryItem(itemId, itemData);
      setInventory(prev => prev.map(i => i.id === parseInt(itemId) ? response.inventoryItem : i));

      return { success: true, inventoryItem: response.inventoryItem };
    } catch (error) {
      console.error('Update inventory item error:', error);
      const errorMessage = error.message || 'Failed to update inventory item';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const deleteInventoryItem = async (itemId) => {
    try {
      setLoading(true);
      setError(null);

      await apiService.deleteInventoryItem(itemId);
      setInventory(prev => prev.filter(i => i.id !== parseInt(itemId)));

      return { success: true };
    } catch (error) {
      console.error('Delete inventory item error:', error);
      const errorMessage = error.message || 'Failed to delete inventory item';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const addInventoryRequest = async (requestData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.createInventoryRequest(requestData);

      // Transform the new request to match frontend expectations
      const transformedRequest = {
        ...response.inventoryRequest,
        requestedBy: response.inventoryRequest.user?.name || 'Unknown User',
        itemName: response.inventoryRequest.inventoryItem?.name || 'Unknown Item',
        timestamp: response.inventoryRequest.createdAt,
        // Keep original fields for compatibility
        inventoryItemId: response.inventoryRequest.inventoryItemId,
        projectId: response.inventoryRequest.projectId,
        quantityRequested: response.inventoryRequest.quantityRequested,
        notes: response.inventoryRequest.notes,
        status: response.inventoryRequest.status
      };

      setInventoryRequests(prev => [...prev, transformedRequest]);

      return { success: true, inventoryRequest: transformedRequest };
    } catch (error) {
      console.error('Add inventory request error:', error);
      const errorMessage = error.message || 'Failed to add inventory request';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const updateInventoryRequest = async (requestId, requestData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateInventoryRequest(requestId, requestData);
      setInventoryRequests(prev => prev.map(r => r.id === parseInt(requestId) ? response.inventoryRequest : r));

      return { success: true, inventoryRequest: response.inventoryRequest };
    } catch (error) {
      console.error('Update inventory request error:', error);
      const errorMessage = error.message || 'Failed to update inventory request';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Media Functions
  const addMediaItem = async (file, projectId = null, description = null, onProgress = null) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.uploadMediaFile(file, projectId, description, onProgress);
      setMedia(prev => [...prev, response.mediaFile]);

      return { success: true, mediaFile: response.mediaFile };
    } catch (error) {
      console.error('Add media item error:', error);
      const errorMessage = error.message || 'Failed to upload media file';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const deleteMediaItem = async (mediaId) => {
    try {
      setLoading(true);
      setError(null);

      await apiService.deleteMediaFile(mediaId);
      setMedia(prev => prev.filter(item => item.id !== mediaId));

      return { success: true };
    } catch (error) {
      console.error('Delete media item error:', error);
      const errorMessage = error.message || 'Failed to delete media file';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Helper Functions
  const getProjectBudgets = (projectId) => budgets.filter(b => b.projectId === projectId);
  const getDailyLogsForProject = (projectId) => dailyLogs.filter(log => log.projectId === projectId);
  const getMediaForProject = (projectId) => media.filter(item => item.projectId === projectId);
  const getProjectsByStaff = (staffName) => projects.filter(p =>
    p.projectLead?.name === staffName ||
    (p.teamMembers && p.teamMembers.some(tm => tm.user?.name === staffName))
  );
  const getProjectsByClient = (clientName) => projects.filter(p => p.client?.name === clientName);

  // Notification Functions
  const addNotification = (notificationData) => {
    // For now, just log the notification
    // In a full implementation, this would send to a notification service
    console.log('Notification:', notificationData);
  };

  // Project Financials Functions
  const updateProjectFinancials = async (projectId, financialData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateProjectFinancials(projectId, financialData);

      // Update local state
      setProjects(prev => prev.map(p =>
        p.id === projectId
          ? { ...p, projectFinancials: response.projectFinancials }
          : p
      ));

      return { success: true, projectFinancials: response.projectFinancials };
    } catch (error) {
      console.error('Update project financials error:', error);
      const errorMessage = error.message || 'Failed to update project financials';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const value = {
    // Data
    projects,
    dailyLogs,
    media,
    budgets,
    materialRequests,
    inventory,
    inventoryRequests,
    loading,
    error,

    // Project Functions
    createProject,
    updateProject,
    deleteProject,
    getProjectById,
    getProjectByIdSync,
    getProjectsByStaff,
    getProjectsByClient,

    // Project Phase Functions
    addProjectPhase,
    updateProjectPhase,
    deleteProjectPhase,

    // Daily Log Functions
    addDailyLog,
    addReplyToDailyLog,
    deleteDailyLogMessage,
    getDailyLogsForProject,

    // Budget Functions
    addBudget,
    updateBudget,
    getProjectBudgets,

    // Project Financials Functions
    updateProjectFinancials,

    // Material Request Functions
    addMaterialRequest,
    updateMaterialRequest,

    // Inventory Functions
    addInventoryItem,
    updateInventoryItem,
    deleteInventoryItem,
    addInventoryRequest,
    updateInventoryRequest,

    // Media Functions
    addMediaItem,
    deleteMediaItem,
    getMediaForProject,

    // Utility Functions
    loadInitialData,
    addNotification
  };

  return <ProjectContext.Provider value={value}>{children}</ProjectContext.Provider>;
};