import { generateId } from '@/utils/storage';

export const initialSampleProjects = [
  {
    id: 'KM-2024-001',
    title: 'Lagos Office Complex',
    description: 'Modern prefabricated office building with 20 units',
    location: 'Victoria Island, Lagos',
    category: 'Commercial',
    status: 'in_progress',
    startDate: '2024-01-15',
    endDate: '2024-06-30',
    budget: 15000000, 
    spent: 8500000,
    progress: 65,
    projectLead: 'Project Lead Alpha',
    teamMembers: ['Site Engineer Bravo', '<PERSON>', '<PERSON>'],
    client: 'Client User Charlie',
    phases: [
      { name: 'Foundation', status: 'completed', progress: 100, tasks: [{id: generateId(), name: 'Clear Site', assignedTo: 'Site Engineer Bravo', progress: 100, notes: 'Site cleared and leveled.'}], customLabel: 'Foundation Works' },
      { name: 'Fabrication', status: 'in_progress', progress: 80, tasks: [{id: generateId(), name: 'Fabricate Steel Frames', assignedTo: 'Site Engineer Bravo', progress: 80, notes: 'Frame fabrication ongoing.'}], customLabel: 'Module Fabrication' },
      { name: 'Assembly', status: 'in_progress', progress: 45, tasks: [], customLabel: 'On-Site Assembly' },
      { name: 'Electrical', status: 'not_started', progress: 0, tasks: [], customLabel: 'Electrical & Plumbing' },
      { name: 'Finishing', status: 'not_started', progress: 0, tasks: [], customLabel: 'Interior Finishing' }
    ],
    financials: {
      totalProjectValue: 18000000,
      initialPayment: 9000000,
      outstandingBalance: 9000000,
      installments: [
        { amount: 4500000, dueDate: '2024-07-15', status: 'pending' },
        { amount: 4500000, dueDate: '2024-08-15', status: 'pending' }
      ]
    },
    zeptomailApiKey: null 
  },
  {
    id: 'KM-2024-002',
    title: 'Abuja Residential Units',
    description: 'Luxury modular homes for executive housing',
    location: 'Maitama, Abuja',
    category: 'Residential',
    status: 'completed',
    startDate: '2024-02-01',
    endDate: '2024-08-15',
    budget: 25000000,
    spent: 24500000,
    progress: 100,
    projectLead: 'Project Lead Echo',
    teamMembers: ['Site Engineer Foxtrot', 'Grace Okoro', 'Peter Williams'],
    client: 'Client User Golf',
    phases: [
      { name: 'Site Preparation', status: 'completed', progress: 100, tasks: [], customLabel: 'Site Prep' },
      { name: 'Fabrication', status: 'completed', progress: 100, tasks: [], customLabel: 'Unit Fabrication' },
      { name: 'Assembly', status: 'completed', progress: 100, tasks: [], customLabel: 'Unit Assembly' },
      { name: 'Utilities', status: 'completed', progress: 100, tasks: [], customLabel: 'Utilities Hookup' },
      { name: 'Landscaping', status: 'completed', progress: 100, tasks: [], customLabel: 'Landscaping & Finishing' }
    ],
    financials: {
      totalProjectValue: 30000000,
      initialPayment: 15000000,
      outstandingBalance: 0,
      installments: [
        { amount: 7500000, dueDate: '2024-09-01', status: 'paid' },
        { amount: 7500000, dueDate: '2024-10-01', status: 'paid' }
      ]
    },
    zeptomailApiKey: null
  }
];

export const initialSampleDailyLogs = [
  {
    id: 'DL001',
    projectId: 'KM-2024-001',
    title: 'Foundation Completed',
    description: 'The foundation work for the Lagos Office Complex is now 100% complete. Ready for module fabrication.',
    type: 'milestone',
    author: 'Project Lead Alpha',
    authorRole: 'project_lead',
    timestamp: '2024-02-15T10:00:00Z',
    isClientVisible: true,
    attachments: [],
    replies: [
      {
        id: 'R001',
        parentId: null,
        author: 'Client User Charlie',
        authorRole: 'client',
        text: 'Great news! Looking forward to the next phase.',
        timestamp: '2024-02-15T14:30:00Z',
        attachments: [],
        replies: [
          {
            id: 'R002',
            parentId: 'R001',
            author: 'Project Lead Alpha',
            authorRole: 'project_lead',
            text: 'Thank you! We are starting fabrication immediately.',
            timestamp: '2024-02-16T09:00:00Z',
            attachments: [],
            replies: []
          }
        ]
      }
    ]
  },
  {
    id: 'DL002',
    projectId: 'KM-2024-002',
    title: 'Site Preparation Progress',
    description: 'Site preparation for Abuja Residential Units is 75% complete. Minor delays due to weather.',
    type: 'progress',
    author: 'Project Lead Echo',
    authorRole: 'project_lead',
    timestamp: '2024-02-20T16:00:00Z',
    isClientVisible: false,
    attachments: [],
    replies: []
  }
];

export const initialSampleBudgets = [
  {
    id: 'B001', projectId: 'KM-2024-001',
    items: [ { id: 'BI001', description: 'Steel Framework', quantity: 100, unitPrice: 30000, notes: 'Heavy gauge steel', total: 3000000 }, { id: 'BI002', description: 'Insulation Panels', quantity: 200, unitPrice: 2500, notes: 'Fire retardant', total: 500000 } ],
    totalAmount: 3500000, status: 'approved_by_admin', submittedBy: 'Project Lead Alpha', pushedByAccountant: true, timestamp: '2024-01-20T10:30:00Z', remarks: 'Initial material budget approved.'
  },
  {
    id: 'B002', projectId: 'KM-2024-001',
    items: [ { id: 'BI003', description: 'Skilled Labor', quantity: 500, unitPrice: 3000, notes: 'Man-hours', total: 1500000 }, { id: 'BI004', description: 'Crane Rental', quantity: 10, unitPrice: 50000, notes: 'Days', total: 500000 } ],
    totalAmount: 2000000, status: 'pending_admin_approval', submittedBy: 'Accountant Delta', pushedByAccountant: true, timestamp: '2024-01-25T14:15:00Z', remarks: 'Labor costs for assembly phase.'
  },
];

export const initialSampleMaterialRequests = [
  { id: generateId(), projectId: 'KM-2024-001', requestedBy: 'Site Engineer Bravo', items: [{name: 'Cement Bags', quantity: 50}], notes: 'For foundation touch-ups', status: 'approved_by_lead', leadApprovalTimestamp: new Date().toISOString(), storeKeeperNotes: '', storeStatus: 'pending_delivery', storeTimestamp: null },
  { id: generateId(), projectId: 'KM-2024-001', requestedBy: 'Site Engineer Bravo', items: [{name: 'Welding Rods', quantity: 5}], notes: 'For steel frame assembly', status: 'pending_lead_approval', leadApprovalTimestamp: null, storeKeeperNotes: '', storeStatus: null, storeTimestamp: null },
];

export const initialSampleInventory = [
  { id: generateId(), name: 'Cement Bags (50kg)', category: 'Building Materials', quantity: 200, unit: 'bags', lowStockThreshold: 50, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
  { id: generateId(), name: 'Steel Rods (12mm)', category: 'Metals', quantity: 500, unit: 'lengths', lowStockThreshold: 100, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
  { id: generateId(), name: 'Paint (White Emulsion)', category: 'Finishing', quantity: 50, unit: 'gallons', lowStockThreshold: 10, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
  { id: generateId(), name: 'Safety Helmets', category: 'Safety Gear', quantity: 30, unit: 'pieces', lowStockThreshold: 5, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
];

export const initialSampleInventoryRequests = [
  { id: generateId(), inventoryItemId: initialSampleInventory[0].id, itemName: initialSampleInventory[0].name, quantityRequested: 20, projectId: 'KM-2024-001', requestedBy: 'Site Engineer Bravo', status: 'pending_store_keeper_approval', timestamp: new Date().toISOString(), notes: 'Urgent for slab work' },
  { id: generateId(), inventoryItemId: initialSampleInventory[1].id, itemName: initialSampleInventory[1].name, quantityRequested: 100, projectId: 'KM-2024-002', requestedBy: 'Site Engineer Foxtrot', status: 'approved', quantityApproved: 100, storeKeeperNotes: 'Released from Batch A', lastActionTimestamp: new Date().toISOString(), timestamp: new Date(Date.now() - 86400000).toISOString() }, // 1 day ago
];