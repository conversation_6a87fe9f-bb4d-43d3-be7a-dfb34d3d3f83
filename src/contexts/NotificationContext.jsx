import React, { createContext, useContext, useState, useEffect } from 'react';
import { storage, generateId } from '@/utils/storage';
import { useAuth } from '@/contexts/AuthContext';
import apiService from '@/services/api';

const NotificationContext = createContext();

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { user, users: allUsers } = useAuth();

  // Load notifications from API
  const loadNotifications = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getNotifications();
      if (response.notifications) {
        setNotifications(response.notifications);
        console.log(`Loaded ${response.notifications.length} notifications from API`);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
      setError('Failed to load notifications');

      // Fallback to local storage if API fails
      const savedNotifications = storage.get('karmod_notifications') || [];
      setNotifications(savedNotifications);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadNotifications();
  }, [user]);

  // Refresh notifications periodically
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(() => {
      loadNotifications();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [user]);

  const addNotification = async (notificationData) => {
    try {
      // For now, keep the local-only approach for adding notifications
      // since the backend notification creation is handled by the server-side services
      const newNotification = {
        id: generateId(),
        ...notificationData,
        timestamp: new Date().toISOString(),
        read: false,
        recipientRoles: notificationData.recipientRoles || ['admin'],
        recipientUsers: notificationData.recipientUsers || [],
        projectId: notificationData.projectId,
      };

      if (process.env.NODE_ENV === 'production' && notificationData.sendEmail) {
        console.log("Simulating ZeptoMail send:", newNotification);
      }

      const updatedNotifications = [newNotification, ...notifications];
      setNotifications(updatedNotifications);
      storage.set('karmod_notifications', updatedNotifications);
    } catch (error) {
      console.error('Error adding notification:', error);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      // Update local state immediately for better UX
      const updatedNotifications = notifications.map(n =>
        n.id === notificationId ? { ...n, read: true, isRead: true } : n
      );
      setNotifications(updatedNotifications);

      // Try to update via API
      try {
        await apiService.markNotificationAsRead(notificationId);
      } catch (apiError) {
        console.error('Failed to mark notification as read via API:', apiError);
        // Keep local change even if API fails
      }

      // Also update local storage as fallback
      storage.set('karmod_notifications', updatedNotifications);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      // Update local state immediately for better UX
      const updatedNotifications = notifications.map(n => ({ ...n, read: true, isRead: true }));
      setNotifications(updatedNotifications);

      // Try to update via API
      try {
        await apiService.markAllNotificationsAsRead();
      } catch (apiError) {
        console.error('Failed to mark all notifications as read via API:', apiError);
        // Keep local change even if API fails
      }

      // Also update local storage as fallback
      storage.set('karmod_notifications', updatedNotifications);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // API notifications are already filtered by userId on the backend
  // No need for complex filtering logic here
  const getFilteredNotifications = () => {
    if (!user) return [];

    // Sort notifications by creation date (newest first)
    return notifications.sort((a, b) => {
      const dateA = new Date(a.timestamp || a.createdAt);
      const dateB = new Date(b.timestamp || b.createdAt);
      return dateB - dateA;
    });
  };

  const filteredNotifications = getFilteredNotifications();
  // Handle both API format (isRead) and local format (read)
  const unreadCount = filteredNotifications.filter(n => !(n.read || n.isRead)).length;

  const value = {
    notifications: filteredNotifications,
    addNotification,
    markAsRead,
    markAllAsRead,
    unreadCount,
    loading,
    error,
    loadNotifications, // Expose this for manual refresh
    refreshNotifications: loadNotifications, // Alias for clarity
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};