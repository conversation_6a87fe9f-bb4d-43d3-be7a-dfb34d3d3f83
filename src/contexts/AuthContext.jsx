
import React, { createContext, useContext, useState, useEffect } from 'react';
import apiService from '@/services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);



  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = apiService.getToken();
        if (token) {
          // Verify token and get current user
          const response = await apiService.getCurrentUser();
          setUser(response.user);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        // Token is invalid, clear it
        apiService.setToken(null);
        setError('Session expired. Please login again.');
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.login(email, password);
      setUser(response.user);

      return { success: true, user: response.user };
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error.message || 'Login failed. Please try again.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setError(null);
    }
  };
  
  const updateUser = async (userId, updatedData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateUser(userId, updatedData);

      // Update users list
      setUsers(prevUsers =>
        prevUsers.map(u => u.id === userId ? response.user : u)
      );

      // Update current user if it's the same user
      if (user && user.id === userId) {
        setUser(response.user);
      }

      return { success: true, user: response.user };
    } catch (error) {
      console.error('Update user error:', error);
      const errorMessage = error.message || 'Failed to update user.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const getAllStaff = async () => {
    try {
      const response = await apiService.getStaffUsers();
      return response.users || [];
    } catch (error) {
      console.error('Get staff error:', error);
      return [];
    }
  };

  const getAllClients = async () => {
    try {
      const response = await apiService.getClientUsers();
      return response.users || [];
    } catch (error) {
      console.error('Get clients error:', error);
      return [];
    }
  };

  const createUser = async (userData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.createUser(userData);

      // Add to users list
      setUsers(prevUsers => [...prevUsers, response.user]);

      return { success: true, user: response.user };
    } catch (error) {
      console.error('Create user error:', error);
      const errorMessage = error.message || 'Failed to create user.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const deleteUser = async (userId) => {
    try {
      setLoading(true);
      setError(null);

      await apiService.deleteUser(userId);

      // Remove from users list
      setUsers(prevUsers => prevUsers.filter(u => u.id !== userId));

      return { success: true };
    } catch (error) {
      console.error('Delete user error:', error);
      const errorMessage = error.message || 'Failed to delete user.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const getUserById = async (userId) => {
    try {
      const response = await apiService.getUserById(userId);
      return response.user;
    } catch (error) {
      console.error('Get user by ID error:', error);
      return null;
    }
  };

  const refreshUsers = async () => {
    try {
      const response = await apiService.getUsers();
      setUsers(response.users || []);
    } catch (error) {
      console.error('Refresh users error:', error);
    }
  };

  const value = {
    user,
    users,
    loading,
    error,
    login,
    logout,
    updateUser,
    createUser,
    deleteUser,
    getAllStaff,
    getAllClients,
    getUserById,
    refreshUsers
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
