import { useState, useCallback } from 'react';

/**
 * Custom hook for managing async operations with loading and error states
 */
export const useAsyncOperation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const execute = useCallback(async (asyncFunction, ...args) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await asyncFunction(...args);
      return result;
    } catch (err) {
      console.error('Async operation error:', err);
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
  }, []);

  return {
    loading,
    error,
    execute,
    clearError,
    reset
  };
};

/**
 * Hook for managing multiple async operations
 */
export const useAsyncOperations = () => {
  const [operations, setOperations] = useState({});

  const execute = useCallback(async (operationKey, asyncFunction, ...args) => {
    try {
      setOperations(prev => ({
        ...prev,
        [operationKey]: { loading: true, error: null }
      }));
      
      const result = await asyncFunction(...args);
      
      setOperations(prev => ({
        ...prev,
        [operationKey]: { loading: false, error: null }
      }));
      
      return result;
    } catch (err) {
      console.error(`Async operation error [${operationKey}]:`, err);
      const errorMessage = err.message || 'An unexpected error occurred';
      
      setOperations(prev => ({
        ...prev,
        [operationKey]: { loading: false, error: errorMessage }
      }));
      
      throw err;
    }
  }, []);

  const clearError = useCallback((operationKey) => {
    setOperations(prev => ({
      ...prev,
      [operationKey]: { ...prev[operationKey], error: null }
    }));
  }, []);

  const reset = useCallback((operationKey) => {
    setOperations(prev => {
      const newOperations = { ...prev };
      delete newOperations[operationKey];
      return newOperations;
    });
  }, []);

  const getOperation = useCallback((operationKey) => {
    return operations[operationKey] || { loading: false, error: null };
  }, [operations]);

  const isAnyLoading = Object.values(operations).some(op => op.loading);
  const hasAnyError = Object.values(operations).some(op => op.error);

  return {
    operations,
    execute,
    clearError,
    reset,
    getOperation,
    isAnyLoading,
    hasAnyError
  };
};
