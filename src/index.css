@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html,
  body,
  #root {
    height: 100%;
    overflow: hidden; /* Prevent body from scrolling */
  }
}

:root {
  --background: 220 20% 97%;
  --foreground: 220 20% 10%;
  --card: 0 0% 100%;
  --card-foreground: 220 20% 10%;
  --popover: 0 0% 100%;
  --popover-foreground: 220 20% 10%;
  --primary: 221 83% 53%;
  --primary-foreground: 210 40% 98%;
  --secondary: 220 14% 96%;
  --secondary-foreground: 220 9% 46%;
  --muted: 220 14% 96%;
  --muted-foreground: 220 9% 46%;
  --accent: 220 14% 96%;
  --accent-foreground: 220 9% 46%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 221 83% 53%;
  --radius: 0.5rem;
}

.dark {
  --background: 220 20% 8%;
  --foreground: 210 40% 98%;
  --card: 220 20% 10%;
  --card-foreground: 210 40% 98%;
  --popover: 220 20% 10%;
  --popover-foreground: 210 40% 98%;
  --primary: 221 83% 53%;
  --primary-foreground: 210 40% 98%;
  --secondary: 220 14% 14%;
  --secondary-foreground: 210 40% 98%;
  --muted: 220 14% 14%;
  --muted-foreground: 217 10% 64%;
  --accent: 220 14% 14%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62% 30%;
  --destructive-foreground: 210 40% 98%;
  --border: 220 13% 18%;
  --input: 220 13% 18%;
  --ring: 221 83% 53%;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.95), rgba(59, 130, 246, 0.95));
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .main-content {
    padding-bottom: 80px;
  }
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Utility to hide scrollbar */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-primary {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.gradient-danger {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Status indicators */
.status-not-started {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

.status-in-progress {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
}

.status-on-hold {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.status-completed {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

/* PWA styles */
.pwa-install {
  position: fixed;
  bottom: 100px;
  right: 20px;
  z-index: 1000;
}

@media (min-width: 768px) {
  .pwa-install {
    bottom: 20px;
  }
}

/* Compact mode styles */
.compact .p-4 {
  padding: 0.75rem;
}

.compact .p-6 {
  padding: 1rem;
}

.compact .space-y-6 > * + * {
  margin-top: 1rem;
}

.compact .space-y-4 > * + * {
  margin-top: 0.75rem;
}

.compact .text-3xl {
  font-size: 1.75rem;
  line-height: 2rem;
}

.compact .text-xl {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.compact .h-10 {
  height: 2rem;
}

.compact .h-12 {
  height: 2.5rem;
}